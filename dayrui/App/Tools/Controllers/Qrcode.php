<?php namespace Phpcmf\Controllers;

class Qrcode extends \Phpcmf\App
{

    public function index() {

        exit();
    }
    public function build(){
        // 引入第三方扩展库
        require_once APPSPATH.ucfirst(APP_DIR).'/Vendor/endroid/qr-code/src/Exceptions/DataDoesntExistsException.php';
        require_once APPSPATH.ucfirst(APP_DIR).'/Vendor/endroid/qr-code/src/QrCode.php';
        $text = \Phpcmf\Service::L('input')->get('text');
        $text=base64_decode($text);
        $size =isset($info['size'])? $info['size'] : 150;
        $padding =isset($info['padding'])? $info['padding'] : 15;
        $filename = md5($text).".png";
        $bannerurl = 'uploadfile/ewm/'.$size.'_'.$padding;
        if(!is_dir($bannerurl)){
            mkdir($bannerurl, 0777, true);
        }
        $bannerurl = WEBPATH.$bannerurl."/".$filename;
        if(file_exists($bannerurl)){
            $this->readImg($bannerurl);
        }
        $errorcorrection = \Phpcmf\Service::L('input')->get('errorcorrection'); // 容错级别
        empty($errorcorrection) && $errorcorrection = 'high';
        $foreground = \Phpcmf\Service::L('input')->get('foreground');
        empty($foreground) && $foreground = '#ffffff';
        $background = \Phpcmf\Service::L('input')->get('background');
        empty($background) && $background = '#000000';
        $logo = intval(\Phpcmf\Service::L('input')->get('logo')); // 是否显示中间logo
        empty($logo) && $logo = 0;
        $logosize = intval(\Phpcmf\Service::L('input')->get('logosize'));
        empty($logosize) && $logosize = 50;
        $label = \Phpcmf\Service::L('input')->get('label'); // 标签文案，在二维码下方
        empty($label) && $label = '';
        $labelfontsize = intval(\Phpcmf\Service::L('input')->get('labelfontsize')); // 标签大小
        empty($labelfontsize) && $labelfontsize = 14;
        $labelhalign = intval(\Phpcmf\Service::L('input')->get('labelhalign')); // 标签水平位置
        empty($labelhalign) && $labelhalign = 0;
        $labelvalign = intval(\Phpcmf\Service::L('input')->get('labelvalign')); // 标签垂直位置
        empty($labelvalign) && $labelvalign = 3;

        // 前景色
        list($r, $g, $b) = sscanf($foreground, "#%02x%02x%02x");
        $foregroundcolor = ['r' => $r, 'g' => $g, 'b' => $b];

        // 背景色
        list($r, $g, $b) = sscanf($background, "#%02x%02x%02x");
        $backgroundcolor = ['r' => $r, 'g' => $g, 'b' => $b];

        $qrCode = new \Endroid\QrCode\QrCode;
        $qrCode->setText($text)
            ->setSize($size)
            ->setPadding($padding)
            ->setErrorCorrection($errorcorrection)
            ->setForegroundColor($foregroundcolor)
            ->setBackgroundColor($backgroundcolor)
            ->setLogoSize($logosize)
            ->setLabelFontPath(APPSPATH.APP_DIR.'/fonts/fzltxh.ttf')
            ->setLabel($label)
            ->setLabelFontSize($labelfontsize)
            ->setLabelHalign($labelhalign)
            ->setLabelValign($labelvalign)
            ->setImageType(\Endroid\QrCode\QrCode::IMAGE_TYPE_PNG);
         if (!empty($logo)) {
             if(is_numeric($logo)){
                 $logo = dr_get_file($logo);
             }
              if(strpos($logo, '/')===0){
                  $logo = substr($logo, 1);
              }
              $qrCode->setLogo($logo);
          }
        //也可以直接使用render方法输出结果
        $imgcode = $qrCode->get('png');

        file_put_contents($bannerurl, $imgcode);
        $this->readImg($bannerurl);

    }
    private function readImg($path){
        // 获取文件扩展名
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        // 设置对应的 MIME 类型
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                $mime = 'image/jpeg';
                break;
            case 'png':
                $mime = 'image/png';
                break;
            case 'gif':
                $mime = 'image/gif';
                break;
            // 添加其他可能的图片格式
            default:
                $mime = 'image/jpeg'; // 默认为 JPEG
                break;
        }
        // 设置 HTTP 头部，告诉浏览器返回的是图片
        header('Content-Type: ' . $mime);

        // 读取图片文件并输出
        readfile($path);
        exit();
    }


}
