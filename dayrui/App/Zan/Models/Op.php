<?php namespace Phpcmf\Model\Zan;

// 自动执行方法
class Op extends \Phpcmf\Model
{

    public function run($tablename, $dirname) {

        if (!dr_in_array('support', \Phpcmf\Service::M('table')->get_cache_field($tablename)) ) {
            return dr_return_data(0, dr_lang('应用[模块内容点赞]未安装到本模块[%s]', $dirname));
        } elseif (!dr_in_array('oppose', \Phpcmf\Service::M('table')->get_cache_field($tablename)) ) {
            return dr_return_data(0, dr_lang('应用[模块内容点赞]未安装到本模块[%s]', $dirname));
        }

        $id = (int)\Phpcmf\Service::L('input')->get('id');
        if (!$id) {
            return dr_return_data(0, dr_lang('id参数不完整'));
        }

        $value = (int)\Phpcmf\Service::L('input')->get('value');
        $data = $this->db->table($tablename.'_index')->where('id', $id)->countAllResults();
        if (!$data) {
            return dr_return_data(0, dr_lang('模块内容不存在'));
        }

        $field = $value ? 'support' : 'oppose';
        $table = $tablename.'_'.$field;
        if (!$this->db->tableExists($table)) {
            return dr_return_data(0, dr_lang('应用[模块内容点赞]未安装到本模块[%s]', $dirname));
        }

        $ip = \Phpcmf\Service::L('input')->ip_address();
        $agent = md5(\Phpcmf\Service::L('input')->get_user_agent().$ip);
        if (!$this->uid) {
            $result = $this->db->table($table)->where('cid', $id)->where('uid', $this->uid)->where('agent', $agent)->get()->getRowArray();
        } else {
            $result = $this->db->table($table)->where('cid', $id)->where('uid', $this->uid)->get()->getRowArray();
        }

        if (!\Phpcmf\Service::M()->db->fieldExists('inputtime', $table)) {
            \Phpcmf\Service::M()->query('ALTER TABLE `'.\Phpcmf\Service::M()->dbprefix($table).'` ADD `inputtime` int(10) unsigned DEFAULT \'0\' COMMENT \'操作时间\';');
        }
        if (!\Phpcmf\Service::M()->db->fieldExists('inputip', $table)) {
            \Phpcmf\Service::M()->query('ALTER TABLE `'.\Phpcmf\Service::M()->dbprefix($table).'` ADD `inputip` varchar(100) DEFAULT \'\' COMMENT \'IP地址\';');
        }

        $save = array(
            'cid' => $id,
            'uid' => $this->uid,
            'agent' => $agent,
            'inputip' => $ip,
            'inputtime' => SYS_TIME,
        );

        $msg = '';
        $config = \Phpcmf\Service::M('app')->get_config('zan');
        if (isset($config['dian']) && $config['dian'] == 1) {
            // 一直可以点击
            if (isset($config['day_dian']) && $config['day_dian']) {
                // 每天每个IP点击限制
                $c = $this->db->table($table)
                    //->where('date_format(FROM_UNIXTIME(inputtime),"%Y-%m-%d") = date_format(DATE_ADD(NOW(),INTERVAL -'.intval($config['day']).' DAY),"%Y-%m-%d")')
                    ->where('DATEDIFF(from_unixtime(inputtime),now())=0')
                    ->where('cid', $id)
                    ->where('inputip', $ip)
                    ->countAllResults();
                if ($c >= $config['day_dian']) {
                    $msg = '今天操作次数达到上限';
                } else {
                    $msg = dr_lang('操作成功');
                    $this->db->table($table)->insert($save);
                }
            }
            if (!$msg && isset($config['day_dian2']) && $config['day_dian2']) {
                // 每个IP总共点击
                $c = $this->db->table($table)
                    //->where('date_format(FROM_UNIXTIME(inputtime),"%Y-%m-%d") = date_format(DATE_ADD(NOW(),INTERVAL -'.intval($config['day']).' DAY),"%Y-%m-%d")')
                    ->where('DATEDIFF(from_unixtime(inputtime),now())=0')
                    ->where('cid', $id)
                    ->countAllResults();
                if ($c >= $config['day_dian2']) {
                    $msg = '今天操作总次数达到上限';
                } else {
                    $msg = dr_lang('操作成功');
                    $this->db->table($table)->insert($save);
                }
            }

        } elseif (isset($config['dian']) && $config['dian'] == 2) {
            // 不计算
            if ($result) {
                // 已经操作了
                $msg = dr_lang('请勿重复操作');
            } else {
                $this->db->table($table)->insert($save);
                $msg = dr_lang('操作成功');
            }
        } else {
            // 取消上次点击
            if ($result) {
                // 已经操作了,我们就删除它
                $this->db->table($table)->where('id', intval($result['id']))->delete();
                $msg = dr_lang('操作取消');
            } else {
                $this->db->table($table)->insert($save);
                $msg = dr_lang('操作成功');
            }
        }

        // 更新数量
        $c = $this->db->table($table)->where('cid', $id)->countAllResults();
        $this->db->table($tablename)->where('id', $id)->set($field, $c)->update();
        \Phpcmf\Service::M('cache')->update_data_cache();

        // 返回结果
        return dr_return_data(1, $msg, $c);
    }


}