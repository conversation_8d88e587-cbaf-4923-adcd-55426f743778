{template "header.html"}
<div class="note note-danger">
    <p><a href="javascript:dr_update_cache();">{dr_lang('更改配置之后需要更新缓存之后才能生效')}</a></p>
</div>
<form class="form-horizontal" role="form" id="myform">
    {dr_form_hidden()}
    <div class="table-scrollable">
        <table class="table table-striped table-bordered table-hover table-checkable dataTable">
            <thead>
            <tr class="heading">
                <th width="50"> </th>
                <th width="200"> {dr_lang('名称')} / {dr_lang('目录')}</th>
                <th> {dr_lang('操作')} </th>
            </tr>
            </thead>
            <tbody>
            {loop $data $i $t}
            <tr class="odd gradeX">

                <td>{$i+1}</td>
                <td><i class="{$t.icon}"></i> {$t.name} / {$t.dirname}</td>
                <td>
                    {if $ci->_is_admin_auth()}
                        {if $t.is_field}
                        <label><a href="javascript:dr_iframe_show('code', '{dr_url(APP_DIR.'/home/<USER>', ['dir'=>$t.dirname])}', '80%', '50%');" class="btn btn-xs dark"> <i class="fa fa-code"></i> {dr_lang('内容页调用代码')}</a></label>
                        <label><a href="javascript:dr_iframe_show('code', '{dr_url(APP_DIR.'/home/<USER>', ['dir'=>$t.dirname])}', '80%', '50%');" class="btn btn-xs yellow"> <i class="fa fa-code"></i> {dr_lang('列表页调用代码')}</a></label>
                        <label><a href="javascript:dr_load_ajax('{dr_lang('确定将此模块重新安装到本插件中吗？')}', '{dr_url(APP_DIR.'/home/<USER>', ['dir'=>$t.dirname])}', 1);" class="btn btn-xs green"> <i class="fa fa-plus"></i> {dr_lang('重装')} </a></label>
                        <label><a href="javascript:dr_load_ajax('{dr_lang('确定将此模块从本插件中删除吗？')}', '{dr_url(APP_DIR.'/home/<USER>', ['dir'=>$t.dirname])}', 1);" class="btn btn-xs red"> <i class="fa fa-trash"></i> {dr_lang('卸载')} </a></label>
                        {else}
                        <label><a href="javascript:dr_load_ajax('{dr_lang('确定将此模块安装到本插件中吗？')}', '{dr_url(APP_DIR.'/home/<USER>', ['dir'=>$t.dirname])}', 1);" class="btn btn-xs blue"> <i class="fa fa-plus"></i> {dr_lang('安装')} </a></label>
                        {/if}
                    {/if}
                </td>
            </tr>
            {/loop}
            </tbody>
        </table>
    </div>


</form>


{template "footer.html"}