{template "header.html"}

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="portlet bordered light myfbody">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-cog"></i> {dr_lang('插件设置')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">
                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="col-md-2 control-label">二次点击</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input onclick="rr(this.value)" type="radio" name="data[dian]" value="1" {if $data.dian ==1}checked=""{/if}> 一直可以点击 <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input onclick="rr(this.value)" type="radio" name="data[dian]" value="2" {if $data.dian ==2}checked=""{/if}> 不计算 <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input onclick="rr(this.value)" type="radio" name="data[dian]" value="0" {if !$data.dian}checked=""{/if}> 取消上次点击 <span></span></label>
                                </div>
                                <span class="help-block">第一次点击成功后，第二次点击的操作行为</span>
                            </div>
                        </div>

                        <div class="form-group r1">
                            <label class="col-md-2 control-label">点击权限</label>
                            <div class="col-md-9">
                                <div class="input-inline input-small hidden">
                                    <div class="input-group">
                                        <input type="text" name="data[day]" value="{intval($data['day'])}" class="form-control">
                                        <span class="input-group-addon">天内</span>
                                    </div>
                                </div>
                                <label>每天每个IP点击</label>
                                <div class="input-inline input-small">
                                    <div class="input-group">
                                        <input type="text" name="data[day_dian]" value="{intval($data['day_dian'])}" class="form-control">
                                        <span class="input-group-addon">次</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group r1">
                            <label class="col-md-2 control-label">&nbsp;</label>
                            <div class="col-md-9">
                                <div class="input-inline input-small hidden">
                                    <div class="input-group">
                                        <input type="text" name="data[day2]" value="{intval($data['day2'])}" class="form-control">
                                        <span class="input-group-addon">天内</span>
                                    </div>
                                </div>
                                <label>每个IP总共点击</label>
                                <div class="input-inline input-small">
                                    <div class="input-group">
                                        <input type="text" name="data[day2_dian]" value="{intval($data['day2_dian'])}" class="form-control">
                                        <span class="input-group-addon">次</span>
                                    </div>
                                </div>
                                <span class="help-block">以上设置0表示不限制</span>
                            </div>
                        </div>



                    </div>
                </div>
            </div>
        </div>

        <div class="portlet-body form myfooter">
            <div class="form-actions text-center">
                <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn green"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
            </div>
        </div>
    </div>
</form>

<script>
    function rr(id) {
        $('.r1').hide();
        $('.r'+id).show();
    }
    $(function () {
        rr({intval($data.dian)});
    });
</script>

{template "footer.html"}