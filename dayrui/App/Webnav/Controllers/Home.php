<?php
namespace Phpcmf\Controllers;

/**
 * 二次开发时可以修改本文件，不影响升级覆盖
 */

class Home extends \Phpcmf\Home\Module
{

	public function index() {
		$this->_Index();
	}
    public function img(){
//        /index.php?s=webnav&c=home&m=img
        @ini_set("memory_limit",'-1');
        ini_set('max_execution_time', '0');
//加上一次取2条
        $lists = \Phpcmf\Service::M()->table('fhx_1_webnav')->select('id,thumb')->where('thumb like "//%"')->limit(5)->getAll();
        if(empty($lists)){
            exit('没有数据了');
        }
        foreach ($lists as $v){
            // 下载远程文件
            $rt = $this->downImg('http:'.$v['thumb']);
            if($rt){
                $rt['attachment'] = \Phpcmf\Service::M('Attachment')->get_attach_info();
                $rt = \Phpcmf\Service::L('upload')->down_file($rt);
                if ($rt['code']) {
                    $att = \Phpcmf\Service::M('Attachment')->save_data($rt['data']);
                    if ($att['code']) {
                        // 归档成功
var_dump('更新id为'.$v['id'].'的数据');
                        \Phpcmf\Service::M()->table('fhx_1_webnav')->update($v['id'],['thumb'=>$att['code']]);
                    }

                }

            }else{
                exit('http:'.$v['thumb'].'下载失败');
            }

        }
//        刷新当前页面
        dr_redirect(\Phpcmf\Service::L('Router')->url('webnav/home/<USER>'));
    }
    
    // 采集URL内容
    public function fetch_url() {
        $url = \Phpcmf\Service::L('input')->get('url');
        if (!$url) {
            $this->_json(0, '请输入要采集的网址');
        }
        
        $source = \Phpcmf\Service::L('input')->get('source');
        
        // AI工具集采集源
        if ($source == 'aibot') {
            return $this->fetch_url_aibot($url);
        }
        
        // 默认采集源处理
        // 初始化cURL会话
        $ch = curl_init();
        
        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        // 执行cURL请求
        $html = curl_exec($ch);
        
        // 检查是否有错误发生
        if (curl_errno($ch)) {
            $this->_json(0, '采集失败: ' . curl_error($ch));
        }
        
        // 关闭cURL会话
        curl_close($ch);
        
        // 解析HTML
        $title = '';
        $description = '';
        $keywords = '';
        $icon = '';
        $content = '';
        
        // 提取标题
        if (preg_match('/<title>(.*?)<\/title>/is', $html, $matches)) {
            $title = trim($matches[1]);
        }
        
        // 提取描述
        if (preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
            $description = trim($matches[1]);
        }
        
        // 提取关键词
        if (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
            $keywords = trim($matches[1]);
        }
        
        // 提取网站图标
        if (preg_match('/<link\s+[^>]*rel=["\'](?:shortcut )?icon["\']\s+[^>]*href=["\']([^"\']+)["\']/is', $html, $matches)) {
            $icon = trim($matches[1]);
            
            // 处理相对路径
            if (strpos($icon, 'http') !== 0) {
                $parsed_url = parse_url($url);
                $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'];
                
                if (strpos($icon, '/') === 0) {
                    $icon = $base_url . $icon;
                } else {
                    $path = isset($parsed_url['path']) ? dirname($parsed_url['path']) : '';
                    $icon = $base_url . $path . '/' . $icon;
                }
            }
        }
        
        // 提取内容
        if (preg_match('/<body[^>]*>(.*?)<\/body>/is', $html, $matches)) {
            $body = $matches[1];
            
            // 尝试提取主要内容区域
            if (preg_match('/<article[^>]*>(.*?)<\/article>/is', $body, $article_matches)) {
                $content = $article_matches[1];
            } elseif (preg_match('/<div\s+class=["\']content["\'][^>]*>(.*?)<\/div>/is', $body, $content_matches)) {
                $content = $content_matches[1];
            } elseif (preg_match('/<div\s+id=["\']content["\'][^>]*>(.*?)<\/div>/is', $body, $content_matches)) {
                $content = $content_matches[1];
            }
            
            // 清理内容
            $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/is', '', $content);
            $content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/is', '', $content);
            $content = preg_replace('/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/is', '', $content);
            $content = preg_replace('/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/is', '', $content);
            $content = preg_replace('/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/is', '', $content);
        }
        
        // 返回结果
        $this->_json(1, '采集成功', [
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'icon' => $icon,
            'url' => $url,
            'content' => $content
        ]);
    }
    
    // AI工具集采集源处理
    private function fetch_url_aibot($url) {
        // 初始化cURL会话
        $ch = curl_init();
        
        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        // 执行cURL请求
        $html = curl_exec($ch);
        
        // 检查是否有错误发生
        if (curl_errno($ch)) {
            $this->_json(0, '采集失败: ' . curl_error($ch));
        }
        
        // 关闭cURL会话
        curl_close($ch);
        
        // 解析HTML
        $title = '';
        $description = '';
        $keywords = '';
        $icon = '';
        $thumb = '';
        $content = '';
        $price_info = '';
        $official_url = '';
        $category = '';
        $features = [];
        $usage_scenarios = [];
        
        // 提取标题 - 从site-name类
        if (preg_match('/<h1 class="site-name[^"]*">(.*?)<\/h1>/is', $html, $matches)) {
            $title = trim(strip_tags($matches[1]));
        } elseif (preg_match('/<title>(.*?)<\/title>/is', $html, $matches)) {
            $title = trim($matches[1]);
            // 移除网站名称部分
            $title = preg_replace('/\s*\|\s*AI工具集$/', '', $title);
        }
        
        // 提取描述 - 从meta description或页面内容
        if (preg_match('/<p class="mb-2">(.*?)<\/p>/is', $html, $matches)) {
            $description = trim(strip_tags($matches[1]));
        } elseif (preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
            $description = trim($matches[1]);
        }
        
        // 提取分类 - 根据您提供的HTML结构
        if (preg_match('/<a\s+class=[\'"]btn-cat[^\'"]*[\'"][^>]*href=[\'"][^\'"]*(\/favorites\/[^\'"]*)[\'"]/is', $html, $matches)) {
            $category_url = $matches[1];
            if (preg_match('/\/favorites\/([^\/]+)/', $category_url, $cat_matches)) {
                $category_slug = $cat_matches[1];
                
                // 提取分类名称
                if (preg_match('/<a\s+class=[\'"]btn-cat[^\'"]*[\'"][^>]*>(.*?)<\/a>/is', $html, $cat_name_matches)) {
                    $category = trim(strip_tags($cat_name_matches[1]));
                } else {
                    // 如果无法直接提取，尝试将slug转换为可读名称
                    $category = str_replace('-', ' ', $category_slug);
                    $category = ucwords($category);
                }
            }
        }
        
        // 提取关键词/标签 - 根据您提供的HTML结构
        $tags = array();
        if (preg_match_all('/<span class="mr-2"><a[^>]*rel="tag"[^>]*>(.*?)<\/a>/is', $html, $matches)) {
            foreach($matches[1] as $tag) {
                $tags[] = trim(strip_tags($tag));
            }
            $keywords = implode(',', $tags);
        } elseif (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
            $keywords = trim($matches[1]);
        }
        
        // 提取图标 - 从og:image或其他图像元素
        if (preg_match('/<meta\s+property=["\']og:image["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
            $icon = trim($matches[1]);
        } elseif (preg_match('/<link[^>]*rel=["\']icon["\']\s+href=["\']([^"\']+)["\']/is', $html, $matches)) {
            $icon = trim($matches[1]);
        }
        
        // 提取缩略图 - 从图片元素
        if (preg_match('/<img[^>]*class="[^"]*img-cover[^"]*"[^>]*data-src="([^"]+)"/is', $html, $matches)) {
            $thumb = trim($matches[1]);
        } elseif (preg_match('/<img[^>]*class="[^"]*img-cover[^"]*"[^>]*src="([^"]+)"/is', $html, $matches)) {
            $thumb = trim($matches[1]);
        }
        
        // 提取内容 - 从面板正文
        if (preg_match('/<div[^>]*class="[^"]*panel-body[^"]*"[^>]*>(.*?)<\/div>/is', $html, $matches)) {
            $content = trim($matches[1]);
            // 清理HTML标签，但保留基本格式
            $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/is', '', $content);
            $content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/is', '', $content);
        }
        
        // 提取功能特点 - 从features部分
        if (preg_match('/<h2[^>]*>特点功能<\/h2>(.*?)<h2/is', $html, $matches)) {
            $features_html = $matches[1];
            if (preg_match_all('/<li>(.*?)<\/li>/is', $features_html, $li_matches)) {
                foreach ($li_matches[1] as $feature) {
                    $features[] = trim(strip_tags($feature));
                }
            }
        }
        
        // 提取使用场景 - 从scenarios部分
        if (preg_match('/<h2[^>]*>应用场景<\/h2>(.*?)<h2/is', $html, $matches)) {
            $scenarios_html = $matches[1];
            if (preg_match_all('/<li>(.*?)<\/li>/is', $scenarios_html, $li_matches)) {
                foreach ($li_matches[1] as $scenario) {
                    $usage_scenarios[] = trim(strip_tags($scenario));
                }
            }
        } elseif (preg_match('/<h2[^>]*>应用场景<\/h2>(.*?)$/is', $html, $matches)) {
            // 如果是最后一个部分，没有后续的h2
            $scenarios_html = $matches[1];
            if (preg_match_all('/<li>(.*?)<\/li>/is', $scenarios_html, $li_matches)) {
                foreach ($li_matches[1] as $scenario) {
                    $usage_scenarios[] = trim(strip_tags($scenario));
                }
            }
        }
        
        // 提取价格信息 - 从country-piece类
        if (preg_match('/<div[^>]*class="[^"]*country-piece[^"]*"[^>]*>(.*?)<\/div>/is', $html, $matches)) {
            $price_info = trim(strip_tags($matches[1]));
        }
        
        // 提取官网URL - 从访问官网按钮
        if (preg_match('/<a[^>]*href="([^"]+)"[^>]*class="[^"]*btn-arrow[^"]*"[^>]*>.*?访问官网.*?<\/a>/is', $html, $matches)) {
            $official_url = trim($matches[1]);
        }
        
        // 确保URL为完整路径
        if ($icon && strpos($icon, 'http') !== 0) {
            if (strpos($icon, '//') === 0) {
                $icon = 'https:' . $icon;
            } else if (strpos($icon, '/') === 0) {
                $icon = 'https://ai-bot.cn' . $icon;
            } else {
                $icon = 'https://ai-bot.cn/' . $icon;
            }
        }
        
        if ($thumb && strpos($thumb, 'http') !== 0) {
            if (strpos($thumb, '//') === 0) {
                $thumb = 'https:' . $thumb;
            } else if (strpos($thumb, '/') === 0) {
                $thumb = 'https://ai-bot.cn' . $thumb;
            } else {
                $thumb = 'https://ai-bot.cn/' . $thumb;
            }
        }
        
        // 下载图标和缩略图
        $icon_file = '';
        if ($icon) {
            $icon_data = $this->downImg($icon);
            if ($icon_data) {
                $icon_data['attachment'] = \Phpcmf\Service::M('Attachment')->get_attach_info();
                $rt = \Phpcmf\Service::L('upload')->down_file($icon_data);
                if ($rt['code']) {
                    $att = \Phpcmf\Service::M('Attachment')->save_data($rt['data']);
                    if ($att['code']) {
                        $icon_file = $att['code'];
                    }
                }
            }
        }
        
        $thumb_file = '';
        if ($thumb) {
            $thumb_data = $this->downImg($thumb);
            if ($thumb_data) {
                $thumb_data['attachment'] = \Phpcmf\Service::M('Attachment')->get_attach_info();
                $rt = \Phpcmf\Service::L('upload')->down_file($thumb_data);
                if ($rt['code']) {
                    $att = \Phpcmf\Service::M('Attachment')->save_data($rt['data']);
                    if ($att['code']) {
                        $thumb_file = $att['code'];
                    }
                }
            }
        }
        
        // 返回结果
        $this->_json(1, '采集成功', [
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'icon' => $icon,
            'icon_file' => $icon_file,
            'thumb' => $thumb,
            'thumb_file' => $thumb_file,
            'url' => $url,
            'official_url' => $official_url,
            'price_info' => $price_info,
            'category' => $category,
            'content' => $content,
            'features' => $features,
            'usage_scenarios' => $usage_scenarios
        ]);
    }
    
    private function downImg($imageUrl){
//获取访问图片链接域名
        $host = parse_url($imageUrl, PHP_URL_HOST);
// 使用cURL获取图片数据
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $imageUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Host: '.$host,
            'Connection: keep-alive',
            'Pragma: no-cache',
            'Cache-Control: no-cache',
            'sec-ch-ua: "Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Windows"',
            'Upgrade-Insecure-Requests: 1',
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Sec-Fetch-Site: none',
            'Sec-Fetch-Mode: navigate',
            'Sec-Fetch-User: ?1',
            'Sec-Fetch-Dest: document',
            'Accept-Encoding: gzip, deflate, br',
            'Accept-Language: zh-CN,zh;q=0.9',
            'Cookie: SL_G_WPT_TO=zh; SL_GWPT_Show_Hide_tmp=1; SL_wptGlobTipTmp=1'
        ));
        $imageData = curl_exec($ch);
// 检查图片数据是否获取成功
        if (!empty($imageData)) {
            // 获取图片的MIME类型
            $mimeType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
            switch ( $mimeType) {
                case 'image/png':
                    $extension = 'png';
                    break;
                case 'image/gif':
                    $extension = 'gif';
                    break;
                default:
                    $extension = 'jpg';
                    break;
                // 添加其他图片格式...
            }
            curl_close($ch);
            return ['file_ext'=>$extension,'file_content'=> $imageData];

        }else{
            curl_close($ch);
            return false;
        }

    }
    public function icon(){
exit();
        $html = file_get_contents('data.txt');

        $pattern = '/<i\s+class="([^"]+) icon-fw icon-lg"><\/i>\s*<span>([^<]+)<\/span>/';
        preg_match_all($pattern, $html, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $iconClass = trim($match[1]);
            $text = trim($match[2]);
            if($iconClass && $text){
                var_dump($text,$iconClass);

                \Phpcmf\Service::M()->db->table('1_webnav_category')->where('name',$text)->update(['tubiao'=>$iconClass]);
            }


        }


    }

}
