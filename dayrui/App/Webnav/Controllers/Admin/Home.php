<?php namespace Phpcmf\Controllers\Admin;

/**
 * 二次开发时可以修改本文件，不影响升级覆盖
 */

class Home extends \Phpcmf\Admin\Module
{

	public function index() {
		$this->_Admin_List();
	}

	public function add() {
		$this->_Admin_Add();
	}

	public function edit() {
		$this->_Admin_Edit();
	}

	public function show_index() {
		$this->_Admin_Show();
	}

	public function move_edit() {
		$this->_Admin_Move();
	}

	public function tui_edit() {
		$this->_Admin_Send();
	}

	public function syncat_edit() {
		$this->_Admin_Syncat();
	}

	public function del() {
		$this->_Admin_Del();
	}
	
	// 采集网址内容
	public function fetch_url() {
	    $url = \Phpcmf\Service::L('input')->get('url');
	    if (!$url) {
	        $this->_json(0, '请输入要采集的网址');
	    }

	    $source = \Phpcmf\Service::L('input')->get('source');

	    // AI工具集采集源
	    if ($source == 'aibot') {
	        return $this->fetch_url_aibot($url);
	    }

	    // 默认采集源处理
	    $html = $this->fetch_html_content($url);

	    if (!$html) {
	        $this->_json(0, '采集请求失败，请检查网络连接');
	    }
	    
	    // 解析HTML
	    $title = '';
	    $description = '';
	    $keywords = '';
	    $icon = '';
	    
	    // 提取标题
	    if (preg_match('/<title>(.*?)<\/title>/is', $html, $matches)) {
	        $title = trim($matches[1]);
	    }
	    
	    // 提取描述
	    if (preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
	        $description = trim($matches[1]);
	    }
	    
	    // 提取关键词
	    if (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
	        $keywords = trim($matches[1]);
	    }
	    
	    // 提取网站图标
	    if (preg_match('/<link\s+[^>]*rel=["\'](?:shortcut )?icon["\']\s+[^>]*href=["\']([^"\']+)["\']/is', $html, $matches)) {
	        $icon = trim($matches[1]);
	        
	        // 处理相对路径
	        if (strpos($icon, 'http') !== 0) {
	            $parsed_url = parse_url($url);
	            $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'];
	            
	            if (strpos($icon, '/') === 0) {
	                $icon = $base_url . $icon;
	            } else {
	                $path = isset($parsed_url['path']) ? dirname($parsed_url['path']) : '';
	                $icon = $base_url . $path . '/' . $icon;
	            }
	        }
	    }
	    
	    // 返回结果
	    $this->_json(1, '采集成功', [
	        'title' => $title,
	        'description' => $description,
	        'keywords' => $keywords,
	        'icon' => $icon,
	        'url' => $url
	    ]);
	}
	
	// AI工具集采集源处理
	private function fetch_url_aibot($url) {
	    // 初始化cURL会话
	    $ch = curl_init();
	    
	    // 设置cURL选项
	    curl_setopt($ch, CURLOPT_URL, $url);
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
	    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
	    
	    // 执行cURL请求
	    $html = curl_exec($ch);
	    
	    // 检查是否有错误发生
	    if (curl_errno($ch)) {
	        $this->_json(0, '采集失败: ' . curl_error($ch));
	    }
	    
	    // 关闭cURL会话
	    curl_close($ch);
	    
	    // 解析HTML
	    $title = '';
	    $description = '';
	    $keywords = '';
	    $icon = '';
	    $content = '';
	    
	    // 提取标题 - 从site-name类
	    if (preg_match('/<h1 class="site-name[^"]*">(.*?)<\/h1>/is', $html, $matches)) {
	        $title = trim(strip_tags($matches[1]));
	    } elseif (preg_match('/<title>(.*?)<\/title>/is', $html, $matches)) {
	        $title = trim($matches[1]);
	        // 移除网站名称部分
	        $title = preg_replace('/\s*\|\s*AI工具集$/', '', $title);
	    }
	    
	    // 提取描述 - 从panel-body类或meta description
	    if (preg_match('/<div class="panel-body single[^"]*">(.*?)<h2>/is', $html, $matches)) {
	        $content = $matches[1];
	        if (preg_match('/<p>(.*?)<\/p>/is', $content, $desc_matches)) {
	            $description = trim(strip_tags($desc_matches[1]));
	        }
	    } elseif (preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
	        $description = trim($matches[1]);
	    }
	    
	    // 提取关键词 - 从标签或meta keywords
	    if (preg_match('/标签：(.*?)<div/is', $html, $matches)) {
	        if (preg_match_all('/<a[^>]*>([^<]+)<\/a>/is', $matches[1], $tag_matches)) {
	            $keywords = implode(',', $tag_matches[1]);
	        }
	    } elseif (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
	        $keywords = trim($matches[1]);
	    }
	    
	    // 提取内容 - 从主要内容区域
	    if (preg_match('/<div class="panel-body single[^"]*">(.*?)<\/div>/is', $html, $matches)) {
	        $content = trim($matches[1]);
	        // 清理内容中的不必要标签和元素
	        $content = preg_replace('/<h2[^>]*>.*?<\/h2>/is', '', $content);
	        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/is', '', $content);
	    }
	    
	    // 提取图标 - 从页面图标或第一个图片
	    if (preg_match('/<img[^>]*class="img-cover[^"]*"[^>]*data-src="([^"]+)"/is', $html, $matches)) {
	        $icon = trim($matches[1]);
	    } elseif (preg_match('/<link\s+[^>]*rel=["\'](?:shortcut )?icon["\']\s+[^>]*href=["\']([^"\']+)["\']/is', $html, $matches)) {
	        $icon = trim($matches[1]);
	    }
	    
	    // 确保URL为完整路径
	    if ($icon && strpos($icon, 'http') !== 0) {
	        if (strpos($icon, '//') === 0) {
	            $icon = 'https:' . $icon;
	        } else if (strpos($icon, '/') === 0) {
	            $icon = 'https://ai-bot.cn' . $icon;
	        } else {
	            $icon = 'https://ai-bot.cn/' . $icon;
	        }
	    }
	    
	    // 返回结果
	    $this->_json(1, '采集成功', [
	        'title' => $title,
	        'description' => $description,
	        'keywords' => $keywords,
	        'icon' => $icon,
	        'url' => $url,
	        'content' => $content
	    ]);
	}

	// 获取HTML内容的通用方法，支持多种HTTP客户端
	private function fetch_html_content($url) {
	    // 方法1: 尝试使用cURL（如果可用）
	    if (function_exists('curl_init')) {
	        $ch = curl_init();
	        curl_setopt($ch, CURLOPT_URL, $url);
	        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
	        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
	        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

	        $html = curl_exec($ch);
	        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	        $error = curl_error($ch);
	        curl_close($ch);

	        if ($html !== false && !$error && $http_code < 400) {
	            return $html;
	        }
	    }

	    // 方法2: 尝试使用file_get_contents（如果allow_url_fopen开启）
	    if (ini_get('allow_url_fopen')) {
	        $context = stream_context_create([
	            'http' => [
	                'method' => 'GET',
	                'header' => "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\r\n",
	                'timeout' => 30,
	                'follow_location' => 1,
	                'max_redirects' => 5
	            ],
	            'ssl' => [
	                'verify_peer' => false,
	                'verify_peer_name' => false
	            ]
	        ]);

	        $html = @file_get_contents($url, false, $context);
	        if ($html !== false) {
	            return $html;
	        }
	    }

	    return false;
	}
}
