{template "header.html"}
<div class="note note-danger">
    <p>{dr_lang('推荐位是对内容进行推荐分类，方便前端数据调用，例如：首页头条、幻灯推荐、频道推荐等')}</p>
</div>
{php echo \Phpcmf\Service::L('Field')->get('select')->get_select_search_code();}

<div class="right-card-box">
<form class="form-horizontal myfbody" role="form" id="myform">
    {$form}
    <div class="table-scrollable">
        <table class="table table-noborder table-striped table-bordered table-hover table-checkable dataTable">
            <thead>
            <tr class="heading">
                <th width="40"> Id </th>
                <th width="250"> {dr_lang('名称')} </th>
                <th width="180"> <a href="{dr_url('api/icon')}" target="_blank"><i class="fa fa-arrow-right fa-fw" /></i>  {dr_lang('图标')} </a></th>
                <th> {dr_lang('后台角色使用权限')}</th>
            </tr>
            </thead>
            <tbody>
            {loop $flag $i $t}
            <tr class="odd gradeX">
                <td>{$i}</td>
                <td><input class="form-control" name="flag[{$i}][name]" type="text" value="{htmlspecialchars((string)$t['name'])}" placeholder="{dr_lang('推荐位名称')}"></td>
                <td><input class="form-control" type="text" name="flag[{$i}][icon]" value="{htmlspecialchars((string)$t['icon'])}" placeholder="{dr_lang('fa fa-user')}" />
                </td>
                <td style="overflow: inherit;">
                    <label style="min-width: 200px">
                        <select class="form-control bs-select" name="role[{$i}][]" multiple data-actions-box="true">
                            {loop $role $a}
                            {if $a.id>1}
                            <option {if isset($t['role'][$a['id']]) && $t['role'][$a['id']]} selected{/if} value="{$a.id}">{dr_lang($a.name)}</option>
                            {/if}
                            {/loop}
                        </select>
                    </label>
                </td>
            </tr>
            {/loop}
            {php $i++;}
            <tr class="odd gradeX">
                <td>{$i}</td>
                <td><input class="form-control" name="flag[{$i}][name]" type="text" value="" placeholder="{dr_lang('新增推荐位')}"></td>
                <td><input class="form-control" type="text" name="flag[{$i}][icon]" value="" placeholder="{dr_lang('fa fa-user')}" />
                </td>
                <td>

                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
        </div>
    </div>

</form>
</div>
<script type="text/javascript">
    function dr_set_flag(id) {

        var url = '{dr_url("module/module/flag_save_edit")}&id='+id;
        $(".dr_flag_"+id).each(function(){
            url+= '&data['+ $(this).attr('iid')+']='+ $(this).val()
        });

        layer.open({
            type: 2,
            title: '{dr_lang('后台权限设置')}',
            shadeClose: true,
            shade: 0.3,
            area: [{php echo \Phpcmf\Service::IS_PC_USER() ? '\'500px\', \'50%\'' : '"95%", "90%"'}],
            btn: [lang['ok']],
            yes: function(index, layero){
                var body = layer.getChildFrame('body', index);
                // 延迟加载
                var loading = layer.load(2, {
                    shade: [0.3,'#fff'], //0.1透明度的白色背景
                    time: 10000
                });
                $(".dr_flag_"+id).each(function(){
                    var iid = $(this).attr('iid');
                    $("#dr_flag_"+id+"_"+iid).val($(body).find('#top_dr_flag_'+iid).val());
                });
                $("#dr_status_"+id).html(" <i class='fa fa-check-square'></i>");
                return false;
            },
            success: function(layero, index){
                // 主要用于后台权限验证

                dr_iframe_error(layer, index, 1);
                $(".dr_flag_"+id).each(function(){
                    var iid = $(this).attr('iid');
                    $(body).find('#top_dr_flag_'+iid).val(Math.abs($("#dr_flag_"+id+"_"+iid).val()));
                });
            },
            content: url+'&is_iframe=1'
        });
    }
</script>

{template "footer.html"}