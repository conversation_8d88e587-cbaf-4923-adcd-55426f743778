{template "header.html"}
<div class="note note-danger">
    <p>{dr_lang('勾选状态的字段可以显示在本栏目的编辑页面中')}</p>
</div>

<form class="form-horizontal" role="form" id="myform">
    {dr_form_hidden()}
    <div class="table-scrollable">
        <table class="table table-striped table-bordered fc-head-table table-hover table-checkable">
            <thead>
            <tr class="heading">
                <th class="myselect">
                    {dr_lang('可用')}
                </th>
                <th style="text-align: center;font-size: 15px;" width="65">Id</th>
                <th>{dr_lang('字段')}</th>
                <th>{dr_lang('类别')}</th>
            </tr>
            </thead>
            <tbody>
            {loop $field $t}
            {if $t.id}
            <tr class="odd gradeX" id="dr_row_{$t.id}">
                <td class="myselect">
                    <label class="mt-table mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                        <input type="checkbox" class="checkboxes" {if !isset($cat_field[$t.fieldname])} checked {/if} name="ids[]" value="{$t.fieldname}" />
                        <span></span>
                    </label>
                </td>
                <td style="text-align: center;font-size: 15px;">{$t.id}</td>
                <td> {$t.spacer}  {$t.name} / {$t.fieldname}</td>
                <td>{$t.fieldtype}</td>
            </tr>
            {/if}
            {/loop}
            </tbody>
        </table>
    </div>

    <div class="note note-danger {if isset($_GET['is_yc'])}hide{/if}" style="margin-top: 20px;">
        <p>{dr_lang('将以上的字段配置覆盖到以下的选中栏目之中')}</p>
    </div>

    <div class="form-group {if isset($_GET['is_yc'])}hide{/if}">
        <div class="col-xs-12" id="multi-select">
            {$select}
        </div>
    </div>

    {php echo \Phpcmf\Service::L('Field')->get('select')->get_select_search_code();}

</form>

{template "footer.html"}