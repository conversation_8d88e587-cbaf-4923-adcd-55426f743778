{template "header.html"}
<script type="text/javascript">
    $(function() {
        $('.tabs-left').height($(document).height());
    });
</script>
<form class="form-horizontal" method="post" role="form" id="myform">
    {$form}
    <div class="portlet-body">
        {if $ids}
        <div class="form-group">
            <div class="col-md-12">
                <label class="">{dr_lang('本次批量处理')}</label>
                <label class="label label-danger">{dr_count($ids)}</label>
                <label class="">{dr_lang('条数据')}</label>
            </div>
        </div>
        {/if}
        {if $page==0}
        <div class="form-body"  style="margin-left:40px">
            <div class="form-group">
                <div class="mt-radio-inline">
                    <label class="mt-radio">
                        <input type="radio" onclick="$('.myflag').show()" name="clear"  value="0" checked=""> {dr_lang('启用')}
                        <span></span>
                    </label>
                    <label class="mt-radio">
                        <input type="radio" onclick="$('.myflag').hide()" name="clear"  value="1"> {dr_lang('清空推荐位')}
                        <span></span>
                    </label>
                </div>
            </div>
            {if $module['setting']['flag']}
            <div class="form-group myflag">
                <div class="mt-checkbox-list">

                    {loop $module['setting']['flag'] $i $t}
                    <label class="mt-checkbox mt-checkbox-outline">
                        <input name="flag[]" type="checkbox" value="{$i}"> <i class="{dr_icon($t.icon)}"></i> {$t.name}
                        <span></span>
                    </label>
                    {/loop}
                </div>
            </div>
            {else}
            <div style="color:red">{dr_lang('推荐位尚未设置')}</div>
            {/if}
        </div>
        {else if $page==1}
            <div class="form-group">
                <div class="col-md-12"> {$select} </div>
            </div>
            <div class="form-group">
                <div class="col-md-12">
                    <span class="help-block">{dr_lang('可以同步发送到多个栏目之中')}</span>
                </div>
            </div>
        {else if $page==5 or $page==6}
		<div class="form-group">
            <div class="col-md-12">
                <div class="mt-radio-inline">
                    <label class="mt-radio">
                        <input type="radio" name="clear"  value="0" checked=""> {dr_lang('原文保持可阅读')}
                        <span></span>
                    </label>
                    <label class="mt-radio">
                        <input type="radio" name="clear"  value="1"> {dr_lang('原文不可阅读')}
                        <span></span>
                    </label>
                </div>
                <span class="help-block">{dr_lang('不可阅读表示退稿后不能阅读内容')}</span>
			</div>
        </div>
		<div class="form-group">
            <label class="col-md-2 control-label ajax_name">{dr_lang('退稿理由')}</label>
            <div class="col-md-8">
                <textarea class="form-control" id="dr_note" name="note" rows="5"></textarea>
                <span class="help-block">{dr_lang('将选中内容状态设置为[审核被拒]状态，用户可以重新编辑，再投稿审核')}</span>
            </div>
        </div>
        {/if}
        </div>
    </div>
</form>

{template "footer.html"}