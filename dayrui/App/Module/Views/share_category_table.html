{template "header.html"}

<div class="note note-danger">
    <p>
        {template "category_btn.html"}
    </p>
</div>
<div class="note note-danger">
    <p>
        1、后台：在后台列表中，必须带有栏目参数时才会加载分表数据
    </p>
    <p>
        2、前台：分表数据不参与全模块搜索，只有带有catid参数时，才会对分表进行查询
    </p>
    <p>
        3、当对模块内容表字段变更时，需要在本页面执行一次同步字段到分表
    </p>
</div>

<div class="right-card-box">

    <form class="form-horizontal" role="form" id="myform">
        {dr_form_hidden()}
        <div class="table-scrollable">
            <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                <thead>
                <tr class="heading">
                    <th width="120" style="text-align:center">{dr_lang('ID')}</th>
                    <th width="250">{dr_lang('顶级栏目')}</th>
                    <th width="150">{dr_lang('模块')}</th>
                    <th width="150">{dr_lang('内容主表')}</th>
                    <th width="120" style="text-align:center">{dr_lang('数据量')}</th>
                    <th>{dr_lang('操作')}</th>
                </tr>
                </thead>
                <tbody>
                {php $cids=[];}
                {loop $data $t}
                {php !$share && $t.mid=APP_DIR;}
                {if $t.mid}
                <tr>
                    <td style="text-align:center">{$t.id}</td>
                    <td>{$t.name}</td>
                    <td>{$t.mid}</td>
                    <td>{php echo XR_M()->dbprefix(dr_module_ctable(SITE_ID.'_'.$t['mid'], $t));}</td>
                    <td style="text-align:center" class="cat-total-{$t.id}"> - </td>
                    <td>
                        {if $t.is_ctable}
                        <label><a class="btn btn-xs red"  href="javascript:;"> {dr_lang('已开启')} </a></label>
                        <label><a class="btn btn-xs blue"  onclick="dr_open_tips({$t.id})" href="javascript:;"> <i class="fa fa-database"></i> {dr_lang('数据同步到分表')} </a></label>
                        <label><a class="btn btn-xs red"  onclick="dr_field_tips({$t.id})" href="javascript:;"> <i class="fa fa-database"></i> {dr_lang('字段同步到分表')} </a></label>
                        <label><a class="btn btn-xs green"  href="{dr_url($t.mid.'/home/<USER>', ['catid'=>$t.id])}"> <i class="fa fa-table"></i> {dr_lang('查看数据')} </a></label>
                        {else}
                        <label><a class="btn btn-xs green"  onclick="dr_open_tips({$t.id})" href="javascript:;"> <i class="fa fa-database"></i> {dr_lang('立即开启分表')} </a></label>
                        {/if}
                    </td>
                </tr>
                {php $cids[]=$t.id.'-'.$t.mid;}
                {/if}
                {/loop}
                <script>
                    function dr_field_tips(id){
                        dr_iframe_show('{dr_lang('栏目[%s]分表字段同步', $t.name)}', '{dr_url(APP_DIR.'/category/table_add', ['at'=>'field'])}&id='+id, '400px', '250px');
                    }
                    function dr_open_tips(id){
                        layer.confirm(
                            "{dr_lang('操作之前，请备份好全站数据库，以免出现数据丢失的情况')}",
                            {
                                icon: 3,
                                shade: 0,
                                title: lang['ts'],
                                btn: [lang['ok'], lang['esc']]
                            }, function(index){
                                layer.close(index);
                            dr_iframe_show('{dr_lang('栏目[%s]分表储存', $t.name)}', '{dr_url(APP_DIR.'/category/table_add', ['at'=>'open'])}&id='+id, '400px', '250px');
                        });
                    }
                    $(function () {
                        $.ajax({
                            type: "POST",
                            dataType: "json",
                            url: "{dr_url('module/api/ctotal')}&zt=1",
                            data: {
                                'cid': {json_encode($cids)},
                                {csrf_token()}: "{csrf_hash()}",
                            },
                            success: function(json2) {
                                if (json2.code == 1) {
                                    eval(json2.msg);
                                }
                            },
                            error: function(HttpRequest, ajaxOptions, thrownError) {
                                dr_ajax_alert_error(HttpRequest, ajaxOptions, thrownError)
                            }
                        });
                        $('.table-striped tbody tr:last').attr('style', 'border-bottom-width:0px')
                    });
                </script>
                </tbody>
            </table>
        </div>
    </form>
</div>

{template "footer.html"}