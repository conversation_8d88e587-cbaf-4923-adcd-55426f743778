{template "header.html"}


<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="myfbody">
    <div class="portlet bordered light ">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a toid="dr_default" iid="0" data-toggle="tab"> {dr_lang('网站信息')} </a>
                </li>
                {loop $mymerge $i $t}
                <li class="{if $page==($i+1)}active{/if}">
                    <a toid="dr_row_{$t}" iid="{$i+1}" data-toggle="tab">{$field[$t]['name']}</a>
                </li>
                {/loop}
                {if $mymerge}
                <script type="text/javascript">
                    $(function () {
                        $('.myfield-main .portlet').hide();
                        $('#dr_default').show();
                        $('.nav-tabs a').click(function () {
                            var tid = $(this).attr('toid');
                            var iid = $(this).attr('iid');
                            $('#dr_page').val(iid);
                            $('.myfield-main .portlet').hide();
                            $('#'+tid).show();
                        });
                        $('.mytitle').hide();
                        {if $page>0}
                        $('.myfield-main .portlet').hide();
                        $('#dr_row_{php echo $mymerge[$page-1];}').show();
                        {/if}
                    });
                </script>
                {/if}
            </ul>
        </div>
        <div class="portlet-body">
            <div class="myfield-main">

                <div class="portlet light bordered" id="dr_default">
                    <div class="portlet-body">
                        <div class="form-body">



                        <div class="form-group" id="dr_row_logo">
                            <label class="col-md-2 control-label">{dr_lang('LOGO')}</label>
                            <div class="col-md-9">
                                {$logofield}
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('网站名称')}</label>
                            <div class="col-md-9">
                                <label><input class="form-control input-large" type="text" name="data[SITE_NAME]" id="dr_name" value="{htmlspecialchars((string)$data['SITE_NAME'])}"></label>
                             </div>
                        </div>
                        <div class="form-group" id="dr_row_icp">
                            <label class="col-md-2 control-label">{dr_lang('ICP备案信息')}</label>
                            <div class="col-md-9">
                                <label><input class="form-control input-large" type="text" name="data[SITE_ICP]" value="{htmlspecialchars((string)$data['SITE_ICP'])}"></label>
                             </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('第三方统计代码')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control" style="height:100px" name="data[SITE_TONGJI]">{$data['SITE_TONGJI']}</textarea>
                            </div>
                        </div>


                        {if $my_site_info}
                        {load "$my_site_info"}
                        {/if}

                        {$myfield}

                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
    </div>
    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存')}</button></label>
            {if dr_is_app('mbdy') && $ci->_is_admin_auth()}<label><a class="btn  yellow" href="javascript:dr_iframe_show('show', '{dr_url('mbdy/site/cms')}');"> <i class="fa fa-code"></i>
                {dr_lang('前端调用')}</a></label>{/if}
        </div>
    </div>
</form>

{template "footer.html"}