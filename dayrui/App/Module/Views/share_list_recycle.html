{template "header.html"}

{template "api_list_date_search.html"}

<div class="note note-danger"  id="table-search-tool">
    <div class="row table-search-tool">
        <form action="{SELF}" method="get">
            {dr_form_search_hidden(['field' => 'content'])}
            {if $is_category_show}
            <div class="col-md-12 col-sm-12">
                <label>
                    {$category_select}
                </label>
            </div>
            {/if}
            <div class="col-md-12 col-sm-12">
                <label><input type="text" class="form-control" placeholder="{dr_lang('全文搜索')}" value="{$param['keyword']}" name="keyword" /></label>
            </div>
            <div class="col-md-12 col-sm-12">
                <label>
                    <div class="input-group input-medium date-picker input-daterange" data-date="" data-date-format="yyyy-mm-dd">
                        <input type="text" class="form-control" value="{$param.date_form}" name="date_form">
                        <span class="input-group-addon"> {dr_lang('到')} </span>
                        <input type="text" class="form-control" value="{$param.date_to}" name="date_to">
                    </div>
                </label>
            </div>
            <div class="col-md-12 col-sm-12">
                <label><button type="submit" class="btn blue btn-sm onloading" name="submit" > <i class="fa fa-search"></i> {dr_lang('搜索')}</button></label>
            </div>
        </form>
    </div>
</div>

<div class="right-card-box">



    <form class="form-horizontal" role="form" id="myform">
        {dr_form_hidden()}

        <div class="bootstrap-table bootstrap-table2">
            <div id="toolbar" class="toolbar">

                {if $ci->_is_admin_auth('edit')}
                <label><button type="button" onclick="dr_ajax_option('{dr_url($uriprefix.'/recovery_add')}', '{dr_lang('你确定要恢复它们吗？')}', 1)" class="btn green btn-sm"> <i class="fa fa-reply"></i> {dr_lang('恢复')}</button></label>
                {/if}
                {if $ci->_is_admin_auth('del')}
                <label><button type="button" onclick="dr_ajax_option('{dr_url($uriprefix.'/del')}', '{dr_lang('你确定要删除它们吗？')}', 1)" class="btn red btn-sm"> <i class="fa fa-trash"></i> {dr_lang('删除')}</button></label>
                <label><button type="button" onclick="dr_ajax_confirm_url('{dr_url($uriprefix.'/recycle_del')}', '{dr_lang('你确定要清空回收站吗？')}', '')" class="btn red btn-sm"> <i class="fa fa-close"></i> {dr_lang('清空回收站')}</button></label>
                {/if}

            </div>
        </div>


        <div class="clearfix"></div>
        <div class="table-scrollable table-clearfix">
            <table class="table table-striped table-bordered2 table-bordered table-hover table-checkable dataTable">
                <thead>
                <tr class="heading">
                    {if $ci->_is_admin_auth('del') || $ci->_is_admin_auth('edit')}
                    <th class="myselect">
                        <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                            <input type="checkbox" class="group-checkable" data-set=".checkboxes" />
                            <span></span>
                        </label>
                    </th>
                    {/if}
                    <th class="{dr_sorting('cid')}" name="cid">{dr_lang('主题')}</th>
                    <th class="{dr_sorting('catid')}" name="cait" width="150">{dr_lang('栏目')}</th>
                    <th class="{dr_sorting('uid')}" name="uid" width="130">{dr_lang('账号')}</th>
                    <th class="{dr_sorting('inputtime')}" name="inputtime" width="160">{dr_lang('删除时间')}</th>
                    <th>{dr_lang('操作')}</th>
                </tr>
                </thead>
                <tbody>
                {loop $list $t}
                <?php
                $c=dr_string2array($t.content);
                $t['url'] = SITE_URL.'index.php?s='.APP_DIR.'&c=show&m=recycle&id='.$t['id'];
                $t['url'] = ADMIN_URL.(dr_url('api/slogin', ['url' => urlencode($t['url'])]));
                ?>
                <tr class="odd gradeX" id="dr_row_{$t.id}">
                    {if $ci->_is_admin_auth('del') || $ci->_is_admin_auth('edit')}
                    <td class="myselect">
                        <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                            <input type="checkbox" class="checkboxes" name="ids[]" value="{$t.id}" />
                            <span></span>
                        </label>
                    </td>
                    {/if}
                    <td>{Function_list::title($c[SITE_ID.'_'.APP_DIR]['title'], null, $c[SITE_ID.'_'.APP_DIR])}</td>
                    <td>{Function_list::catid($t.catid, null)}</td>
                    <td>{Function_list::uid($t.uid, null)}</td>
                    <td>{dr_date($t.inputtime)}</td>
                    <td>
                        {if $ci->_is_admin_auth('edit')}
                        <label><a href="{dr_url(APP_DIR.'/recycle/edit',array('id'=>$t.id))}" class="btn btn-xs blue"> <i class="fa fa-edit"></i> {dr_lang('修改')}</a></label>
                        {/if}
                        <label><a href="{$t.url}" target="_blank" class="btn btn-xs green"> <i class="fa fa-search"></i> {dr_lang('查看内容')}</a></label>
                    </td>
                </tr>
                {/loop}
                </tbody>
            </table>
        </div>
        {if $mypages}
        <div class="row fc-list-footer table-checkable ">

            <div class="col-md-12 fc-list-page">
                {$mypages}
            </div>
        </div>
        {/if}
    </form>
</div>

{template "footer.html"}