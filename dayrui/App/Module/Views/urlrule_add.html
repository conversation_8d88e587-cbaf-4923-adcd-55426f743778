{template "header.html"}

<script type="text/javascript">
    $(function() {
        dr_set_type({intval($type)});
    });
    function dr_set_type(id) {
        $('.dr_type').hide();
        $('.dr_type_'+id).show();
    }
</script>
<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="myfbody">
    <div class="portlet bordered light ">
        <div class="portlet-title">
            <div class="caption">
                <span class="caption-subject font-green-sharp">
                    {dr_lang('URL规则')}
                </span>
            </div>
            <div class="actions">
                <div class="btn-group">
                    <a class="btn" href="{$reply_url}"> <i class="fa fa-mail-reply"></i> {dr_lang('返回')}</a>
                </div>
            </div>
        </div>
        <div class="portlet-body">
            <div class="form-body">

                {$myfield}
                <div class="form-group">
                    <label class="col-md-2 control-label">{dr_lang('类别')}</label>
                    <div class="col-md-9">
                        <div class="mt-radio-inline">
                            {loop $ci->type $ii $name}
                            <label class="mt-radio">
                                <input onclick="dr_set_type({$ii})" {if $ii==$type}checked{/if} name="type" type="radio" value="{$ii}"> {$name}
                                <span></span>
                            </label>
                            {/loop}
                        </div>
                    </div>
                </div>

                <!--单页URL-->
                <div class="form-group dr_type_0 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('自定义页面')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[0][page]" value="{htmlspecialchars((string)$value.page)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_page()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_0 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('自定义页面分页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[0][page_page]" value="{htmlspecialchars((string)$value.page_page)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_page()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>



                <!--共享模块-->

                <div class="form-group dr_type_2 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('搜索页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[2][search]" value="{htmlspecialchars((string)$value.search)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_search()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_2 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('搜索分页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[2][search_page]" value="{htmlspecialchars((string)$value.search_page)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_search_page()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>

                <!--独立模块-->
                <div class="form-group dr_type_1 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('模块首页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[1][module]" value="{htmlspecialchars((string)$value.module)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_index()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_1 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('栏目页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[1][list]" value="{htmlspecialchars((string)$value.list)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_list()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_1 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('栏目分页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[1][list_page]" value="{htmlspecialchars((string)$value.list_page)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_list()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_1 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('内容页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[1][show]" value="{htmlspecialchars((string)$value.show)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_show()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_1 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('内容分页（可选）')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[1][show_page]" value="{htmlspecialchars((string)$value.show_page)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_show()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_1 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('搜索页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[1][search]" value="{htmlspecialchars((string)$value.search)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_search()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_1 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('搜索分页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[1][search_page]" value="{htmlspecialchars((string)$value.search_page)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_search_page()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>

                <!--共享模块-->
                <div class="form-group dr_type_3 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('栏目页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[3][list]" value="{htmlspecialchars((string)$value.list)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_list()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_3 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('栏目分页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[3][list_page]" value="{htmlspecialchars((string)$value.list_page)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_list()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_3 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('内容页')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[3][show]" value="{htmlspecialchars((string)$value.show)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_show()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group dr_type_3 dr_type">
                    <label class="col-md-2 control-label">{dr_lang('内容分页（可选）')}</label>
                    <div class="col-md-7">
                        <div class="input-group">
                            <input type="text" name="value[3][show_page]" value="{htmlspecialchars((string)$value.show_page)}" class="form-control">
                            <span class="input-group-btn">
                                <button class="btn blue" onclick="dr_url_module_show()" type="button"><i class="fa fa-code"></i> {dr_lang('提示')}</button>
                            </span>
                        </div>
                    </div>
                </div>



                <div class="form-group">
                    <label class="col-md-2 control-label">{dr_lang('连接符号')}</label>
                    <div class="col-md-7">
                        <label>
                            <input type="text" name="catjoin" value="{htmlspecialchars((string)$value.catjoin)}" class="form-control">
                        </label>
                        <span class="help-block"> {dr_lang('针对pdirname的连接符号，默认为"/"，如：china[连接符号]beijin')} </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}', 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存内容')}</button></label>
            <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}', 'myform', '2000', '{$post_url}')" class="btn green"> <i class="fa fa-plus"></i> {dr_lang('保存再添加')}</button></label>
            <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}', 'myform', '2000', '{$reply_url}')" class="btn yellow"> <i class="fa fa-mail-reply-all"></i> {dr_lang('保存并返回')}</button></label>
        </div>
    </div>
</form>

{template "footer.html"}