{template "header.html"}

<form class="form-horizontal" method="post" role="form" id="myform">
    {dr_form_hidden()}
    <div class="form-body">

        {if $dirname == 'share'}
        {if dr_is_app('chtml')}
        <div class="form-group">
            <label class="col-md-2 control-label">{dr_lang('栏目生成静态')}</label>
            <div class="col-md-9">
                <div class="mt-radio-inline">
                    <label class="mt-radio mt-radio-outline"><input type="radio" name="setting[html]" value="1" {if $data['setting']['html']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                    <label class="mt-radio mt-radio-outline"><input type="radio" name="setting[html]" value="0" {if empty($data['setting']['html'])}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                </div>
                <span class="help-block">{dr_lang('开启生成静态时必须给它配置URL规则')}</span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 control-label">{dr_lang('内容生成静态')}</label>
            <div class="col-md-9">
                <div class="mt-radio-inline">
                    <label class="mt-radio mt-radio-outline"><input type="radio" name="setting[chtml]" value="1" {if $data['setting']['chtml']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                    <label class="mt-radio mt-radio-outline"><input type="radio" name="setting[chtml]" value="0" {if empty($data['setting']['chtml'])}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                </div>
                <span class="help-block">{dr_lang('开启生成静态时必须给它配置URL规则')}</span>
            </div>
        </div>
        {else}
        <input type="hidden" name="setting[html]" value="{intval($data['setting']['html'])}">
        {/if}
        <div class="form-group">
            <label class="col-md-2 control-label">{dr_lang('URL规则')}</label>
            <div class="col-md-9">

                <label>
                    <select class="form-control" name="setting[urlrule]">
                        <option value="0"> {dr_lang('动态地址')} </option>
                        {list action=cache name=urlrule return=u}
                        {if $u.type==3}<option value="{$u.id}" {if $u.id == $data['setting']['urlrule']}selected{/if}> {$u.name} </option>{/if}
                        {/list}
                    </select>
                </label>
                <label style="margin-left:20px;"><a href="{dr_url('module/urlrule/index', ['hide_menu' => 1])}" style="color:blue !important">{dr_lang('[URL规则管理]')}</a> </label>

            </div>
        </div>
        {/if}

        <?php !$data['setting']['seo']['list_title'] && $data['setting']['seo']['list_title'] = '[第{page}页{join}]{catpname}{join}{'.'SITE_NAME}';?>
        <div class="form-group">
            <label class="col-md-2 control-label">{dr_lang('SEO标题')}</label>
            <div class="col-md-9">
                <textarea class="form-control " style="height:90px" name="seo[list_title]">{$data['setting']['seo']['list_title']}</textarea>
                <span class="help-block">
                    <button class="btn btn-xs green" onclick="dr_seo_title_rule()" type="button"><i class="fa fa-code"></i> {dr_lang('可用通配符标签')}</button>
                    <script>
                        function dr_seo_title_rule() {
                            layer.alert('{join}	SEO连接符号，默认“_”<br>'+
                                '[{page}]	分页页码<br>'+
                                '{modulename}	当前模块的名称<br>'+
                                '{catname}	当前栏目名称<br>'+
                                '{catpname}	当前栏目带层次的栏目名称<br>'+
                                '支持“栏目表”任何字段，格式：{字段名}，<br>如：{name}表示栏目名称<br>'+
                                '支持系统常量，格式：{大写的常量名称}，<br>如：{SITE_NAME}表示项目名称<br>'+
                                ''+
                                '', {
                                shade: 0,
                                title: '',
                                btn: []
                            });
                        }
                    </script>
                </span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 control-label">{dr_lang('SEO关键字')}</label>
            <div class="col-md-9">
                <textarea class="form-control " style="height:90px" name="seo[list_keywords]">{$data['setting']['seo']['list_keywords']}</textarea>

            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 control-label">{dr_lang('SEO描述信息')}</label>
            <div class="col-md-9">
                <textarea class="form-control " style="height:90px" name="seo[list_description]">{$data['setting']['seo']['list_description']}</textarea>
            </div>
        </div>

    </div>
</form>

{template "footer.html"}