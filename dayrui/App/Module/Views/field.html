{template "header.html"}
<div class="note note-danger">
    <p><a href="javascript:dr_update_cache();">{dr_lang('更改配置之后需要更新缓存之后才能生效')}</a></p>
    <p style="margin-top:10px;"><a style="color:blue" href="javascript:dr_load_ajax2('{dr_lang('确定要开启更多功能吗？')}', '{dr_url('menu/init')}', 0);">{dr_lang('若需获取更多功能设置，可开启更多功能')}</a></p>
</div>
<script>

    function dr_load_ajax2(msg, url, go) {
        layer.confirm(
            msg,
            {
                icon: 3,
                shade: 0,
                title: lang['ts'],
                btn: [lang['ok'], lang['esc']]
            }, function(index){
                layer.close(index);
                var index = layer.load(2, {
                    shade: [0.3,'#fff'], //0.1透明度的白色背景
                    time: 5000
                });

                $.ajax({type: "GET",dataType:"json", url: url,
                    success: function(json) {
                        layer.close(index);
                        dr_tips(json.code, json.msg);
                        if (json.code > 0) {
                            setTimeout("top.location.reload(true)", 2000)
                        }
                    },
                    error: function(HttpRequest, ajaxOptions, thrownError) {
                        dr_ajax_alert_error(HttpRequest, this, thrownError);
                    }
                });
            });
    }
</script>
<div class="right-card-box">
    <form class="form-horizontal" role="form" id="myform">
        {dr_form_hidden()}
        <div class="table-scrollable">
            <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                <thead>
                <tr class="heading">
                    <th width="200"> {dr_lang('名称')} / {dr_lang('目录')}</th>
                    <th> {dr_lang('操作')} </th>
                </tr>
                </thead>
                <tbody>
                {loop $my $t}
                <tr class="odd gradeX">
                    <td><a {if !$t.share}href="{WEB_DIR}index.php?s={$t.dirname}" target="_blank"{/if}>{$t.name} / {$t.dirname}</a></td>
                    <td>
                        {if $t.install}
                        {if $ci->_is_admin_auth('edit')}
                        <label><a class="btn btn-xs red" href="javascript:dr_iframe('{dr_lang("变更名称")}', '{dr_url("module/module/name_edit", ['dir'=>$t.dirname])}', '450px', '300px');"><i class="fa fa-edit"></i> {dr_lang('更名')}</a></label>
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('配置')}','{dr_url('module/module/edit', ['id'=>$t.id])}', '80%', '90%');" class="btn btn-xs green"> <i class="fa fa-cog"></i> {dr_lang('配置')} </a></label>
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('推荐位')}','{dr_url('module/module/flag_edit', ['id'=>$t.id])}', '80%', '90%');" class="btn btn-xs dark"> <i class="fa fa-flag"></i> {dr_lang('推荐位')} </a></label>
                        {/if}
                        {if $ci->_is_admin_auth()}
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('模块内容字段')}','{dr_url('field/index', ['rname'=>'module', 'rid'=>$t.id])}&is_menu=1', '80%', '90%');" class="btn btn-xs green"> <i class="fa fa-code"></i> {dr_lang('模块内容字段')} </a></label>
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('栏目模型字段')}','{dr_url('field/index', ['rname'=>'catmodule-'.$t.dirname, 'rid'=>0])}&is_menu=1', '80%', '90%');" class="btn btn-xs dark"> <i class="fa fa-reorder"></i> {dr_lang('栏目模型字段')} </a></label>
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('划分栏目模型字段')}','{dr_url('module/module_category/field_index', ['dir'=>$t.dirname])}', '80%', '90%');" class="btn btn-xs red"> <i class="fa fa-cog"></i> {dr_lang('划分栏目模型字段')} </a></label>
                        {/if}
                        {else}
                        {if $ci->_is_admin_auth()}
                        <label><a href="javascript:dr_install_module('{dr_url('module/module/install', ['dir'=>$t.dirname])}', '{intval($t.inde)}');" class="btn btn-xs blue"> <i class="fa fa-plus"></i> {dr_lang('安装')} </a></label>
                        {/if}
                        {/if}
                    </td>
                </tr>
                {/loop}
                </tbody>
            </table>
        </div>


    </form>
</div>

{template "footer.html"}