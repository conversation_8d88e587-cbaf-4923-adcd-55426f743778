// 采集URL功能
function dr_fetch_url() {
    var url = $('#dr_fetch_url').val();
    if (!url) {
        dr_tips(0, '请输入要采集的网址');
        return;
    }
    
    var source = $('#dr_fetch_source').val();
    var loading = layer.load(2, {
        shade: [0.3,'#fff'], //0.1透明度的白色背景
        time: 10000
    });
    
    var app_dir = $('#dr_app_dir').val();
    $.ajax({
        type: "GET",
        url: "/index.php?s="+app_dir+"&c=home&m=fetch_url&url="+encodeURIComponent(url)+"&source="+source,
        dataType: "json",
        success: function(json) {
            layer.close(loading);
            if (json.code == 1) {
                // 填充表单
                if (json.data.title) {
                    $('#dr_title').val(json.data.title);
                }
                if (json.data.description) {
                    // 查找内容编辑器或描述字段
                    if ($('#dr_description').length > 0) {
                        $('#dr_description').val(json.data.description);
                    } else if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances.dr_content) {
                        CKEDITOR.instances.dr_content.setData(json.data.description);
                    } else if (typeof UE !== 'undefined' && UE.getEditor('dr_content')) {
                        UE.getEditor('dr_content').setContent(json.data.description);
                    }
                }
                if (json.data.keywords) {
                    $('#dr_keywords').val(json.data.keywords);
                }
                if (json.data.url) {
                    $('#dr_url').val(json.data.url);
                }
                if (json.data.icon && $('#dr_thumb').length > 0) {
                    $('#dr_thumb').val(json.data.icon);
                    $('.my_thumb_img').attr('src', json.data.icon);
                }
                
                dr_tips(1, '采集成功');
            } else {
                dr_tips(0, json.msg);
            }
        },
        error: function(HttpRequest, ajaxOptions, thrownError) {
            layer.close(loading);
            dr_ajax_alert_error(HttpRequest, this, thrownError);
        }
    });
} 