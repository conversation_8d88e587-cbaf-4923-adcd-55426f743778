{template "header.html"}

<div class="note note-danger">
    <p>
        {template "category_btn.html"}
    </p>
</div>
<div class="note note-danger">
    <p>
        电脑域名绑定到目录：{WEBPATH}category_pc/
    </p>
    <p>
        手机域名绑定到目录：{WEBPATH}category_mobile/
    </p>
</div>
<div class="right-card-box">

    <form class="form-horizontal" role="form" id="myform">
        {dr_form_hidden()}
        <div class="table-scrollable">
            <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                <thead>
                <tr class="heading">
                    <th width="120" style="text-align:center">{dr_lang('ID')}</th>
                    <th width="250">{dr_lang('顶级栏目')}</th>
                    <th >{dr_lang('电脑域名')}</th>
                    {if SITE_IS_MOBILE == 1}
                    <th >{dr_lang('手机域名')}</th>
                    {/if}
                </tr>
                </thead>
                <tbody>
                {loop $data $t}
                <tr>
                    <td style="text-align:center">{$t.id}</td>
                    <td>{$t.name}</td>
                    <td>
                        <div class="input-inline input-medium">
                            <div class="input-group">
                                <input type="text" id="dr_{$t.id}_domain" name="data[{$t.id}][domain]" value="{$t.domain}" class="input-large form-control">
                                <a onclick="dr_test_domain({$t.id}, $('#dr_{$t.id}_domain').val())" class="input-group-addon btn  green">{dr_lang('测试')}</a>
                            </div>
                        </div>

                    </td>
                    {if SITE_IS_MOBILE == 1}
                    <td>
                        <div class="input-inline input-medium">
                            <div class="input-group">
                                <input type="text" name="data[{$t.id}][mobile_domain]" id="dr_{$t.id}_m_domain" value="{$t.mobile_domain}" class="input-large form-control">
                                <a onclick="dr_test_domain({$t.id}, $('#dr_{$t.id}_m_domain').val())" class="input-group-addon btn green">{dr_lang('测试')}</a>
                            </div>
                        </div>
                    </td>
                    {/if}
                </tr>
                {/loop}
                <script>

                    $(function () {
                        $('.table-striped tbody tr:last').attr('style', 'border-bottom-width:0px')
                    });

                    function dr_test_domain(id, value) {
                        var loading = layer.load(2, {
                            shade: [0.3,'#fff'], //0.1透明度的白色背景
                            time: 100000000
                        });
                        $.ajax({type: "GET",dataType:"json", url:"{dr_now_url()}&at=test&id="+id+"&value="+encodeURIComponent(value),
                            success: function(json) {
                                layer.close(loading);
                                dr_tips(json.code, json.msg);
                            },
                            error: function(HttpRequest, ajaxOptions, thrownError) {
                                dr_ajax_admin_alert_error(HttpRequest, ajaxOptions, thrownError)
                            }
                        });
                    }
                </script>
                </tbody>
            </table>
        </div>
        <div class="portlet-body form myfooter">
            <div class="form-actions text-center">
                <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}', 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存')}</button></label>
            </div>
        </div>
    </form>
</div>

{template "footer.html"}