{template "header.html"}


<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="portlet bordered light ">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-cog"></i> {dr_lang('网站域名')} </a>
                </li>
                {if IS_USE_MODULE && $module}
                <li class="{if $page==1}active{/if}">
                    <a href="#tab_1" data-toggle="tab" onclick="$('#dr_page').val('1')"> <i class="fa fa-table"></i> {dr_lang('模块域名')} </a>
                </li>
                {/if}
                <li>
                    <a href="#tab_3" data-toggle="tab" onclick="dr_check_domain()"> <i class="fa fa-refresh"></i> {dr_lang('域名检测')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">

                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('电脑域名')}</label>
                            <div class="col-md-4">
                                <div class="input-group" style="width: 300px;">
                                    <input type="text" {if SITE_ID == 1} readonly{/if} name="data[site_domain]" value="{htmlspecialchars((string)$data['site_domain'])}" class="form-control input-large">
                                    <span class="input-group-btn">
                                        {if SITE_ID == 1}
                                        <a class="btn red" href="javascript:dr_iframe('{dr_lang("变更域名")}', '{dr_url("module/site_domain/edit")}', '450px', '400px');"><i class="fa fa-edit"></i> {dr_lang('变更')}</a>
                                        {else}
                                        <a class="btn blue" href="{SITE_URL}" target="_blank"><i class="fa fa-send"></i> {dr_lang('访问')}</a>
                                        {/if}
                                    </span>
                                </div>
                                <span class="help-block">{dr_lang('域名格式：www.xxx.com')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('手机域名')}</label>
                            <div class="col-md-9">
                                <div class="input-group" style="width: 300px;">
                                <input class="form-control input-large" readonly type="text"  value="{htmlspecialchars((string)$data['mobile_domain'])}"></label>
                                <span class="input-group-btn">
                                        <a class="btn red" href="{dr_url("module/site_mobile/index")}"><i class="fa fa-edit"></i> {dr_lang('变更')}</a>

                                    </span>

                            </div>
                                <span class="help-block">{dr_lang('手机访问时的域名，通常m.xxx.com')}</span>
                            </div>
                        </div>

                        {if SITE_ID > 1}
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('Web目录')}</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <input type="text" name="data[webpath]" id="dr_html_dir" value="{htmlspecialchars((string)$data['webpath'])}" class="form-control">
                                    <span class="input-group-btn">
                                        <button class="btn blue" onclick="dr_test_html_dir('dr_html_dir')" type="button"><i class="fa fa-code"></i> {dr_lang('测试')}</button>
                                    </span>
                                </div>
                                <span class="help-block">{dr_lang('本网站的目录，必须填写一个有效的目录，并设置可写权限')}</span>
                            </div>
                        </div>
                        {/if}
                    </div>
                </div>

                <div class="tab-pane {if $page==1}active{/if}" id="tab_1">
                    <div class="form-body form">


                        {loop $module $dir $t}
                        {if !$t.share}
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('%s电脑域名', $t.name)}</label>
                            <div class="col-md-9">
                                {if $t.error}
                                <span class="help-block" style="color:#ed6b75;margin-top:7px;">{$t.error}</span>
                                {else}
                                <div class="input-group" style="width: 300px;">
                                    <input type="text" name="data[module_{$dir}]" value="{htmlspecialchars((string)$data['module_'.$dir])}" class="form-control">
                                    <span class="input-group-btn">
                                        <a class="btn red" href="javascript:dr_help(750);"><i class="fa fa-book"></i> {dr_lang('说明')}</a>
                                    </span>
                                </div>
                                {/if}
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('%s手机域名', $t.name)}</label>
                            <div class="col-md-9">
                                {if $t.error}
                                <span class="help-block" style="color:#ed6b75;margin-top:7px;">{$t.error}</span>
                                {else}
                                <div class="input-group" style="width: 300px;">
                                    <input type="text" name="data[module_mobile_{$dir}]" value="{htmlspecialchars((string)$data['module_mobile_'.$dir])}" class="form-control">
                                    <span class="input-group-btn">
                                        <a class="btn red" href="javascript:dr_help(750);"><i class="fa fa-book"></i> {dr_lang('说明')}</a>
                                    </span>
                                </div>
                                <span class="help-block">{dr_lang('如果绑定了手机域名，这里必须要绑定域名')}</span>
                                {/if}
                            </div>
                        </div>
                        <div class="form-group" style="margin-bottom: 30px;">
                            <label class="col-md-2 control-label">{dr_lang('%s模块Web目录', $t.name)}</label>
                            <div class="col-md-9">
                                {if $t.error}
                                <span class="help-block" style="color:#ed6b75;margin-top:7px;">{$t.error}</span>
                                {else}
                                <div class="input-group">
                                    <input type="text" name="data[webpath_{$dir}]" id="dr_html_dir_{$dir}" value="{htmlspecialchars((string)$data['webpath_'.$dir])}" class="form-control">
                                    <span class="input-group-btn">
                                        <button class="btn blue" onclick="dr_test_html_dir('dr_html_dir_{$dir}')" type="button"><i class="fa fa-code"></i> {dr_lang('测试')}</button>
                                    </span>
                                </div>
                                <span class="help-block">{dr_lang('本模块的Web目录，必须填写一个有效的目录，并设置可写权限')}</span>
                                {/if}
                            </div>
                        </div>
                        {/if}
                        {/loop}

                    </div>
                </div>

                <div class="tab-pane " id="tab_3">
                    <div class="form-body form">

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('域名检测结果')}</label>
                            <div class="col-md-9">
                                <div id="dr_domain" style="margin-top: -12px;">

                                </div>
                            </div>
                        </div>


                    </div>
                    </iv>

                </div>
            </div>
        </div>

        <div class="portlet-body form myfooter">
            <div class="form-actions text-center">
                <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
            </div>
        </div>
</form>

</div>
<form  class="form-horizontal">

    <div class="portlet bordered light myfbody">
        <div class="portlet-title">
            <div class="caption">
                <span class="caption-subject">{dr_lang('服务器绑定域名说明')}</span>
                <span class="caption-helper">{dr_lang('以下域名需要在服务器上进行绑定，也可以找空间商帮你操作')}</span>
            </div>
        </div>
        <div class="portlet-body">
            <div class="form-body">


                {if $data['site_domain']}
                <div class="form-group">
                    <label class="col-md-2 control-label">{dr_lang('电脑域名')}</label>
                    <div class="col-md-9">
                        <span class="form-control-static">{$data['site_domain']}</span>
                        <span class="help-block">{dr_lang('绑定目录：')}{php echo \Phpcmf\Service::L('html')->get_webpath(SITE_ID, 'site', '');}</span>
                    </div>
                </div>
                {/if}
                {if $data['mobile_domain']}
                <div class="form-group">
                    <label class="col-md-2 control-label">{dr_lang('手机域名')}</label>
                    <div class="col-md-9">
                        <span class="form-control-static">{$data['mobile_domain']}</span>
                        <span class="help-block">{dr_lang('绑定目录：')}{php echo \Phpcmf\Service::L('html')->get_webpath(SITE_ID, 'site', SITE_MOBILE_DIR.'/');}</span>
                    </div>
                </div>
                {/if}


                {loop $module $dir $t}
                {if !$t.error}
                {if $data['module_'.$dir]}
                <div class="form-group">
                    <label class="col-md-2 control-label">{dr_lang('%s电脑域名', $t.name)}</label>
                    <div class="col-md-9">
                        <span class="form-control-static">{$data['module_'.$dir]}</span>
                        <span class="help-block">{dr_lang('绑定目录：')}{php echo \Phpcmf\Service::L('html')->get_webpath(SITE_ID, $dir, '');}</span>
                    </div>
                </div>
                {/if}
                {if $data['module_mobile_'.$dir]}
                <div class="form-group">
                    <label class="col-md-2 control-label">{dr_lang('%s手机域名', $t.name)}</label>
                    <div class="col-md-9">
                        <span class="form-control-static">{$data['module_mobile_'.$dir]}</span>
                        <span class="help-block">{dr_lang('绑定目录：')}{php echo \Phpcmf\Service::L('html')->get_webpath(SITE_ID, $dir, 'mobile/');}</span>
                    </div>
                </div>
                {/if}

                {/if}
                {/loop}


            </div>
        </div>
    </div>


</form>


<script>
    function dr_check_domain() {
        $.ajax({
            type: "POST",
            dataType: "text",
            url: "{dr_url('api/domain')}",
            data: $("#myform").serialize(),
            success: function(html) {
                $("#dr_domain").html(html);
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);;
            }
        });
    }

</script>

{template "footer.html"}