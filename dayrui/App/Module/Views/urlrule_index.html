{template "header.html"}


<div class="right-card-box">

    <form class="form-horizontal" role="form" id="myform">
        {dr_form_hidden()}
        {if $ci->_is_admin_auth('del')}
        <div class="bootstrap-table bootstrap-table2">
            <div id="toolbar" class="toolbar">
                <label><button type="button" onclick="dr_ajax_option('{dr_url($uriprefix.'/del')}', '{dr_lang('你确定要删除它们吗？')}', 1)" class="btn red btn-sm"> <i class="fa fa-trash"></i> {dr_lang('删除')}</button>
                </label>
                {if !IS_OEM_CMS}
                <label><button type="button" onclick="dr_help(1210)" class="btn default btn-sm"> <i class="fa fa-book"></i> {dr_lang('常用的规则代码')}</button>
                </label>
                {/if}
            </div>
        </div>
        <div class="clearfix"></div>
        <div class="table-scrollable table-clearfix">
            {else}
            <div class="table-scrollable">
                {/if}
            <table class="table table-noborder table-striped table-bordered table-hover table-checkable dataTable">
                <thead>
                <tr class="heading">
                    {if $ci->_is_admin_auth('del')}
                    <th class="myselect">
                        <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                            <input type="checkbox" class="group-checkable" data-set=".checkboxes" />
                            <span></span>
                        </label>
                    </th>
                    {/if}
                    <th style="text-align:center" class="{dr_sorting('type')}" name="type" width="140">{dr_lang('类型')}</th>
                    <th class="{dr_sorting('name')}" name="name">{dr_lang('名称')}</th>
                    <th>{dr_lang('操作')}</th>
                </tr>
                </thead>
                <tbody>
                {loop $list $t}
                <tr class="odd gradeX" id="dr_row_{$t.id}">
                    {if $ci->_is_admin_auth('del')}
                    <td class="myselect">
                        <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                            <input type="checkbox" class="checkboxes" name="ids[]" value="{$t.id}" />
                            <span></span>
                        </label>
                    </td>
                    {/if}
                    <td style="text-align:center"> <span class="badge badge-{$color[$t['type']]}"> {$ci->type[$t['type']]} </span></td>
                    <td>{$t.name}</td>
                    <td>
                        {if $ci->_is_admin_auth('edit')}
                        <label><a href="{dr_url($uriprefix.'/edit', ['id'=>$t.id])}" class="btn btn-xs green"> <i class="fa fa-edit"></i> {dr_lang('修改')}</a></label>
                        <label><a href="javascript:dr_admin_menu_ajax('{dr_url($uriprefix.'/copy_edit', ['id'=>$t.id])}');" class="btn btn-xs dark"> <i class="fa fa-copy"></i> {dr_lang('复制')}</a></label>
                        <label><button type="button" onclick="javascript:dr_iframe_show('{dr_lang('导出')}', '{dr_url($uriprefix.'/export_edit', ['id'=>$t.id])}');" class="btn blue btn-xs"> <i class="fa fa-mail-forward"></i> {dr_lang('导出')}</button></label>
                        {/if}
                    </td>
                </tr>
                {/loop}
                </tbody>
            </table>
        </div>

         <div class="row fc-list-footer table-checkable ">
             <div class="col-md-12 fc-list-page">
                 {$mypages}
             </div>
         </div>
    </form>

</div>
{template "footer.html"}