{template "header.html"}

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="myfbody">
    <div class="portlet bordered light">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-cog"></i> {dr_lang('模块设置')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">

                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">
                        <div class="form-group" id="dr_row_name">
                            <label class="col-md-2 control-label ">{dr_lang('模块名称')}</label>
                            <div class="col-md-9">
                                <label><input type="text" class="form-control" id="dr_name" name="data[name]"></label>
                                <span class="help-block"> {dr_lang('模块的描述名称')} </span>
                            </div>
                        </div>

                        <div class="form-group" id="dr_row_dirname">
                            <label class="col-md-2 control-label ">{dr_lang('目录名称')}</label>
                            <div class="col-md-9">
                                <label><input type="text" class="form-control" id="dr_dirname" name="data[dirname]" value=""></label>
                                <span class="help-block"> {dr_lang('只能由字母构成')} </span>
                            </div>
                        </div>

                        <div class="form-group" id="dr_row_icon">
                            <label class="col-md-2 control-label ">{dr_lang('模块图标')}</label>
                            <div class="col-md-9">
                                <div class="input-group" style="width:250px">
                                    <input class="form-control" id="dr_icon" placeholder="fa fa-code" type="text" name="data[icon]" />
                                    <span class="input-group-btn">
                                        <a class="btn btn-success" href="{dr_url('api/icon')}" target="_blank"><i class="fa fa-arrow-right fa-fw" /></i> {dr_lang('查看')}</a>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label ">{dr_lang('模块位置')}</label>
                            <div class="col-md-9">
                                <p class="form-control-static"> {APPSPATH}</p>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('创建模块')}</button></label>
        </div>
    </div>
</form>

{template "footer.html"}