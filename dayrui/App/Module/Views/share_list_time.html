{template "header.html"}

{template "api_list_date_search.html"}

<div class="note note-danger" id="table-search-tool">

    <div class="row table-search-tool">
        <form action="{SELF}" method="get">
            {dr_form_search_hidden(['field' => 'content'])}
            {if $is_category_show}
            <div class="col-md-12 col-sm-12">
                <label>
                    {$category_select}
                </label>
            </div>
            {/if}
            <div class="col-md-12 col-sm-12">
                <label><input type="text" class="form-control" placeholder="{dr_lang('全文搜索')}" value="{$param['keyword']}" name="keyword" /></label>
            </div>
            <div class="col-md-12 col-sm-12">
                <label>
                    <div class="input-group input-medium date-picker input-daterange" data-date="" data-date-format="yyyy-mm-dd">
                        <input type="text" class="form-control" value="{$param.date_form}" name="date_form">
                        <span class="input-group-addon"> {dr_lang('到')} </span>
                        <input type="text" class="form-control" value="{$param.date_to}" name="date_to">
                    </div>
                </label>
            </div>

            <div class="col-md-12 col-sm-12">
                <label><button type="submit" class="btn blue btn-sm onloading" name="submit" > <i class="fa fa-search"></i> {dr_lang('搜索')}</button></label>
            </div>
        </form>
    </div>
</div>

<div class="right-card-box">


<form class="form-horizontal" role="form" id="myform">
    {dr_form_hidden()}
    <div class="bootstrap-table bootstrap-table2">
        <div id="toolbar" class="toolbar">
            {if $ci->_is_admin_auth('del')}
            <label><button type="button" onclick="dr_ajax_option('{dr_url($uriprefix.'/del')}', '{dr_lang('你确定要删除它们吗？')}', 1)" class="btn red btn-sm"> <i class="fa fa-trash"></i> {dr_lang('删除')}</button></label>
            {/if}
            {if $ci->_is_admin_auth('edit')}
            <label><button type="button" onclick="dr_ajax_option('{dr_url($uriprefix.'/add')}&at=post', '{dr_lang('你确定要批量发布它们吗？')}', 1)" class="btn green btn-sm"> <i class="fa fa-post"></i> {dr_lang('批量立即发布')}</button></label>
            {/if}
        </div>
    </div><div class="clearfix"></div>
    <div class="table-scrollable table-clearfix">
        <table class="table table-striped table-bordered table-bordered2 table-hover table-checkable dataTable">
            <thead>
            <tr class="heading">
                {if $ci->_is_admin_auth('del')}
                <th class="myselect">
                    <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                        <input type="checkbox" class="group-checkable" data-set=".checkboxes" />
                        <span></span>
                    </label>
                </th>
                {/if}
                <th class="{dr_sorting('id')}" name="id">{dr_lang('主题')}</th>
                <th class="{dr_sorting('catid')}" name="cait" width="150">{dr_lang('栏目')}</th>
                <th class="{dr_sorting('uid')}" name="uid" width="150">{dr_lang('发布者')}</th>
                <th class="{dr_sorting('posttime')}" name="posttime" width="170">{dr_lang('定时发布时间')}</th>
                <th>{dr_lang('操作')}</th>
            </tr>
            </thead>
            <tbody>
            {loop $list $t}
            <?php
            $c=dr_string2array($t.content);
            $t['url'] = SITE_URL.'index.php?s='.APP_DIR.'&c=show&m=time&id='.$t['id'];
            $t['url'] = ADMIN_URL.(dr_url('api/slogin', ['url' => urlencode($t['url'])]));
            ?>
            <tr class="odd gradeX" id="dr_row_{$t.id}">
                {if $ci->_is_admin_auth('del') || $ci->_is_admin_auth('edit')}
                <td class="myselect">
                    <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                        <input type="checkbox" class="checkboxes" name="ids[]" value="{$t.id}" />
                        <span></span>
                    </label>
                </td>
                {/if}
                <td>{Function_list::title($c.title, null, $t)}</td>
                <td>{Function_list::catid($t.catid, null, $t)}</td>
                <td>{Function_list::uid($t.uid, null, $t)}</td>
                <td>{dr_date($t.posttime, null, 'red')}</td>
                <td>
                    {if $ci->_is_admin_auth('edit')}
                    <label><a href="{dr_url($uriprefix.'/edit', ['id'=>$t.id])}" class="btn btn-xs green"> <i class="fa fa-edit"></i> {dr_lang('修改内容')}</a></label>
                    <label><a href="javascript:dr_iframe('{dr_lang('修改发布时间')}', '{dr_url($uriprefix.'/time_edit', ['id'=>$t.id])}', 0, '450px');" class="btn btn-xs blue"> <i class="fa fa-clock-o"></i> {dr_lang('修改发布时间')}</a></label>
                    {/if}
                    <label style="color:red">{$t.result}</label>
                </td>
            </tr>
            {/loop}
            </tbody>
        </table>
    </div>

    {if $mypages}
         <div class="row fc-list-footer table-checkable ">

             <div class="col-md-12 fc-list-page">
                 {$mypages}
             </div>
         </div>
    {/if}
</form>
</div>

{template "footer.html"}