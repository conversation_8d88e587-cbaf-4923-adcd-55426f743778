{template "header.html"}


<div class="note note-danger">
    <p> {template "category_btn.html"}</p>
</div>

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {dr_form_hidden()}
    <div class="myfbody">
    <div class="portlet bordered light ">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class=" {if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-table"></i> {dr_lang('栏目属性设置')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">



                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">

                        {if !APP_DIR}
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('树形栏目状态')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[tree_open]" value="1" {if $data['tree_open']}checked{/if} /> {dr_lang('展开')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[tree_open]" value="0" {if empty($data['tree_open'])}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>

                                <span class="help-block">{dr_lang('聚合内容页面左侧树形栏目是否全部展开')}</span>
                            </div>
                        </div>
                        {/if}
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('栏目选择框')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[ld]" value="1" {if $data['ld']}checked{/if} /> {dr_lang('折叠弹窗模式')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[ld]" value="0" {if empty($data['ld'])}checked{/if} /> {dr_lang('经典模式')} <span></span></label>
                                </div>

                                <span class="help-block">{dr_lang('当栏目数据过多时，建议设置为折叠弹窗模式')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('默认展开顶级栏目下层')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[popen]" value="1" {if $data['popen']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[popen]" value="0" {if empty($data['popen'])}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>

                                <span class="help-block">{dr_lang('进入栏目管理时默认展开顶级栏目的下级子栏目')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('栏目目录允许重复')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[rname]" value="1" {if $data['rname']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[rname]" value="0" {if empty($data['rname'])}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('栏目开启之后请不要使用目录作为伪静态关键字，否则需要使用生成真静态功能')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('外链地址补全')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[linkfix]" value="1" {if $data['linkfix']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[linkfix]" value="0" {if empty($data['linkfix'])}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('当外链地址是相当路径时，开启后将当前域名自动补全到URL中')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('栏目列表数量统计')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[total]" value="1" {if $data['total']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[total]" value="0" {if empty($data['total'])}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('进入栏目管理时可以看到栏目的文章数量，当栏目过多时建议关闭此选项，会影响加载速度')}</span>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('后台列表显示字段')}</label>
                            <div class="col-md-9">

                                <div class="table-scrollable">

                                <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                                    <thead>
                                    <tr class="heading">
                                        <th class="myselect">
                                            {dr_lang('显示')}
                                        </th>
                                        <th width="180"> {dr_lang('字段')} </th>
                                        <th> {dr_lang('说明')} </th>
                                    </tr>
                                    </thead>
                                    <tbody class="field-sort-items2">
                                    {loop $sysfield $n $t}
                                    <tr class="odd gradeX">
                                        <td class="myselect">
                                            <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                                                <input type="checkbox" class="checkboxes" name="data[sys_field][]" value="{$n}" {if dr_in_array($n, $data['sys_field'])} checked{/if} />
                                                <span></span>
                                            </label>
                                        </td>
                                        <td>{dr_lang($t[0])}</td>
                                        <td>{dr_lang($t[1])}</td>
                                    </tr>
                                    {/loop}
                                    </tbody>
                                </table></div>

                                    <div class="table-scrollable" style="margin-top: 30px">
                                    <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                                        <thead>
                                        <tr class="heading">
                                            <th class="myselect">
                                                {dr_lang('显示')}
                                            </th>
                                            <th width="180"> {dr_lang('字段')} </th>
                                            <th width="150"> {dr_lang('名称')} </th>
                                            <th width="100"> {dr_lang('宽度')} </th>
                                            <th width="140"> {dr_lang('对其方式')} </th>
                                            <th> {dr_lang('回调方法')} </th>
                                        </tr>
                                        </thead>

                                        <tbody class="field-sort-items">
                                        {loop $field $n $t}
                                        <tr class="odd gradeX">
                                            <td class="myselect">
                                                <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                                                    <input type="checkbox" class="checkboxes" name="data[list_field][{$t.fieldname}][use]" value="1" {if $data['list_field'][$t.fieldname]['use']} checked{/if} />
                                                    <span></span>
                                                </label>
                                            </td>
                                            <td>{dr_lang($t.name)} ({$t.fieldname})</td>
                                            <td><input class="form-control" type="text" name="data[list_field][{$t.fieldname}][name]" value="{php echo $data['list_field'][$t.fieldname]['name'] ? htmlspecialchars($data['list_field'][$t.fieldname]['name']) : $t.name}" /></td>
                                            <td> <input class="form-control" type="text" name="data[list_field][{$t.fieldname}][width]" value="{htmlspecialchars((string)$data['list_field'][$t.fieldname]['width'])}" /></td>
                                            <td><input type="checkbox" name="data[list_field][{$t.fieldname}][center]" {if $data['list_field'][$t.fieldname]['center']} checked{/if} value="1"  data-on-text="{dr_lang('居中')}" data-off-text="{dr_lang('默认')}" data-on-color="success" data-off-color="danger" class="make-switch" data-size="small">
                                            </td>
                                            <td> <div class="input-group" style="width:250px">
                                        <span class="input-group-btn">
                                            <a class="btn btn-success" href="javascript:dr_call_alert();">{dr_lang('回调')}</a>
                                        </span>
                                                <input class="form-control" type="text" name="data[list_field][{$t.fieldname}][func]" value="{htmlspecialchars((string)$data['list_field'][$t.fieldname]['func'])}" />
                                            </div></td>
                                        </tr>
                                        {/loop}
                                        </tbody>
                                    </table>
                            </div>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('栏目列表名称长度')}</label>
                            <div class="col-md-9">
                                <label><input class="form-control" type="text" name="data[name_size]" value="{intval((string)$data.name_size)}"></label>
                                <span class="help-block">{dr_lang('在后台栏目列表处显示的名称长度控制值，0表示不限制')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('栏目数量阈值')}</label>
                            <div class="col-md-9">
                                <label><input class="form-control" type="text" name="data[MAX_CATEGORY]" value="{php echo isset($data['MAX_CATEGORY']) ? intval((string)$data.MAX_CATEGORY) : MAX_CATEGORY;}"></label>
                                <span class="help-block">{dr_lang('当栏目总数量在阈值范围内时，变动栏目时系统会自动更新缓存，可能会造成延迟情况')}</span>
                                <span class="help-block">{dr_lang('当栏目数量大于阈值时，系统将不会自动更新缓存，需要手动更新缓存才会生效')}</span>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
    </div>
    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
        </div>
    </div>
</form>
<script type="text/javascript">
    $(function() {
        $(".field-sort-items").sortable();
    });
</script>
{template "footer.html"}