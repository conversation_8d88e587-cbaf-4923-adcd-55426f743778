{template "header.html"}

<form class="form-horizontal" role="form" id="myform">
    {$form}
    <div class="form-body">
        <div class="form-group">
            <label class="control-label col-xs-3 ajax_name"> {dr_lang('发布时间')}</label>
            <div class="col-xs-9">
                <span class="form-date input-group">
                    <link href="{THEME_PATH}assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
                    <link href="{THEME_PATH}assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
                    <link href="{THEME_PATH}assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
                    <script src="{THEME_PATH}assets/global/plugins/moment.min.js" type="text/javascript"></script>
                    <script src="{THEME_PATH}assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.finecms.js" type="text/javascript"></script>
                    <script src="{THEME_PATH}assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.finecms.js" type="text/javascript"></script>
                    <div class="input-group date field_date_inputtime">
                        <input id="posttime" name="posttime" type="text" style="width:160px;" value="{$posttime}"  required="required" class="form-control ">
                        <span class="input-group-btn">
                            <button class="btn default date-set" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </span>
                    </div>
                    <script>
                    $(function(){
                        $(".field_date_inputtime").datetimepicker({
                            isRTL: App.isRTL(),
                            format: "yyyy-mm-dd hh:ii:ss",
                            showMeridian: true,
                            autoclose: true,
                            pickerPosition: (App.isRTL() ? "bottom-right" : "bottom-left"),
                            todayBtn: true
                        });
                    });
                    </script>
                </span>
                <span class="help-block">{dr_lang('定时时间不能晚于当前时间')}</span>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-3 ajax_name"> {dr_lang('功能提醒')}</label>
            <div class="col-xs-9">
                <div class="form-control-static">{dr_lang('服务器需要开启自动任务功能')}</div>
            </div>
        </div>

    </div>
</form>

{template "footer.html"}