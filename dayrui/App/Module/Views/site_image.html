{template "header.html"}
<div class="note note-danger">
    <p>{dr_lang('水印仅限于本地存储的图片文件，此处设置针对远程图片无效')}</p>
</div>


<link href="{ROOT_THEME_PATH}assets/global/plugins/jquery-fileupload/css/jquery.fileupload.css?v={CMF_UPDATE_TIME}" rel="stylesheet" type="text/css" />
<script src="{ROOT_THEME_PATH}assets/global/plugins/jquery-fileupload/js/jquery.fileupload.min.js?v={CMF_UPDATE_TIME}" type="text/javascript"></script>

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="myfbody">
    <div class="portlet bordered light">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-photo"></i> {dr_lang('水印设置')} </a>
                </li>
                <li class="{if $page==1}active{/if}">
                    <a href="#tab_1" data-toggle="tab" onclick="$('#dr_page').val('1')"> <i class="fa fa-cog"></i> {dr_lang('缩略图设置')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">


                <div class="tab-pane {if $page==1}active{/if}" id="tab_1">
                    <div class="form-body">
						
						<div class="form-group " >
                            <label class="col-md-2 control-label">{dr_lang('缩略图格式')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="image[ext]" value="0" {if empty($image['ext'])}checked{/if} /> JPG <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="image[ext]" value="1" {if $image['ext']}checked{/if} /> WEBP <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('缩略图扩展名格式选择')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('缩略图存储目录')}</label>
                            <div class="col-md-9">
                                <div class="input-group input-xlarge">
                                    <input class="form-control " type="text" id="dr_cache_dir" name="image[cache_path]" value="{htmlspecialchars((string)$image['cache_path'])}" >
                                    <span class="input-group-btn">
                                            <button class="btn blue" onclick="dr_test_domain_dir('dr_cache_dir')" type="button"><i class="fa fa-code"></i> {dr_lang('测试')}</button>
                                        </span>
                                </div>
                                <span class="help-block">{dr_lang('绝对路径请以“/”开头，默认uploadfile/thumb/')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('缩略图访问URL地址')}</label>
                            <div class="col-md-9">
                                <div class="input-group input-xlarge">
                                    <input class="form-control " id="dr_cache_url" type="text" name="image[cache_url]" value="{htmlspecialchars((string)$image['cache_url'])}" >
                                    <span class="input-group-btn">
                                            <button class="btn blue" onclick="dr_test_thumb_domain()" type="button"><i class="fa fa-wrench"></i> {dr_lang('检测')}</button>
                                        </span>
                                </div>

                                <span class="help-block">{dr_lang('缩略图文件访问地址，可单独指定域名，默认/uploadfile/thumb/')}</span>
                            </div>
                        </div>

                        <div class="form-group" style="display: none" id="dr_test_thumb_domain">
                            <label class="col-md-2 control-label">{dr_lang('目录检测结果')}</label>
                            <div class="col-md-9" style="padding-top: 3px; line-height: 25px; color:green" id="dr_test_thumb_domain_result">

                            </div>
                        </div>
                        

                    </div>
                </div>

                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('水印位置')}</label>
                            <div class="col-md-9">
                                <div class="btn-group fc-3x3" data-toggle="buttons">
                                    {loop $locate $i $t}
                                    <label class="btn btn-default  {if $data.locate == $i} active{/if}  {if dr_strpos($i, 'bottom')!==false} btn2{/if}">
                                        <input type="radio" name="data[locate]" {if $data.locate == $i}checked{/if} value="{$i}" class="toggle"> {dr_lang($t)}
                                    </label>
                                    {/loop}
                                </div>
                                <span class="help-block">{dr_lang('选择水印在图片中的位置')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('水印类型')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" onclick="dr_type(0)" name="data[type]" value="0" {if empty($data['type'])}checked{/if} /> {dr_lang('图片')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" onclick="dr_type(1)" name="data[type]" value="1" {if $data['type']}checked{/if} /> {dr_lang('文字')} <span></span></label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group dr_sy dr_sy_1">
                            <label class="col-md-2 control-label">{dr_lang('文字字体')}</label>
                            <div class="col-md-9">
                                <label><select class="form-control" name="data[wm_font_path]">
                                    {loop $waterfile $t}
                                    {if strpos($t, '.ttf') !== false}
                                    <option {if $t==$data['wm_font_path']} selected=""{/if} value="{$t}">{$t}</option>
                                    {/if}
                                    {/loop}
                                </select></label>
                                <label class="wm-fileupload-font"><div style="margin-bottom:5px;" class="btn green btn-sm fileinput-button"><i class="fa fa-plus"></i> <span>{dr_lang('上传')}</span> <input type="file" name="file_data"> </div> </label>
                                <span class="help-block">{dr_lang('自定义水印字体文件cache/watermark/***.ttf')}</span>
                            </div>
                        </div>
                        <div class="form-group dr_sy dr_sy_1">
                            <label class="col-md-2 control-label">{dr_lang('水印文字')}</label>
                            <div class="col-md-5">
                                <input name="data[wm_text]" value="{htmlspecialchars((string)$data['wm_text'])}" type="text" class="form-control">
                                <span class="help-block">{dr_lang('如果为中文，先要水印目录中添加中文字体')}</span>
                            </div>
                        </div>
                        <div class="form-group dr_sy dr_sy_1">
                            <label class="col-md-2 control-label">{dr_lang('水印文字大小')}</label>
                            <div class="col-md-9">
                                <label><input name="data[wm_font_size]" value="{intval($data['wm_font_size'])}" type="text" class="form-control"></label>
                                <span class="help-block">{dr_lang('字体大小，单位px像素')}</span>
                            </div>
                        </div>
                        <div class="form-group dr_sy dr_sy_1">
                            <label class="col-md-2 control-label">{dr_lang('水印文字颜色')}</label>
                            <div class="col-md-9" style="padding-top: 2px;">
                                <label><input type="hidden" id="hue-demo" data-control="hue" class="form-control demo" name="data[wm_font_color]" value="{htmlspecialchars((string)$data['wm_font_color'])}"></label>
                            </div>
                        </div>

                        <div class="form-group dr_sy dr_sy_0">
                            <label class="col-md-2 control-label">{dr_lang('水印文件')}</label>
                            <div class="col-md-9">
                                <label><select class="form-control" name="data[wm_overlay_path]">
                                    {loop $waterfile $t}
                                    {if strpos($t, '.jpg') !== false || strpos($t, '.jpeg') !== false || strpos($t, '.png') !== false}
                                    <option {if $t==$data['wm_overlay_path']} selected=""{/if} value="{$t}">{$t}</option>
                                    {/if}
                                    {/loop}
                                </select></label>
                                <label class="wm-fileupload-img"><div style="margin-bottom:5px;" class="btn green btn-sm fileinput-button"><i class="fa fa-plus"></i> <span>{dr_lang('上传')}</span> <input type="file" name="file_data"> </div> </label>

                                <span class="help-block">{dr_lang('自定义图片水印文件位于/cache/watermark/')}</span>
                            </div>
                        </div>

                        <div class="form-group dr_sy dr_sy_0">
                            <label class="col-md-2 control-label">{dr_lang('水印图片透明度')}</label>
                            <div class="col-md-9">
                                <label><input name="data[wm_opacity]" value="{intval($data.wm_opacity)}" type="text" class="form-control" placeholder="{dr_lang('px')}"></label>
                                <span class="help-block">{dr_lang('设置范围：1~100，表示水印图片的透明度数，可以预览看效果')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('水印图片内边距')}</label>
                            <div class="col-md-9">
                                <label><input name="data[wm_padding]" value="{intval($data.wm_padding)}" type="text" class="form-control" placeholder="{dr_lang('px')}"></label>
                                <span class="help-block">{dr_lang('内边距，以像素为单位，是水印与图片边缘之间的距离')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('水印图片偏移量')}</label>
                            <div class="col-md-9">
                                <div class="input-inline input-small">
                                    <div class="input-group">
                                        <span class="input-group-addon">{dr_lang('水平')}</span>
                                        <input type="text"  name="data[wm_hor_offset]" value="{intval($data.wm_hor_offset)}" class="form-control" placeholder="px">
                                    </div>
                                </div>
                                <div class="input-inline input-small">
                                    <div class="input-group">
                                        <span class="input-group-addon">{dr_lang('垂直')}</span>
                                        <input type="text"  name="data[wm_vrt_offset]" value="{intval($data.wm_vrt_offset)}" class="form-control" placeholder="px">
                                    </div>
                                </div>
                            </div>
                        </div>
						
						<div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('图片大小限制')}</label>
                            <div class="col-md-9">
                                <div class="input-inline input-large">
                                    <div class="input-group">
                                        <span class="input-group-addon">{dr_lang('宽大于')}</span>
                                        <input type="text"  name="data[width]" value="{intval($data.width)}" class="form-control" placeholder="px">
                                    </div>
                                </div>
                                <div class="input-inline input-large">
                                    <div class="input-group">
                                        <span class="input-group-addon">{dr_lang('高大于')}</span>
                                        <input type="text"  name="data[height]" value="{intval($data.height)}" class="form-control" placeholder="px">
                                    </div>
                                </div>
                               <span class="help-block">{dr_lang('对超过设定值的图片附件加水印，例如小尺寸的图片不水印')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('编辑器水印')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[ueditor]" value="0" {if empty($data['ueditor'])}checked{/if} /> {dr_lang('按编辑器属性')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[ueditor]" value="1" {if $data['ueditor']}checked{/if} /> {dr_lang('全部水印')} <span></span></label>
                                </div>
								 <span class="help-block">{dr_lang('是否对编辑器上传的图片进行强制水印')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('缩略图水印')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[thumb]" value="0" {if empty($data['thumb'])}checked{/if} /> {dr_lang('按调用参数')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[thumb]" value="1" {if $data['thumb']}checked{/if} /> {dr_lang('全部水印')} <span></span></label>
                                </div>
								 <span class="help-block">{dr_lang('是否对缩略图函数dr_thumb的图片进行强制水印')}</span>
                            </div>
                        </div>


                    </div>
                </div>



            </div>
        </div>
    </div>
    </div>
    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存')}</button></label>
            <label><button type="button" onclick="dr_preview()" class="btn red"> <i class="fa fa-photo"></i> {dr_lang('预览水印')}</button></label>
        </div>
    </div>
</form>

<link href="{THEME_PATH}assets/global/plugins/bootstrap-colorpicker/css/colorpicker.css" rel="stylesheet" type="text/css" />
<link href="{THEME_PATH}assets/global/plugins/jquery-minicolors/jquery.minicolors.css" rel="stylesheet" type="text/css" />
<script src="{THEME_PATH}assets/global/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.js" type="text/javascript"></script>
<script src="{THEME_PATH}assets/global/plugins/jquery-minicolors/jquery.minicolors.min.js" type="text/javascript"></script>
<script type="text/javascript">

function dr_type(v) {
    $('.dr_sy').hide();
    $('.dr_sy_'+v).show();
}
function dr_preview() {

    layer.open({
        type: 2,
        title: '{dr_lang('水印预览')}',
        fix:true,
        scrollbar: false,
        shadeClose: true,
        shade: 0,
        area: [{php echo \Phpcmf\Service::IS_PC_USER() ? '\'50%\', \'60%\'' : '"95%", "90%"'}],
        success: function(layero, index){
            var body = layer.getChildFrame('body', index);
            $(body).attr("style", "text-align:center");
        },
        content: '{dr_url("api/preview")}&'+$("#myform").serialize()+'&is_ajax=1'
    });
}

$(function(){

    // 初始化上传组件
    $('.wm-fileupload-font').fileupload({
        disableImageResize: false,
        autoUpload: true,
        maxFileSize: 2,
        url: '{dr_url("module/site_image/upload_index")}&at=font',
        dataType: 'json',
        formData : {
            '{csrf_token()}': '{csrf_hash()}',
        },
        progressall: function (e, data) {
            // 上传进度条 all
            var progress = parseInt(data.loaded / data.total * 100, 10);
            layer.msg(progress+'%');
        },
        add: function (e, data) {
            data.submit();
        },
        done: function (e, data) {
            //console.log($(this).html());
            dr_tips(data.result.code, data.result.msg);
            if (data.result.code) {
                setTimeout("window.location.reload(true)", 2000);
            }

        },
    });

    // 初始化上传组件
    $('.wm-fileupload-img').fileupload({
        disableImageResize: false,
        autoUpload: true,
        maxFileSize: 2,
        url: '{dr_url("module/site_image/upload_index")}&at=img',
        dataType: 'json',
        formData : {
            '{csrf_token()}': '{csrf_hash()}',
        },
        progressall: function (e, data) {
            // 上传进度条 all
            var progress = parseInt(data.loaded / data.total * 100, 10);
            layer.msg(progress+'%');
        },
        add: function (e, data) {
            data.submit();
        },
        done: function (e, data) {
            //console.log($(this).html());
            dr_tips(data.result.code, data.result.msg);
            if (data.result.code) {
                setTimeout("window.location.reload(true)", 2000);
            }

        },
    });

    $("#hue-demo").minicolors({
        control: $(this).attr('data-control') || 'hue',
        defaultValue: $(this).attr('data-defaultValue') || '',
        inline: $(this).attr('data-inline') === 'true',
        letterCase: $(this).attr('data-letterCase') || 'lowercase',
        opacity: $(this).attr('data-opacity'),
        position: $(this).attr('data-position') || 'bottom left',
        change: function(hex, opacity) {
            if (!hex) return;
            if (opacity) hex += ', ' + opacity;
            if (typeof console === 'object') {
                console.log(hex);
            }
        },
        theme: 'bootstrap'
    });


    dr_type({intval($data['type'])});

});

function dr_test_thumb_domain() {
    // 延迟加载
    var loading = layer.load(2, {
        shade: [0.3,'#fff'], //0.1透明度的白色背景
        time: 5000
    });
    $('#dr_test_domain').hide();
    $.ajax({type: "POST",dataType:"json", url: admin_file+"?c=api&m=test_thumb_domain", data: $('#myform').serialize(),
        success: function(json) {
            layer.close(loading);
            $('#dr_test_thumb_domain').show();
            $('#dr_test_thumb_domain_result').html(json.msg);
            return false;
        },
        error: function(HttpRequest, ajaxOptions, thrownError) {
            dr_ajax_admin_alert_error(HttpRequest, ajaxOptions, thrownError);
        }
    });
}
function dr_test_domain_dir(id) {
    $.ajax({type: "GET",dataType:"json", url: admin_file+"?c=api&m=test_attach_dir&v="+encodeURIComponent($("#"+id).val()),
        success: function(json) {
            dr_tips(json.code, json.msg, -1);
        },
        error: function(HttpRequest, ajaxOptions, thrownError) {
            dr_ajax_admin_alert_error(HttpRequest, ajaxOptions, thrownError)
        }
    });
}
</script>
<style>
    .btn-default.active, .btn-default:active, .btn-default:hover, .btn-default.active.focus {
        border-color: #e7ecf1;
    }
    .fc-3x3 .btn2 {
        border-bottom: 1px solid #e7ecf1;
    }
</style>
{template "footer.html"}