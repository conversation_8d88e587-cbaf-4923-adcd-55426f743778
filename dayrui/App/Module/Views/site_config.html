{template "header.html"}
<script type="text/javascript">
    $(function() {
        {if empty($data['SITE_CLOSE'])}
        $('.dr_close_msg').hide();
        {else}
        $('.dr_close_msg').show();
        {/if}
            {if empty($data['SITE_INDEX_HTML'])}
            $('.dr_index_time').hide();
            {else}
            $('.dr_index_time').show();
            {/if}
            dr_theme({$is_theme});
        });
    function dr_theme(id) {
        if (id == 1) {
            $("#dr_theme_html").html($("#dr_web").html());
        } else {
            $("#dr_theme_html").html($("#dr_local").html());
        }
    }
</script>
<div id="dr_local" style="display: none">
    <label class="col-md-2 control-label">{dr_lang('主题风格')}</label>
    <div class="col-md-9">
        <label><select class="form-control" name="data[SITE_THEME]">
            <option value="default"> -- </option>
            {loop $theme $t}
            <option{if $t==$data['SITE_THEME']} selected=""{/if} value="{$t}">{$t}</option>
            {/loop}
        </select></label>
        <span class="help-block">{dr_lang('位于网站主站根目录下：/static/风格名称/')}</span>
    </div>
</div>
<div id="dr_web" style="display: none">
    <label class="col-md-2 control-label">{dr_lang('远程资源')}</label>
    <div class="col-md-9">
        <input class="form-control  input-xlarge" type="text" placeholder="http://" name="data[SITE_THEME2]" value="{php echo $data['SITE_THEME'];}">
        <span class="help-block">{dr_lang('网站将调用此地址的css、js、图片等静态资源，以http开头并以/结尾')}</span>
    </div>
</div>

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="myfbody">
    <div class="portlet bordered light ">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-globe"></i> {dr_lang('网站设置')} </a>
                </li>
                <li class="">
                    <a href="{dr_url("api/demo", ['name'=>'pc'])}" target="_blank"> <i class="fa fa-desktop"></i> {dr_lang('预览网站')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">

                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('网站名称')}</label>
                            <div class="col-md-9">
                                <label><input class="form-control input-large" type="text" name="data[SITE_NAME]" id="dr_name" value="{htmlspecialchars((string)$data['SITE_NAME'])}"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('系统状态')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" onclick="$('.dr_close_msg').hide()" name="data[SITE_CLOSE]" value="0" {if empty($data['SITE_CLOSE'])}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" onclick="$('.dr_close_msg').show()" name="data[SITE_CLOSE]" value="1" {if $data['SITE_CLOSE']}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('当关闭网站时，除管理员之外的用户将无法访问（静态页面除外）')}</span>
                            </div>
                        </div>
                        <div class="form-group dr_close_msg">
                            <label class="col-md-2 control-label">{dr_lang('关闭理由')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control" style="height:100px" name="data[SITE_CLOSE_MSG]">{php echo $data['SITE_CLOSE_MSG'] ? $data['SITE_CLOSE_MSG'] : '网站升级中....';}</textarea>

                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('首页静态')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" onclick="$('.dr_index_time').show()" name="data[SITE_INDEX_HTML]" value="1" {if $data['SITE_INDEX_HTML']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" onclick="$('.dr_index_time').hide()" name="data[SITE_INDEX_HTML]" value="0" {if empty($data['SITE_INDEX_HTML'])}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('开启后，系统会定时删除index.html文件，当有人访问首页时才会自动生成新的index.html文件')}</span>
                            </div>
                        </div>
                        <div class="form-group dr_index_time">
                            <label class="col-md-2 control-label">{dr_lang('生成周期')}</label>
                            <div class="col-md-9">
                                <div class="input-inline input-small">
                                    <div class="input-group">
                                        <input type="text" name="data[SITE_INDEX_TIME]" value="{php echo $data['SITE_INDEX_TIME'] ? intval($data['SITE_INDEX_TIME']) : 10;}" class="form-control">
                                        <span class="input-group-addon">
                                            {dr_lang('小时')}
                                        </span>
                                    </div>
                                </div>
                                <span class="help-inline"> {dr_lang('每隔多少小时生成首页静态文件')} </span>
                                <p>
                                    {if $run_time}
                                    <font color="green">{dr_lang('最近自动执行时间为：%s', $run_time)}</font>
                                    {else}
                                    <font color="red">{dr_lang('当前服务器没有设置自动任务脚本')}</font>
                                    {/if}
                                </p>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('网站语言')}</label>
                            <div class="col-md-9">
                                <label><select class="form-control" name="data[SITE_LANGUAGE]">
                                    <option value="zh-cn"> -- </option>
                                    {loop $lang $t}
                                    <option{if $t==$data['SITE_LANGUAGE']} selected=""{/if} value="{$t}">{$t}</option>
                                    {/loop}
                                </select></label>
                                <span class="help-block">{dr_lang('为网站设置不同的语言包')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('风格模式')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" onclick="dr_theme(1)" name="theme" value="1" {if $is_theme}checked{/if} /> {dr_lang('远程地址')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" onclick="dr_theme(0)" name="theme" value="0" {if !$is_theme}checked{/if} /> {dr_lang('本网站资源')} <span></span></label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="dr_theme_html">

                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('模板目录')}</label>
                            <div class="col-md-9">
                                <label><select class="form-control" name="data[SITE_TEMPLATE]">
                                    <option value="default"> -- </option>
                                    {loop $template_path $t}
                                    <option{if $t==$data['SITE_TEMPLATE']} selected=""{/if} value="{$t}">{$t}</option>
                                    {/loop}
                                </select></label>
                                <span class="help-block">{dr_lang('存放网站模板html文件的目录')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('网站时区')}</label>
                            <div class="col-md-9">
                                <label><select class="form-control" name="data[SITE_TIMEZONE]">
                                    <option value=""> -- </option>
                                    <option value="-12" {if $data['SITE_TIMEZONE']=="-12"}selected{/if}>(GMT -12:00)</option>
                                    <option value="-11" {if $data['SITE_TIMEZONE']=="-11"}selected{/if}>(GMT -11:00)</option>
                                    <option value="-10" {if $data['SITE_TIMEZONE']=="-10"}selected{/if}>(GMT -10:00)</option>
                                    <option value="-9" {if $data['SITE_TIMEZONE']=="-9"}selected{/if}>(GMT -09:00)</option>
                                    <option value="-8" {if $data['SITE_TIMEZONE']=="-8"}selected{/if}>(GMT -08:00)</option>
                                    <option value="-7" {if $data['SITE_TIMEZONE']=="-7"}selected{/if}>(GMT -07:00)</option>
                                    <option value="-6" {if $data['SITE_TIMEZONE']=="-6"}selected{/if}>(GMT -06:00)</option>
                                    <option value="-5" {if $data['SITE_TIMEZONE']=="-5"}selected{/if}>(GMT -05:00)</option>
                                    <option value="-4" {if $data['SITE_TIMEZONE']=="-4"}selected{/if}>(GMT -04:00)</option>
                                    <option value="-3.5" {if $data['SITE_TIMEZONE']=="-3.5"}selected{/if}>(GMT -03:30)</option>
                                    <option value="-3" {if $data['SITE_TIMEZONE']=="-3"}selected{/if}>(GMT -03:00)</option>
                                    <option value="-2" {if $data['SITE_TIMEZONE']=="-2"}selected{/if}>(GMT -02:00)</option>
                                    <option value="-1" {if $data['SITE_TIMEZONE']=="-1"}selected{/if}>(GMT -01:00)</option>
                                    <option value="0" {if $data['SITE_TIMEZONE']=="0"}selected{/if}>(GMT)</option>
                                    <option value="1" {if $data['SITE_TIMEZONE']=="1"}selected{/if}>(GMT +01:00)</option>
                                    <option value="2" {if $data['SITE_TIMEZONE']=="2"}selected{/if}>(GMT +02:00)</option>
                                    <option value="3" {if $data['SITE_TIMEZONE']=="3"}selected{/if}>(GMT +03:00)</option>
                                    <option value="3.5" {if $data['SITE_TIMEZONE']=="3.5"}selected{/if}>(GMT +03:30)</option>
                                    <option value="4" {if $data['SITE_TIMEZONE']=="4"}selected{/if}>(GMT +04:00)</option>
                                    <option value="4.5" {if $data['SITE_TIMEZONE']=="4.5"}selected{/if}>(GMT +04:30)</option>
                                    <option value="5" {if $data['SITE_TIMEZONE']=="5"}selected{/if}>(GMT +05:00)</option>
                                    <option value="5.5" {if $data['SITE_TIMEZONE']=="5.5"}selected{/if}>(GMT +05:30)</option>
                                    <option value="5.75" {if $data['SITE_TIMEZONE']=="5.75"}selected{/if}>(GMT +05:45)</option>
                                    <option value="6" {if $data['SITE_TIMEZONE']=="6"}selected{/if}>(GMT +06:00)</option>
                                    <option value="6.5" {if $data['SITE_TIMEZONE']=="6.6"}selected{/if}>(GMT +06:30)</option>
                                    <option value="7" {if $data['SITE_TIMEZONE']=="7"}selected{/if}>(GMT +07:00)</option>
                                    <option value="8" {if $data['SITE_TIMEZONE']=="" || $data['SITE_TIMEZONE']=="8"}selected{/if}>(GMT +08:00)</option>
                                    <option value="9" {if $data['SITE_TIMEZONE']=="9"}selected{/if}>(GMT +09:00)</option>
                                    <option value="9.5" {if $data['SITE_TIMEZONE']=="9.5"}selected{/if}>(GMT +09:30)</option>
                                    <option value="10" {if $data['SITE_TIMEZONE']=="10"}selected{/if}>(GMT +10:00)</option>
                                    <option value="11" {if $data['SITE_TIMEZONE']=="11"}selected{/if}>(GMT +11:00)</option>
                                    <option value="12" {if $data['SITE_TIMEZONE']=="12"}selected{/if}>(GMT +12:00)</option>
                                </select></label>
                                <span class="help-block">{dr_lang('例如中国地区选择“GMT +08:00”表示东八区')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('时间格式')}</label>
                            <div class="col-md-9">
                                <label><input class="form-control" type="text" name="data[SITE_TIME_FORMAT]" value="{htmlspecialchars((string)$data['SITE_TIME_FORMAT'])}"></label>
                                <span class="help-block">{dr_lang('网站时间显示格式与date函数一致，默认Y-m-d H:i:s')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('服务器时间')}</label>
                            <div class="col-md-9">
                                <div class="form-control-static">{dr_date(time(), 'Y-m-d H:i:s')}</div>
                            </div>
                        </div>

                    </div>
                </div>


            </div>
        </div>
    </div>
    </div>
    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
        </div>
    </div>
</form>

{template "footer.html"}