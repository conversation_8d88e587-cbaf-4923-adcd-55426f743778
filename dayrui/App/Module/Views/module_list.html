{template "header.html"}

<div class="right-card-box">
    <form class="form-horizontal" role="form" id="myform">
        {dr_form_hidden()}
        <div class="table-scrollable">
            <table class="table table-noborder table-striped table-bordered table-hover table-checkable dataTable">
                <thead>
                <tr class="heading">
                    <th width="50" style="text-align:center"> {dr_lang('可用')} </th>
                    <th width="70" style="text-align:center"> {dr_lang('排序')} </th>
                    <th width="60" style="text-align:center"> {dr_lang('类型')} </th>
                    <th width="200"> {dr_lang('名称')} / {dr_lang('目录')}</th>
                    <th> {dr_lang('操作')} </th>
                </tr>
                </thead>
                <tbody>
                {loop $my $t}
                <tr class="odd gradeX">
                    <td style="text-align:center">
                        <a href="javascript:;" onclick="dr_ajax_open_close(this, '{dr_url('module/module/hidden_edit', ['id'=>$t.id])}', 1);" class="badge badge-{if $t.disabled}no{else}yes{/if}"><i class="fa fa-{if $t.disabled}times{else}check{/if}"></i></a>
                    </td>
                    <td style="text-align:center">
                        <input type="text" onblur="dr_ajax_save(this.value, '{dr_url('module/module/displayorder_edit', ['id'=>$t.id])}')" value="{$t.displayorder}" class="displayorder form-control input-sm input-inline input-mini">
                    </td>
                    <td style="text-align:center">
                        {if !$t.system}
                        <span class="badge badge-warning badge-roundless"> {dr_lang('应用')} </span>
                        {else}
                        {if $t.share}
                        <span class="badge badge-success badge-roundless"> {dr_lang('共享')} </span>
                        {else}
                        <span class="badge badge-info badge-roundless"> {dr_lang('独立')} </span>
                        {/if}
                        {/if}
                    </td>
                    <td><a {if !$t.share}href="{WEB_DIR}index.php?s={$t.dirname}" target="_blank"{/if}>{$t.name} / {$t.dirname}</a></td>
                    <td>
                        {if $t.install}
                        {if $ci->_is_admin_auth('edit')}
                        <label><a class="btn btn-xs red" href="javascript:dr_iframe('{dr_lang("变更名称")}', '{dr_url("module/module/name_edit", ['dir'=>$t.dirname])}', '450px', '300px');"><i class="fa fa-edit"></i> {dr_lang('更名')}</a></label>
                        <label><a href="{dr_url('module/module/edit', ['id'=>$t.id])}" class="btn btn-xs green"> <i class="fa fa-cog"></i> {dr_lang('配置')} </a></label>
                        {if is_file(dr_get_app_dir($t.dirname).'Controllers/Admin/Param.php')}
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('自定义设置')}','{dr_url($t.dirname.'/param/index')}&is_menu=1', '80%', '90%');" class="btn btn-xs blue"> <i class="fa fa-cogs"></i> {dr_lang('自定义')} </a></label>
                        {/if}
                        <label><a href="{dr_url('module/module/flag_edit', ['id'=>$t.id])}" class="btn btn-xs dark"> <i class="fa fa-flag"></i> {dr_lang('推荐位')} </a></label>
                        {if dr_is_app('mform')}<label><a href="{dr_url('mform/module/index')}" class="btn btn-xs blue"> <i class="fa fa-list"></i> {dr_lang('模块表单')} </a></label>{/if}
                        {/if}
                        {if $ci->_is_admin_auth()}
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('模块内容字段')}','{dr_url('field/index', ['rname'=>'module', 'rid'=>$t.id])}&is_menu=1', '80%', '90%');" class="btn btn-xs green"> <i class="fa fa-code"></i> {dr_lang('模块内容字段')} </a></label>
                        <label><a href="{dr_url('module/module_category/field_index', ['dir'=>$t.dirname])}" class="btn btn-xs dark"> <i class="fa fa-reorder"></i> {dr_lang('栏目模型字段')} </a></label>
                        <label><a href="javascript:dr_load_ajax('{dr_lang('确定将此模块从当前项目中删除吗？')}', '{dr_url('module/module/uninstall', ['dir'=>$t.dirname])}', 1);" class="btn btn-xs red"> <i class="fa fa-trash"></i> {dr_lang('释放')} </a></label>
                        {/if}
                        {else}
                        {if $ci->_is_admin_auth()}
                        <label><a href="javascript:dr_install_module('{dr_url('module/module/install', ['dir'=>$t.dirname])}', '{intval($t.inde)}');" class="btn btn-xs blue"> <i class="fa fa-plus"></i> {dr_lang('装载')} </a></label>
                        {/if}
                        {/if}
                    </td>
                </tr>
                {/loop}
                {if $ci->_is_admin_auth()}
                {loop $list $t}
                <tr class="odd gradeX">
                    <td style="text-align:center"> -- </td>
                    <td style="text-align:center"> -- </td>
                    <td style="text-align:center"> -- </td>
                    <td>{$t.name} / {$t.dirname}</td>
                    <td>
                        <label><a href="javascript:{if !$t.mtype}dr_install_module_select{else}dr_install_module{/if}('{dr_url('module/module/install', ['dir'=>$t.dirname])}');" class="btn btn-xs blue"> <i class="fa fa-plus"></i> {dr_lang('装载')} </a></label>
                        <label><a href="javascript:dr_iframe('{dr_lang('删除提示')}', '{dr_url('cloud/app_delete', ['dir'=>$t.dirname])}', '600px', '320px');" class="btn btn-xs red"> <i class="fa fa-close"></i> {dr_lang('删除')} </a></label>
                    </td>
                </tr>
                {/loop}
                {/if}
                </tbody>
            </table>
        </div>


    </form>
</div>

{template "footer.html"}