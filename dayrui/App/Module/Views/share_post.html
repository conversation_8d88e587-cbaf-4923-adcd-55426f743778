{template "header.html"}

<script type="text/javascript">
    {if $is_verify}
    $(function () {
        $('#dr_row_hits').hide();
    });
    {else}
    {$auto_form_data_ajax}
    {/if}
    
    // 采集URL功能
    /*function dr_fetch_url() {
        var url = $('#dr_fetch_url').val();
        if (!url) {
            dr_tips(0, '请输入要采集的网址');
            return;
        }
        
        var source = $('#dr_fetch_source').val();
        var loading = layer.load(2, {
            shade: [0.3,'#fff'], //0.1透明度的白色背景
            time: 10000
        });
        
        $.ajax({
            type: "GET",
            url: "{dr_url(APP_DIR.'/home/<USER>')}&url="+encodeURIComponent(url)+"&source="+source,
            dataType: "json",
            success: function(json) {
                layer.close(loading);
                if (json.code == 1) {
                    // 填充表单
                    if (json.data.title) {
                        $('#dr_title').val(json.data.title);
                    }
                    if (json.data.description) {
                        // 查找内容编辑器或描述字段
                        if ($('#dr_description').length > 0) {
                            $('#dr_description').val(json.data.description);
                        } else if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances.dr_content) {
                            CKEDITOR.instances.dr_content.setData(json.data.description);
                        } else if (typeof UE !== 'undefined' && UE.getEditor('dr_content')) {
                            UE.getEditor('dr_content').setContent(json.data.description);
                        }
                    }
                    if (json.data.keywords) {
                        $('#dr_keywords').val(json.data.keywords);
                    }
                    if (json.data.url) {
                        $('#dr_url').val(json.data.url);
                    }
                    if (json.data.icon && $('#dr_thumb').length > 0) {
                        $('#dr_thumb').val(json.data.icon);
                        $('.my_thumb_img').attr('src', json.data.icon);
                    }
                    
                    dr_tips(1, '采集成功');
                } else {
                    dr_tips(0, json.msg);
                }
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                layer.close(loading);
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }*/
    
    function dr_syncat() {
        var title = '<i class="fa fa-refresh"></i> {dr_lang('同步到其他栏目')}';
        var url = '{dr_url(MOD_DIR.'/home/<USER>')}&catid='+$('#dr_syncatid').val();
        layer.open({
            type: 2,
            title: title,
            shadeClose: true,
            shade: 0,
            area: [{php echo \Phpcmf\Service::IS_PC_USER() ? '"40%", "70%"' : '"95%", "90%"'}],
            btn: [lang['ok']],
            yes: function(index, layero){
                var body = layer.getChildFrame('body', index);
                $(body).find('.form-group').removeClass('has-error');
                // 延迟加载
                var loading = layer.load(2, {
                    shade: [0.3,'#fff'], //0.1透明度的白色背景
                    time: 10000
                });
                $.ajax({type: "POST",dataType:"json", url: url, data: $(body).find('#myform').serialize(),
                    success: function(json) {
                        layer.close(loading);
                        if (json.code == 1) {
                            layer.close(index);
                            $('#dr_syncatid').val(json.data);
                            $('#dr_syncat_text').show();
                            $('#dr_syncat_num').html(json.msg);
                        } else {
                            dr_tips(json.code, json.msg);
                        }
                        return false;
                    },
                    error: function(HttpRequest, ajaxOptions, thrownError) {
                        dr_ajax_alert_error(HttpRequest, this, thrownError);;
                    }
                });
                return false;
            },
            content: url+'&is_iframe=1'
        });
    }

    // 定时发布
    function dr_ajax_time_submit() {

        layer.open({
            type: 2,
            title: '<i class="fa fa-clock-o"></i> {dr_lang('定时发布')}',
            fix:true,
            scrollbar: false,
            shadeClose: true,
            shade: 0,
            area: [{php echo \Phpcmf\Service::IS_PC_USER() ? '"500px", "450px"' : '"95%", "90%"'}],
            btn: [lang['ok']],
            success: function (json) {
                if (json.code == 0) {
                    layer.close();
                    dr_tips(json.code, json.msg);
                }
            },
            yes: function(index, layero){
                var body = layer.getChildFrame('body', index);
                var loading = layer.load(2, {
                    time: 10000
                });
                var url = '{dr_url(APP_DIR.'/time/add')}';
                var posttime = $(body).find('#posttime').val();
                if (posttime.length < 5) {
                    layer.closeAll('loading');
                    dr_tips(0, "{dr_lang('发布时间必须填写')}");
                    return false;
                }
                $('#myform .form-group').removeClass('has-error');
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: url+"&posttime="+posttime,
                    data: $("#myform").serialize(),
                    success: function(json) {
                        layer.close(loading);
                        if (json.code > 0) {
                            dr_tips(1, json.msg);
                            setTimeout("window.location.href = '{dr_url(APP_DIR.'/time/index')}'", 2000);
                        } else {
                            dr_tips(0, json.msg);
                            if (json.data.field) {
                                $('#dr_row_'+json.data.field).addClass('has-error');
                                $('#dr_'+json.data.field).focus();
                            }
                        }
                    },
                    error: function(HttpRequest, ajaxOptions, thrownError) {
                        dr_ajax_alert_error(HttpRequest, this, thrownError);;
                    }
                });

                return false;
            },
            content: "{dr_url(APP_DIR.'/time/add')}&is_iframe=1"
        });
    }
    // 退稿
    function dr_ajax_tui_submit() {

        layer.open({
            type: 2,
            title: '<i class="fa fa-sign-out"></i> {dr_lang('退稿')}',
            fix:true,
            scrollbar: false,
            shadeClose: true,
            shade: 0,
            area: [{php echo \Phpcmf\Service::IS_PC_USER() ? '"40%", "70%"' : '"95%", "90%"'}],
            btn: [lang['ok']],
            success: function (json) {
                if (json.code == 0) {
                    layer.close();
                    dr_tips(json.code, json.msg);
                }
            },
            yes: function(index, layero){
                var body = layer.getChildFrame('body', index);
                var loading = layer.load(2, {
                    time: 10000
                });
                var url = '{dr_url(APP_DIR.'/home/<USER>')}&id={$id}';
                var note = $(body).find('#dr_note').val();
                var clear = $(body).find("input[name='clear']:checked").val();
		
                if (note.length == 0) {
                    layer.closeAll('loading');
                    dr_tips(0, "{dr_lang('退稿理由必须填写')}");
                    return false;
                }
                $('#myform .form-group').removeClass('has-error');
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: url+"&note="+note+"&clear="+clear,
                    data: $("#myform").serialize(),
                    success: function(json) {
                        layer.close(loading);
                        if (json.code == 1) {
                            dr_tips(1, json.msg);
                            setTimeout("window.location.href = '{$reply_url}'", 2000);
                        } else {
                            dr_tips(0, json.msg);
                            if (json.data.field) {
                                $('#dr_row_'+json.data.field).addClass('has-error');
                                $('#dr_'+json.data.field).focus();
                            }
                        }
                    },
                    error: function(HttpRequest, ajaxOptions, thrownError) {
                        dr_ajax_alert_error(HttpRequest, this, thrownError);;
                    }
                });

                return false;
            },
            content: "{dr_url(APP_DIR.'/home/<USER>')}&page=5&is_iframe=1"
        });
    }
    function dr_ajax_verify_submit() {
        $('#dr_is_draft').val(0);
        dr_ajax_submit('{dr_now_url()}', 'myform', '2000', '{$reply_url}');
    }
    $(function () {
        {if isset($_GET['is_verify_iframe']) && $_GET['is_verify_iframe']}
        setTimeout(function(){
            $('#dr_is_draft').val(0);
            $.ajax({
                type: "POST",
                dataType: "json",
                url: '{dr_now_url()}',
                data: $("#myform").serialize(),
                success: function(json) {
                    if (json.code) {
                        if (json.data.htmlfile) {
                            // 执行生成htmljs
                            $.ajax({
                                type: "GET",
                                url: json.data.htmlfile,
                                dataType: "jsonp",
                                success: function(json){ },
                                error: function(){ }
                            });
                        }
                        if (json.data.htmllist) {
                            // 执行生成htmljs
                            $.ajax({
                                type: "GET",
                                url: json.data.htmllist,
                                dataType: "jsonp",
                                success: function(json){ },
                                error: function(){ }
                            });
                        }
                        parent.$('#content_{$id}').addClass("badge badge-success");
                        parent.$('#content_{$id}').html('{dr_lang("成功")}');
                    } else {
                        parent.$('#content_{$id}').addClass("badge badge-danger");
                        parent.$('#content_{$id}').html(json.msg);
                    }
                },
                error: function(HttpRequest, ajaxOptions, thrownError) {
                    parent.$('#content_{$id}').addClass("badge badge-danger");
                    parent.$('#content_{$id}').html('{dr_lang("执行失败")}');
                }
            });
        }, 5000);
        {/if}
    });
</script>
{if $post_notice_msg}
<div class="note note-danger">
    <p>{$post_notice_msg}</p>
</div>
{/if}
<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="myfbody">
        {if $mymerge && $ci->module['setting']['merge']}
        <div class="tabbable-line margin-bottom-20">
            <ul class="nav nav-tabs">
                <li class="active">
                    <a toid="dr_default" data-toggle="tab">{dr_lang('基本内容')}</a>
                </li>
                {loop $mymerge $t}
                <li>
                    <a toid="dr_row_{$t}" data-toggle="tab">{$field[$t]['name']}</a>
                </li>
                {/loop}
                {if $diyfield}
                <li>
                    <a toid="dr_orther" data-toggle="tab">{dr_lang('其他内容')}</a>
                </li>
                {/if}
            </ul>
        </div>
        <script type="text/javascript">
            $(function () {
                $('.myfield-main .portlet.light.bordered').hide();
                $('#dr_default').show();
                $('.nav-tabs a').click(function () {
                    var tid = $(this).attr('toid');
                    $('.myfield-main .portlet.light.bordered').hide();
                    $('#'+tid).show();
                });
            });
        </script>
        {/if}
        <div class="row ">
            <div class="myfield-main {if !$is_right_field || $is_mobile}col-md-12{else}col-md-9{/if}">

                <div class="portlet light bordered" id="dr_default">
                    <div class="portlet-title">
                        <div class="caption">
                            <span class="caption-subject font-green sbold ">{dr_lang('基本内容')}</span>
                        </div>

                        <div class="actions">
                            {if $draft_list}
                            <div class="btn-group">
                                <a class="btn btn-default dropdown-toggle" data-toggle="dropdown" href="javascript:;" aria-expanded="false">
                                    {dr_lang('草稿')}
                                    <i class="fa fa-angle-down"></i>
                                </a>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a href="{$draft_url}"> {dr_lang('查看原文')} </a>
                                    </li>
                                    {loop $draft_list $t}
                                    <li>
                                        <a href="{$draft_url}&did={$t.id}" {if $t.id==$did}style="color:red"{/if}> {dr_date($t.inputtime)} - {$t.title} </a>
                                    </li>
                                    {/loop}
                                </ul>
                            </div>
                            {/if}
                            <div class="btn-group">
                                <a class="btn" href="{$reply_url}"> <i class="fa fa-mail-reply"></i> {dr_lang('返回列表')}</a>
                            </div>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="form-body">
                            <div class="form-group {if !$is_category_show}hide{/if}">
                                <label class="col-md-2 control-label">{dr_lang('栏目')}</label>
                                <div class="col-md-9">
                                    {php $catid && $cat=dr_cat_value($catid);}
                                    {if $catid && $cat && $cat['setting']['notedit']}
                                    <label style="margin-top: 7px;">
                                        <span class="label label-sm label-success circle">{$cat['name']}</span>
                                    </label>
                                    <input type="hidden" id="dr_catid" name="catid" value="{$catid}">
                                    {else}
                                        <label style="margin-right:10px">{$select}</label>
                                        {if $module['setting']['sync_category']}
                                        {if !$id || $is_sync_cat}
                                        <label style="margin-right:10px"><a href="javascript:;" onclick="dr_syncat()">[{dr_lang('同步发布到其他栏目')}]</a></label>
                                        <label>
                                            <input id="dr_syncatid" name="sync_cat" type="hidden" value="{$is_sync_cat}" />
                                            <span id="dr_syncat_text" class="label label-success" style="display: {if $is_sync_cat}blank{else}none{/if};"><b id="dr_syncat_num">{php echo substr_count((string)$is_sync_cat, ',') + 1;}</b></span>
                                        </label>
                                        {else if $link_id != 0}
                                        <label>{dr_lang('修改内容时会同步更新其他外联文档')}</label>
                                        {/if}
                                        {/if}
										{if CI_DEBUG && $ci->_is_admin_auth($module['dirname'].'/category/index')}
										<label style="margin-right:10px"><a href="{dr_url($module['dirname'].'/category/index')}">[{dr_lang('栏目管理')}]</a></label>
										{/if}
                                    {/if}
                                </div>
                            </div>
                            
                            <!-- 添加采集URL输入框 -->
                            <div class="form-group">
                                <label class="col-md-2 control-label">采集网址</label>
                                <div class="col-md-9">
                                    <div class="input-group">
                                        <div class="input-group-btn" style="width: 120px;">
                                            <select id="dr_fetch_source" class="form-control" style="border-radius: 4px 0 0 4px;">
                                                <option value="default">默认源</option>
                                                <option value="aibot" selected>AI工具集</option>
                                            </select>
                                        </div>
                                        <input type="text" class="form-control" id="dr_fetch_url" placeholder="请输入要采集内容的网址">
                                        <span class="input-group-btn">
                                            <button class="btn blue" type="button" id="fetch_url_btn">采集</button>
                                        </span>
                                    </div>
                                    <span class="help-block">输入网址后点击"采集"按钮，自动填充标题、描述、关键词等信息</span>
                                </div>
                            </div>
                            <!-- 采集URL输入框结束 -->
                            
                            {$myfield}
                        </div>
                    </div>
                </div>

                {if $diyfield}
                <div class="portlet light bordered" id="dr_orther">
                    <div class="portlet-title">
                        <div class="caption">
                            <span class="caption-subject font-green sbold ">{dr_lang('其他内容')}</span>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="form-body">
                            {$diyfield}
                        </div>
                    </div>
                </div>
                {/if}

            </div>
            <div class="{if !$is_right_field || $is_mobile}col-md-12{else}col-md-3{/if} my-sysfield" {if !$is_right_field} style="display: none"{/if}>
                <div class="portlet light bordered">
                    <div class="portlet-body">
                        <div class="form-body">
                            {dr_rp($sysfield, ['md-2', 'md-10'], 'md-12')}
                            {if $is_flag}
                            <div class="form-group">
                                <label class="col-md-12 control-label">{dr_lang('推荐位')}</label>
                                <div class="col-md-12">
                                    <div class="mt-checkbox-list">
                                        {loop $is_flag $n $f}
                                        <label class="mt-checkbox mt-checkbox-outline"> {dr_lang($f.name)}
                                            <input {if isset($verify_next) && $verify_next!==9} disabled=""{/if} type="checkbox" {if dr_in_array($n, $my_flag)} checked{/if} value="{$n}" name="flag[]" />
                                            <span></span>
                                        </label>
                                        {/loop}
                                    </div>
                                    {if isset($verify_next) && $verify_next!==9}
                                    <span class="help-block">{dr_lang('只有在最终审核状态时才能设置推荐位')}</span>
                                    {/if}
                                </div>
                            </div>
                            {/if}
                            {if !$is_verify && $is_post_time}
                            <div class="form-group">
                                <label class="col-md-12 control-label">{dr_lang('定时发布时间')}</label>
                                <div class="col-md-12">
                                    <span class="form-date input-group">
                                        <div class="input-group date field_date_post_inputtime">
                                            <input id="posttime" name="posttime" type="text" style="width:160px;" value="{dr_date($posttime, 'Y-m-d H:i:s')}"  required="required" class="form-control ">
                                            <span class="input-group-btn">
                                                <button class="btn default date-set" type="button">
                                                    <i class="fa fa-calendar"></i>
                                                </button>
                                            </span>
                                        </div>
                                        <script>
                                        $(function(){
                                            $(".field_date_post_inputtime").datetimepicker({
                                                isRTL: App.isRTL(),
                                                format: "yyyy-mm-dd hh:ii:ss",
                                                showMeridian: true,
                                                autoclose: true,
                                                pickerPosition: (App.isRTL() ? "bottom-right" : "bottom-left"),
                                                todayBtn: true
                                            });
                                        });
                                        </script>
                                    </span>
                                </div>
                            </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>


        {if $is_verify}
        <div class="row ">
            <div class="col-md-12 ">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <span class="caption-subject font-green sbold ">{dr_lang('内容审核')}</span>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="form-body">

                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('审核类型')}</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        {if $verify.isnew==1}
                                        <span class="label label-success"> {dr_lang('新增')} </span>
                                        {elseif $verify.isnew==2}
                                        <span class="label label-danger"> {dr_lang('删除')} </span>
                                        {else}
                                        <span class="label label-warning"> {dr_lang('修改')} </span>
                                        {/if}
                                    </p>
                                </div>
                            </div>
                            {if $status}
                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('当前状态')}</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        <span class="label label-warning"> {dr_lang('%s审中', $status)} </span>
                                    </p>
                                </div>
                            </div>
                            {else}
                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('当前状态')}</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        <span class="label label-danger"> {dr_lang('被拒绝')} </span>
                                    </p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('处理账号')}</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        {Function_list::author($verify.backinfo.author, [], $verify.backinfo)}
                                    </p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('处理时间')}</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        {dr_date($verify.backinfo.optiontime)}
                                    </p>
                                </div>
                            </div>
                            {if $verify.backinfo.backcontent}
                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('审核说明')}</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        {$verify.backinfo.backcontent}
                                    </p>
                                </div>
                            </div>
                            {/if}
                            {/if}
                            {if $verify_step[$status]['name']}
                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('当前审核')}</label>
                                <div class="col-md-9">
                                    <p class="form-control-static"> {$verify_step[$status]['name']} </p>
                                </div>
                            </div>
                            {/if}
                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('下级审核')}</label>
                                <div class="col-md-9">
                                    <p class="form-control-static"> {$verify_step[$verify_next]['name']} </p>
                                </div>
                            </div>
                            {if $is_post_user}
                            <div class="form-group">
                                <label class="col-md-2 control-label">审核人</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        {Function_list::author($verify.backinfo.author, [], $verify.backinfo)}
                                    </p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label">审核时间</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        {dr_date($verify.backinfo.optiontime)}
                                    </p>
                                </div>
                            </div>
                            {if $verify.backinfo.backcontent}
                            <div class="form-group">
                                <label class="col-md-2 control-label">审核说明</label>
                                <div class="col-md-9">
                                    <p class="form-control-static">
                                        {$verify.backinfo.backcontent}
                                    </p>
                                </div>
                            </div>
                            {/if}
                            {else}
                            <div class="form-group">
                                <label class="col-md-2 control-label">{dr_lang('操作状态')}</label>
                                <div class="col-md-9">
                                    <div class="mt-radio-inline">
                                        <label class="mt-radio mt-radio-outline"><input type="radio" onclick="$('.dr_close_msg').hide();$('.dr_verify_time').show()" name="verify[status]" value="1" {if !$back_note} checked{/if} /> {dr_lang('通过')} <span></span></label>
                                        <label class="mt-radio mt-radio-outline"><input type="radio" onclick="$('.dr_close_msg').show();$('.dr_verify_time').hide()" name="verify[status]" value="0" {if $back_note} checked{/if} /> {dr_lang('拒绝')} <span></span></label>
                                    </div>
                                </div>
                            </div>
                            {if $verify_next == 9}
                            <div class="form-group dr_verify_time">
                                <label class="col-md-2 control-label">{dr_lang('发布时间')}</label>
                                <div class="col-md-9">
                                    <span class="form-date input-group">
                                    <div class="input-group date field_date_inputtime">
                                        <input id="verify_posttime" name="verify_posttime" type="text" style="width:160px;" placeholder="{dr_lang('立即发布')}"  required="required" class="form-control ">
                                        <span class="input-group-btn">
                                            <button class="btn default date-set" type="button">
                                                <i class="fa fa-calendar"></i>
                                            </button>
                                        </span>
                                    </div>
                                    <script>
                                    $(function(){
                                        $(".field_date_inputtime").datetimepicker({
                                            isRTL: App.isRTL(),
                                            format: "yyyy-mm-dd hh:ii:ss",
                                            showMeridian: true,
                                            autoclose: true,
                                            pickerPosition: (App.isRTL() ? "bottom-right" : "bottom-left"),
                                            todayBtn: true
                                        });
                                    });
                                    </script>
                                </span>
                                    <span class="help-block">{dr_lang('可设置定时发布，定时时间不能晚于当前时间')}</span>
                                </div>
                            </div>
                            {/if}
                            {if dr_is_app('cverify')}
                            <div class="form-group ">
                                {else}
                            <div class="form-group dr_close_msg" style="{if !$back_note} display:none{/if}">
                                {/if}
                                <label class="col-md-2 control-label">{dr_lang('审核说明')}</label>
                                <div class="col-md-9">
                                    <textarea id="dr_verify_msg" class="form-control" style="height:100px" name="verify[msg]">{$back_note}</textarea>
                                </div>
                            </div>
                                {if dr_is_app('cverify')}
                                <div class="form-group ">
                                    {else}
                                    <div class="form-group dr_close_msg" style="{if !$back_note} display:none{/if}">
                                        {/if}
                                <label class="col-md-2 control-label">{dr_lang('常用理由')}</label>
                                <div class="col-md-9">
                                    <label>
                                        <select class="form-control" onchange="javascript:$('#dr_verify_msg').val(this.value)">
                                            <option value=""> -- </option>
                                            {loop $verify_msg $t}
                                            <option value="{$t}">{$t}</option>
                                            {/loop}
                                        </select>
                                    </label>
                                </div>
                            </div>
                            {/if}



                        </div>
                    </div>
                </div>
            </div>
        </div>
        {/if}

    </div>


    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            {if !defined('IS_MODULE_RECYCLE')}
                {if $is_verify}
                    <label><button type="button" onclick="dr_ajax_verify_submit()" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('提交审核')}</button></label>
                {else if $is_post_time}
                    <label><button type="button" onclick="$('#dr_is_draft').val(0);dr_ajax_submit('{dr_now_url()}&posttime={dr_date($posttime, 'Y-m-d H:i:s')}', 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存内容')}</button></label>
                {else}
                    <label><button type="button" onclick="$('#dr_is_draft').val(0);dr_ajax_submit('{dr_now_url()}&is_self=1', 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存内容')}</button></label>
                    <label><button type="button" onclick="$('#dr_is_draft').val(0);dr_ajax_submit('{dr_now_url()}', 'myform', '2000', '{$post_url}&catid='+$('#dr_catid').val())" class="btn green"> <i class="fa fa-plus"></i> {dr_lang('保存再添加')}</button></label>
                    <label><button type="button" onclick="$('#dr_is_draft').val(0);dr_ajax_submit('{dr_now_url()}', 'myform', '2000', '{$reply_url}')" class="btn yellow"> <i class="fa fa-mail-reply-all"></i> {dr_lang('保存并返回')}</button></label>
                    {if $ci->_is_admin_auth(MOD_DIR.'/draft/add')}
                    <label><button type="button" onclick="$('#dr_is_draft').val(1);dr_ajax_submit('{dr_now_url()}', 'myform', '2000')" class="btn red"> <i class="fa fa-pencil"></i> {dr_lang('保存草稿')}</button></label>
                    {/if}
                    {if !$id && $ci->_is_admin_auth(MOD_DIR.'/time/add')}
                    <label><button type="button" onclick="dr_ajax_time_submit()" class="btn dark"> <i class="fa fa-clock-o"></i> {dr_lang('定时发布')}</button></label>
                    {/if}
                    {if $is_form_cache}
                    <label><button type="button" onclick="auto_form_data_delete()" class="btn red"> <i class="fa fa-trash"></i> {dr_lang('删除历史缓存')}</button></label>
                    {/if}
                    {$clink_btn}
                {/if}
                {if $id && !$is_verify && !$is_post_user && $ci->_is_admin_auth(MOD_DIR.'/verify/index')}
                <label><button type="button" onclick="dr_ajax_tui_submit()" class="btn green"> <i class="fa fa-sign-out"></i> {dr_lang('退稿')}</button></label>
                {/if}
            {else}
            <label><a href="{$reply_url}" class="btn yellow"> <i class="fa fa-mail-reply-all"></i> {dr_lang('返回列表')}</a></label>
            <label><button type="button" onclick="$('#dr_is_draft').val(0);dr_ajax_submit('{dr_now_url()}', 'myform', '2000', '{$reply_url}')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('恢复此内容')}</button></label>
            {/if}
            {if $web_url}
            <label><a href="{$web_url}" target="_blank" class="btn dark"> <i class="fa fa-link"></i> {dr_lang('浏览')}</a></label>
            {/if}
        </div>
    </div>
</form>

{php include APPPATH."Module/fix/fix.php";}

{template "footer.html"}