<?php
/**
 * 修复采集功能的问题
 */

// 注入修复脚本
\Phpcmf\Service::V()->display('Module/fix/header.html');

// 设置全局模块名变量
$MODULE_NAME = APP_DIR;

// 定义网站根URL，如果常量未定义
if (!defined('ROOT_URL')) {
    define('ROOT_URL', SITE_URL);
}
?>

<!-- 添加采集测试工具链接 -->
<div style="margin: 10px 0; padding: 10px; background-color: #f5f5f5; border-radius: 4px;">
    <p style="margin-bottom: 10px;"><strong>采集功能已增强</strong>：现在支持从AI工具集网站采集内容，包括标题、描述、关键词、图标、价格信息、官网链接、分类、功能特点和应用场景等。</p>
    <p><a href="<?php echo ROOT_URL; ?>dayrui/App/Module/fix/aibot_collector.php" target="_blank" class="btn blue btn-sm">打开采集测试工具</a> 
       <a href="<?php echo ROOT_URL; ?>dayrui/App/Module/fix/test_crawler.php" target="_blank" class="btn green btn-sm">诊断工具</a>
       <a href="<?php echo ROOT_URL; ?>dayrui/App/Module/fix/error_collector.php" target="_blank" class="btn red btn-sm">错误收集器</a>
       <a href="<?php echo ROOT_URL; ?>dayrui/App/Module/fix/fix_js_error.php" target="_blank" class="btn orange btn-sm">修复工具</a>
       <span style="margin-left: 10px; color: #666;">使用这些工具可以测试采集功能是否正常工作</span>
    </p>
</div>

<script type="text/javascript">
window.MODULE_NAME = '<?php echo $MODULE_NAME; ?>';
</script>

<!-- 引入修复脚本 -->
<script type="text/javascript" src="<?php echo ROOT_URL; ?>dayrui/App/Module/fix/fetch_url_fix.js?v=<?php echo time(); ?>"></script> 