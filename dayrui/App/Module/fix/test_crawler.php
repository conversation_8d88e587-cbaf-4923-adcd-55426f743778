<?php
/**
 * 轻量级测试爬虫 - 用于调试采集功能
 * 
 * 此脚本可以独立运行，不依赖于其他框架代码
 * 支持错误记录和详细日志
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义日志文件
define('LOG_FILE', __DIR__ . '/crawler_log.txt');

// 记录日志的函数
function log_message($message, $type = 'INFO') {
    $time = date('Y-m-d H:i:s');
    $log = "[$time] [$type] $message\n";
    file_put_contents(LOG_FILE, $log, FILE_APPEND);
    return $log;
}

// 开始记录
log_message('爬虫测试开始');

// 定义爬取函数
function fetch_url($url, $source = 'aibot') {
    log_message("开始抓取URL: $url, 源: $source");
    
    // 初始化curl
    $ch = curl_init();
    
    // 记录curl设置过程
    try {
        log_message("设置CURL选项");
        
        // 设置URL
        curl_setopt($ch, CURLOPT_URL, $url);
        
        // 设置用户代理
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // 返回结果而非输出
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // 允许重定向
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        // 超时设置
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        // SSL验证设置
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        log_message("执行CURL请求");
        
        // 执行请求
        $html = curl_exec($ch);
        
        // 检查是否有错误
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            log_message("CURL错误: $error", 'ERROR');
            curl_close($ch);
            return ['code' => 0, 'msg' => "CURL错误: $error", 'data' => null];
        }
        
        // 获取HTTP状态码
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        log_message("HTTP状态码: $http_code");
        
        if ($http_code != 200) {
            log_message("HTTP状态码错误: $http_code", 'ERROR');
            curl_close($ch);
            return ['code' => 0, 'msg' => "HTTP状态码错误: $http_code", 'data' => null];
        }
        
        // 关闭CURL会话
        curl_close($ch);
        
        // 记录HTML长度
        $html_length = strlen($html);
        log_message("成功获取HTML内容，长度: $html_length 字节");
        
        // 正则提取内容
        log_message("开始解析内容");
        $data = [];
        
        // 提取标题
        if (preg_match('/<title>(.*?)<\/title>/i', $html, $matches)) {
            $data['title'] = trim($matches[1]);
            log_message("提取到标题: {$data['title']}");
        } else if (preg_match('/<h1[^>]*>(.*?)<\/h1>/i', $html, $matches)) {
            $data['title'] = trim(strip_tags($matches[1]));
            log_message("从H1提取到标题: {$data['title']}");
        } else {
            log_message("未能提取到标题", 'WARNING');
        }
        
        // 提取描述
        if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            $data['description'] = trim($matches[1]);
            log_message("提取到描述: {$data['description']}");
        } else if (preg_match('/<div[^>]*class=["\']description["\'][^>]*>(.*?)<\/div>/is', $html, $matches)) {
            $data['description'] = trim(strip_tags($matches[1]));
            log_message("从DIV提取到描述: {$data['description']}");
        } else {
            log_message("未能提取到描述", 'WARNING');
        }
        
        // 提取关键词
        if (preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            $data['keywords'] = trim($matches[1]);
            log_message("提取到关键词: {$data['keywords']}");
        } else {
            log_message("未能提取到关键词", 'WARNING');
        }
        
        // 提取图标
        if (preg_match('/<link[^>]*rel=["\']icon["\'][^>]*href=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            $icon_url = trim($matches[1]);
            // 处理相对URL
            if (strpos($icon_url, 'http') !== 0) {
                $parsed_url = parse_url($url);
                $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'];
                $icon_url = $base_url . ($icon_url[0] == '/' ? '' : '/') . $icon_url;
            }
            $data['icon'] = $icon_url;
            log_message("提取到图标URL: {$data['icon']}");
        } else {
            log_message("未能提取到图标", 'WARNING');
        }
        
        // 提取缩略图
        if (preg_match('/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            $thumb_url = trim($matches[1]);
            // 处理相对URL
            if (strpos($thumb_url, 'http') !== 0) {
                $parsed_url = parse_url($url);
                $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'];
                $thumb_url = $base_url . ($thumb_url[0] == '/' ? '' : '/') . $thumb_url;
            }
            $data['thumb'] = $thumb_url;
            log_message("提取到缩略图URL: {$data['thumb']}");
        } else {
            log_message("未能提取到缩略图", 'WARNING');
        }
        
        // AI工具集特定处理
        if ($source == 'aibot') {
            log_message("执行AI工具集特定处理");
            
            // 提取价格信息
            if (preg_match('/<div[^>]*class=["\']price-info["\'][^>]*>(.*?)<\/div>/is', $html, $matches)) {
                $data['price_info'] = trim(strip_tags($matches[1]));
                log_message("提取到价格信息: {$data['price_info']}");
            }
            
            // 提取官网链接
            if (preg_match('/<a[^>]*class=["\']official-link["\'][^>]*href=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
                $data['official_url'] = trim($matches[1]);
                log_message("提取到官网链接: {$data['official_url']}");
            }
            
            // 提取分类
            if (preg_match('/<div[^>]*class=["\']category["\'][^>]*>(.*?)<\/div>/is', $html, $matches)) {
                $data['category'] = trim(strip_tags($matches[1]));
                log_message("提取到分类: {$data['category']}");
            }
        }
        
        // 测试下载图片（图标）
        if (isset($data['icon'])) {
            log_message("测试下载图标: {$data['icon']}");
            $icon_content = @file_get_contents($data['icon']);
            if ($icon_content) {
                $icon_size = strlen($icon_content);
                log_message("成功下载图标，大小: $icon_size 字节");
                
                // 检测MIME类型
                $finfo = new finfo(FILEINFO_MIME_TYPE);
                $mime_type = $finfo->buffer($icon_content);
                log_message("图标MIME类型: $mime_type");
                
                // 根据MIME类型判断扩展名
                $extensions = [
                    'image/jpeg' => 'jpg',
                    'image/png' => 'png',
                    'image/gif' => 'gif',
                    'image/webp' => 'webp',
                    'image/svg+xml' => 'svg',
                    'image/x-icon' => 'ico'
                ];
                
                if (isset($extensions[$mime_type])) {
                    $ext = $extensions[$mime_type];
                    log_message("图标扩展名: $ext");
                    
                    // 保存图标文件（仅用于测试）
                    $icon_file = __DIR__ . '/test_icon.' . $ext;
                    if (file_put_contents($icon_file, $icon_content)) {
                        log_message("图标文件已保存: $icon_file");
                        $data['icon_file'] = $icon_file;
                    } else {
                        log_message("无法保存图标文件", 'ERROR');
                    }
                } else {
                    log_message("未知的MIME类型: $mime_type", 'WARNING');
                }
            } else {
                log_message("无法下载图标", 'ERROR');
            }
        }
        
        // 记录最终结果
        $data_count = count($data);
        log_message("提取完成，共获取 $data_count 个数据项");
        
        return ['code' => 1, 'msg' => '抓取成功', 'data' => $data];
        
    } catch (Exception $e) {
        log_message("异常: " . $e->getMessage(), 'ERROR');
        if (isset($ch) && is_resource($ch)) {
            curl_close($ch);
        }
        return ['code' => 0, 'msg' => "异常: " . $e->getMessage(), 'data' => null];
    }
}

// 测试JavaScript问题
function check_js_files() {
    log_message("开始检查JavaScript文件");
    
    $js_file = __DIR__ . '/fetch_url_fix.js';
    log_message("检查文件: $js_file");
    
    if (file_exists($js_file)) {
        $content = file_get_contents($js_file);
        $size = strlen($content);
        log_message("文件存在，大小: $size 字节");
        
        // 检查是否有HTML标签
        if (preg_match('/<html|<!DOCTYPE|<body|<head|<script/i', $content)) {
            log_message("警告：JavaScript文件中包含HTML标签！", 'WARNING');
        }
        
        // 检查window.dr_fetch_url的定义
        if (preg_match('/window\.dr_fetch_url\s*=\s*function/i', $content)) {
            log_message("找到window.dr_fetch_url函数定义");
        } else {
            log_message("未找到window.dr_fetch_url函数定义", 'ERROR');
        }
    } else {
        log_message("文件不存在: $js_file", 'ERROR');
    }
    
    // 检查fix.php
    $php_file = __DIR__ . '/fix.php';
    log_message("检查文件: $php_file");
    
    if (file_exists($php_file)) {
        $content = file_get_contents($php_file);
        log_message("文件存在，大小: " . strlen($content) . " 字节");
        
        // 检查JavaScript引入
        if (preg_match('/src=["\']([^"\']*fetch_url_fix\.js[^"\']*)["\']/', $content, $matches)) {
            $js_path = $matches[1];
            log_message("找到JavaScript文件引入: $js_path");
        } else {
            log_message("未找到JavaScript文件引入", 'ERROR');
        }
    } else {
        log_message("文件不存在: $php_file", 'ERROR');
    }
}

// HTML表单输出
function output_html_form($result = null) {
    $logs = file_exists(LOG_FILE) ? file_get_contents(LOG_FILE) : '暂无日志';
    
    echo '<!DOCTYPE html>
<html>
<head>
    <title>采集调试工具</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .container { display: flex; flex-wrap: wrap; }
        .left-panel { flex: 1; min-width: 400px; margin-right: 20px; }
        .right-panel { flex: 1; min-width: 400px; }
        h1, h2 { color: #333; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], select { width: 100%; padding: 8px; box-sizing: border-box; }
        button { padding: 10px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        .result, .log { background: #f5f5f5; border: 1px solid #ddd; padding: 15px; margin-top: 15px; border-radius: 4px; overflow: auto; }
        .log { max-height: 600px; font-family: monospace; white-space: pre-wrap; }
        .error { color: red; }
        .warning { color: orange; }
        .success { color: green; }
        .code { font-family: monospace; background: #eee; padding: 10px; }
    </style>
</head>
<body>
    <h1>采集功能调试工具</h1>
    
    <div class="container">
        <div class="left-panel">
            <form method="post" action="">
                <div class="form-group">
                    <label for="url">要采集的网址：</label>
                    <input type="text" id="url" name="url" placeholder="请输入网址，例如: https://ai-bot.cn/sites/33687.html" value="' . (isset($_POST['url']) ? htmlspecialchars($_POST['url']) : '') . '">
                </div>
                
                <div class="form-group">
                    <label for="source">采集源：</label>
                    <select id="source" name="source">
                        <option value="aibot" ' . (isset($_POST['source']) && $_POST['source'] == 'aibot' ? 'selected' : '') . '>AI工具集</option>
                        <option value="default" ' . (isset($_POST['source']) && $_POST['source'] == 'default' ? 'selected' : '') . '>默认源</option>
                    </select>
                </div>
                
                <button type="submit" name="action" value="fetch">测试采集</button>
                <button type="submit" name="action" value="check_js">检查JavaScript</button>
                <button type="submit" name="action" value="clear_log">清除日志</button>
            </form>';
    
    if ($result) {
        echo '<div class="result">
                <h2>采集结果</h2>';
        if ($result['code'] == 1) {
            echo '<p class="success">采集成功！</p>';
            if (!empty($result['data'])) {
                echo '<h3>获取到的数据：</h3>';
                echo '<ul>';
                foreach ($result['data'] as $key => $value) {
                    if ($key == 'icon_file' || $key == 'thumb_file') {
                        echo '<li><strong>' . htmlspecialchars($key) . '</strong>: ' . htmlspecialchars($value) . ' <a href="' . htmlspecialchars($value) . '" target="_blank">[查看]</a></li>';
                    } else if ($key == 'icon' || $key == 'thumb') {
                        echo '<li><strong>' . htmlspecialchars($key) . '</strong>: ' . htmlspecialchars($value) . ' <a href="' . htmlspecialchars($value) . '" target="_blank">[查看]</a></li>';
                    } else {
                        echo '<li><strong>' . htmlspecialchars($key) . '</strong>: ' . htmlspecialchars($value) . '</li>';
                    }
                }
                echo '</ul>';
            } else {
                echo '<p class="warning">采集成功但未获取到数据</p>';
            }
        } else {
            echo '<p class="error">采集失败：' . htmlspecialchars($result['msg']) . '</p>';
        }
        echo '</div>';
    }
    
    echo '</div>
        
        <div class="right-panel">
            <h2>调试日志</h2>
            <div class="log">' . htmlspecialchars($logs) . '</div>
            
            <h2>JavaScript调试指南</h2>
            <div class="form-group">
                <p>如果您遇到 <code class="code">Uncaught SyntaxError: Unexpected token \'&lt;\'</code> 错误，可能原因：</p>
                <ol>
                    <li>JavaScript文件实际返回了HTML内容而非JS代码</li>
                    <li>文件路径错误，服务器返回了404页面</li>
                    <li>JavaScript文件中包含了HTML标签</li>
                </ol>
                <p>如果遇到 <code class="code">dr_fetch_url is not a function</code> 错误，可能原因：</p>
                <ol>
                    <li>JavaScript文件未正确加载</li>
                    <li>函数定义被注释掉了</li>
                    <li>window.dr_fetch_url 未正确定义</li>
                </ol>
                <p>解决方案：</p>
                <ol>
                    <li>检查JavaScript文件路径是否正确</li>
                    <li>确保函数定义没有被注释</li>
                    <li>确保使用 <code class="code">window.dr_fetch_url = function() {...}</code> 形式定义函数</li>
                    <li>检查控制台是否有其他JavaScript错误</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>';
}

// 处理请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'fetch' && !empty($_POST['url'])) {
            $url = $_POST['url'];
            $source = $_POST['source'] ?? 'aibot';
            log_message("收到抓取请求：URL=$url, Source=$source");
            $result = fetch_url($url, $source);
            output_html_form($result);
        } else if ($_POST['action'] === 'check_js') {
            log_message("收到检查JavaScript请求");
            check_js_files();
            output_html_form();
        } else if ($_POST['action'] === 'clear_log') {
            if (file_exists(LOG_FILE)) {
                unlink(LOG_FILE);
            }
            log_message("日志已清除");
            output_html_form();
        } else {
            log_message("无效的请求参数", 'WARNING');
            output_html_form();
        }
    } else {
        log_message("缺少action参数", 'WARNING');
        output_html_form();
    }
} else {
    // 首次访问
    log_message("首次访问调试工具");
    output_html_form();
} 