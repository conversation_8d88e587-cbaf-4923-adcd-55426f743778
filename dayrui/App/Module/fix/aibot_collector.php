<?php
/**
 * AI工具集采集测试工具
 * 
 * 这是一个独立的采集测试工具，用于测试从AI工具集网站采集内容的功能
 */

// 引入系统
define('IS_ADMIN', true);
require '../../../../../index.php';

// 安全检查
if (!isset($member) || !$member['adminid']) {
    exit('需要管理员权限');
}

// 设置全局模块名变量
$MODULE_NAME = APP_DIR;

// 处理采集请求
$result = [];
$url = '';
if (isset($_POST['url']) && $_POST['url']) {
    $url = $_POST['url'];
    
    // 初始化cURL会话
    $ch = curl_init();
    
    // 设置cURL选项
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    // 执行cURL请求
    $html = curl_exec($ch);
    
    // 检查是否有错误发生
    if (curl_errno($ch)) {
        $result = ['code' => 0, 'msg' => '采集失败: ' . curl_error($ch)];
    } else {
        // 关闭cURL会话
        curl_close($ch);
        
        // 解析HTML
        $title = '';
        $description = '';
        $keywords = '';
        $icon = '';
        $thumb = '';
        $content = '';
        $price_info = '';
        $official_url = '';
        $category = '';
        $features = [];
        $usage_scenarios = [];
        
        // 提取标题 - 从site-name类
        if (preg_match('/<h1 class="site-name[^"]*">(.*?)<\/h1>/is', $html, $matches)) {
            $title = trim(strip_tags($matches[1]));
        } elseif (preg_match('/<title>(.*?)<\/title>/is', $html, $matches)) {
            $title = trim($matches[1]);
            // 移除网站名称部分
            $title = preg_replace('/\s*\|\s*AI工具集$/', '', $title);
        }
        
        // 提取描述 - 从meta description或页面内容
        if (preg_match('/<p class="mb-2">(.*?)<\/p>/is', $html, $matches)) {
            $description = trim(strip_tags($matches[1]));
        } elseif (preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
            $description = trim($matches[1]);
        }
        
        // 提取分类 - 根据您提供的HTML结构
        if (preg_match('/<a\s+class=[\'"]btn-cat[^\'"]*[\'"][^>]*href=[\'"][^\'"]*(\/favorites\/[^\'"]*)[\'"]/is', $html, $matches)) {
            $category_url = $matches[1];
            if (preg_match('/\/favorites\/([^\/]+)/', $category_url, $cat_matches)) {
                $category_slug = $cat_matches[1];
                
                // 提取分类名称
                if (preg_match('/<a\s+class=[\'"]btn-cat[^\'"]*[\'"][^>]*>(.*?)<\/a>/is', $html, $cat_name_matches)) {
                    $category = trim(strip_tags($cat_name_matches[1]));
                } else {
                    // 如果无法直接提取，尝试将slug转换为可读名称
                    $category = str_replace('-', ' ', $category_slug);
                    $category = ucwords($category);
                }
            }
        }
        
        // 提取关键词/标签 - 根据您提供的HTML结构
        $tags = array();
        if (preg_match_all('/<span class="mr-2"><a[^>]*rel="tag"[^>]*>(.*?)<\/a>/is', $html, $matches)) {
            foreach($matches[1] as $tag) {
                $tags[] = trim(strip_tags($tag));
            }
            $keywords = implode(',', $tags);
        } elseif (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
            $keywords = trim($matches[1]);
        }
        
        // 提取图标 - 从og:image或其他图像元素
        if (preg_match('/<meta\s+property=["\']og:image["\']\s+content=["\']([^"\']+)["\']/is', $html, $matches)) {
            $icon = trim($matches[1]);
        } elseif (preg_match('/<link[^>]*rel=["\']icon["\']\s+href=["\']([^"\']+)["\']/is', $html, $matches)) {
            $icon = trim($matches[1]);
        }
        
        // 提取缩略图 - 从图片元素
        if (preg_match('/<img[^>]*class="[^"]*img-cover[^"]*"[^>]*data-src="([^"]+)"/is', $html, $matches)) {
            $thumb = trim($matches[1]);
        } elseif (preg_match('/<img[^>]*class="[^"]*img-cover[^"]*"[^>]*src="([^"]+)"/is', $html, $matches)) {
            $thumb = trim($matches[1]);
        }
        
        // 提取内容 - 从面板正文
        if (preg_match('/<div[^>]*class="[^"]*panel-body[^"]*"[^>]*>(.*?)<\/div>/is', $html, $matches)) {
            $content = trim($matches[1]);
            // 清理HTML标签，但保留基本格式
            $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/is', '', $content);
            $content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/is', '', $content);
        }
        
        // 提取功能特点 - 从features部分
        if (preg_match('/<h2[^>]*>特点功能<\/h2>(.*?)<h2/is', $html, $matches)) {
            $features_html = $matches[1];
            if (preg_match_all('/<li>(.*?)<\/li>/is', $features_html, $li_matches)) {
                foreach ($li_matches[1] as $feature) {
                    $features[] = trim(strip_tags($feature));
                }
            }
        }
        
        // 提取使用场景 - 从scenarios部分
        if (preg_match('/<h2[^>]*>应用场景<\/h2>(.*?)<h2/is', $html, $matches)) {
            $scenarios_html = $matches[1];
            if (preg_match_all('/<li>(.*?)<\/li>/is', $scenarios_html, $li_matches)) {
                foreach ($li_matches[1] as $scenario) {
                    $usage_scenarios[] = trim(strip_tags($scenario));
                }
            }
        } elseif (preg_match('/<h2[^>]*>应用场景<\/h2>(.*?)$/is', $html, $matches)) {
            // 如果是最后一个部分，没有后续的h2
            $scenarios_html = $matches[1];
            if (preg_match_all('/<li>(.*?)<\/li>/is', $scenarios_html, $li_matches)) {
                foreach ($li_matches[1] as $scenario) {
                    $usage_scenarios[] = trim(strip_tags($scenario));
                }
            }
        }
        
        // 提取价格信息 - 从country-piece类
        if (preg_match('/<div[^>]*class="[^"]*country-piece[^"]*"[^>]*>(.*?)<\/div>/is', $html, $matches)) {
            $price_info = trim(strip_tags($matches[1]));
        }
        
        // 提取官网URL - 从访问官网按钮
        if (preg_match('/<a[^>]*href="([^"]+)"[^>]*class="[^"]*btn-arrow[^"]*"[^>]*>.*?访问官网.*?<\/a>/is', $html, $matches)) {
            $official_url = trim($matches[1]);
        }
        
        // 确保URL为完整路径
        if ($icon && strpos($icon, 'http') !== 0) {
            if (strpos($icon, '//') === 0) {
                $icon = 'https:' . $icon;
            } else if (strpos($icon, '/') === 0) {
                $icon = 'https://ai-bot.cn' . $icon;
            } else {
                $icon = 'https://ai-bot.cn/' . $icon;
            }
        }
        
        if ($thumb && strpos($thumb, 'http') !== 0) {
            if (strpos($thumb, '//') === 0) {
                $thumb = 'https:' . $thumb;
            } else if (strpos($thumb, '/') === 0) {
                $thumb = 'https://ai-bot.cn' . $thumb;
            } else {
                $thumb = 'https://ai-bot.cn/' . $thumb;
            }
        }
        
        // 下载图标和缩略图
        $icon_file = '';
        $thumb_file = '';
        
        // 实际API采集时会通过控制器执行下载操作
        
        $result = [
            'code' => 1, 
            'msg' => '采集成功', 
            'data' => [
                'title' => $title,
                'description' => $description,
                'keywords' => $keywords,
                'icon' => $icon,
                'thumb' => $thumb,
                'url' => $url,
                'official_url' => $official_url,
                'price_info' => $price_info,
                'category' => $category,
                'features' => $features,
                'usage_scenarios' => $usage_scenarios
            ]
        ];
        
        // 模拟调用API
        // 这里我们只是测试，所以不实际下载图片，但在真实使用时会有下载的文件ID
        if (isset($_POST['simulate_download']) && $_POST['simulate_download'] == 1) {
            $result['data']['icon_file'] = '模拟的图标文件ID';
            $result['data']['thumb_file'] = '模拟的缩略图文件ID';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI工具集采集测试工具</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 900px;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .card {
            margin-bottom: 20px;
        }
        .result-container {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
        }
        .field-name {
            font-weight: bold;
            color: #495057;
        }
        .field-value {
            color: #212529;
            word-break: break-word;
        }
        .mb-3 {
            margin-bottom: 1rem;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center">AI工具集采集测试工具</h1>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">输入采集网址</h5>
            </div>
            <div class="card-body">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="url">AI工具集网址：</label>
                        <input type="url" class="form-control" id="url" name="url" placeholder="请输入AI工具集网站的URL，例如：https://ai-bot.cn/sites/33687.html" value="<?php echo htmlspecialchars($url); ?>" required>
                        <small class="form-text text-muted">输入AI工具集网站的URL，点击"开始采集"按钮测试采集效果</small>
                    </div>
                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="simulate_download" name="simulate_download" value="1" checked>
                        <label class="form-check-label" for="simulate_download">模拟下载图标和缩略图（仅测试用）</label>
                    </div>
                    <button type="submit" class="btn btn-primary">开始采集</button>
                </form>
            </div>
        </div>
        
        <?php if (!empty($result)): ?>
        <div class="card">
            <div class="card-header bg-<?php echo $result['code'] ? 'success' : 'danger'; ?> text-white">
                <h5 class="mb-0">采集结果：<?php echo htmlspecialchars($result['msg']); ?></h5>
            </div>
            <div class="card-body">
                <?php if ($result['code']): ?>
                <div class="result-container">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <div class="field-name">标题：</div>
                                <div class="field-value"><?php echo htmlspecialchars($result['data']['title']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="field-name">描述：</div>
                                <div class="field-value"><?php echo htmlspecialchars($result['data']['description']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="field-name">关键词：</div>
                                <div class="field-value"><?php echo htmlspecialchars($result['data']['keywords']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="field-name">采集网址：</div>
                                <div class="field-value"><?php echo htmlspecialchars($result['data']['url']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="field-name">官方网址：</div>
                                <div class="field-value"><?php echo htmlspecialchars($result['data']['official_url']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="field-name">价格信息：</div>
                                <div class="field-value"><?php echo htmlspecialchars($result['data']['price_info']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="field-name">分类：</div>
                                <div class="field-value"><?php echo htmlspecialchars($result['data']['category']); ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="field-name">图标：</div>
                                <div class="field-value">
                                    <?php if (!empty($result['data']['icon_file'])): ?>
                                    <p><strong>已下载的图标文件：</strong> <?php echo htmlspecialchars($result['data']['icon_file']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($result['data']['icon'])): ?>
                                    <p><strong>原始图标URL：</strong> <?php echo htmlspecialchars($result['data']['icon']); ?></p>
                                    <img src="<?php echo htmlspecialchars($result['data']['icon']); ?>" alt="图标" style="max-width: 100px; max-height: 100px;">
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="field-name">缩略图：</div>
                                <div class="field-value">
                                    <?php if (!empty($result['data']['thumb_file'])): ?>
                                    <p><strong>已下载的缩略图文件：</strong> <?php echo htmlspecialchars($result['data']['thumb_file']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($result['data']['thumb'])): ?>
                                    <p><strong>原始缩略图URL：</strong> <?php echo htmlspecialchars($result['data']['thumb']); ?></p>
                                    <img src="<?php echo htmlspecialchars($result['data']['thumb']); ?>" alt="缩略图" style="max-width: 200px; max-height: 200px;">
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($result['data']['features'])): ?>
                            <div class="mb-3">
                                <div class="field-name">功能特点：</div>
                                <div class="field-value">
                                    <ul>
                                        <?php foreach ($result['data']['features'] as $feature): ?>
                                        <li><?php echo htmlspecialchars($feature); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($result['data']['usage_scenarios'])): ?>
                            <div class="mb-3">
                                <div class="field-name">使用场景：</div>
                                <div class="field-value">
                                    <ul>
                                        <?php foreach ($result['data']['usage_scenarios'] as $scenario): ?>
                                        <li><?php echo htmlspecialchars($scenario); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-danger">
                    <?php echo htmlspecialchars($result['msg']); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">使用说明</h5>
            </div>
            <div class="card-body">
                <p>此工具用于测试从AI工具集网站采集内容的功能。步骤如下：</p>
                <ol>
                    <li>在上方输入框中输入AI工具集网站的URL，例如：<code>https://ai-bot.cn/sites/33687.html</code></li>
                    <li>点击"开始采集"按钮，系统将解析页面内容并展示结果</li>
                    <li>您可以查看采集到的各项内容，包括标题、描述、关键词、图标、缩略图等</li>
                    <li>在实际使用时，系统将自动下载图标和缩略图，并将其保存到附件库中</li>
                </ol>
                <p class="text-muted">注意：此工具仅用于测试采集功能，实际使用时请通过正常的内容发布流程进行采集。</p>
            </div>
        </div>
    </div>
</body>
</html> 