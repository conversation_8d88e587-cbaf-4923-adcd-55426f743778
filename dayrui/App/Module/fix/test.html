<!DOCTYPE html>
<html>
<head>
    <title>采集功能测试</title>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/layer.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="text"] { width: 100%; padding: 8px; }
        button { padding: 8px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        .progress { height: 20px; background-color: #f5f5f5; border-radius: 4px; margin-bottom: 15px; overflow: hidden; }
        .progress-bar { float: left; width: 0; height: 100%; line-height: 20px; color: #fff; text-align: center; background-color: #337ab7; }
        .result { margin-top: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>采集功能测试</h1>
        
        <div class="form-group">
            <label for="dr_fetch_url">要采集的网址：</label>
            <input type="text" id="dr_fetch_url" placeholder="请输入要采集的网址，例如：https://ai-bot.cn/sites/33687.html">
        </div>
        
        <div class="form-group">
            <label for="dr_fetch_source">采集源：</label>
            <select id="dr_fetch_source">
                <option value="aibot" selected>AI工具集</option>
            </select>
        </div>
        
        <button id="fetch_url_btn">开始采集</button>
        
        <div id="progress_container" style="display:none;">
            <h3>采集进度</h3>
            <div class="progress">
                <div id="dr_fetch_progress" class="progress-bar" role="progressbar" style="width: 0%">
                    <span id="dr_fetch_progress_text">准备采集...</span>
                </div>
            </div>
        </div>
        
        <div id="result" class="result" style="display:none;">
            <h3>采集结果</h3>
            <div id="result_content"></div>
        </div>
    </div>
    
    <script>
        // 定义全局变量
        window.MODULE_NAME = 'webnav';
        
        // 定义辅助函数
        function dr_tips(code, msg) {
            alert(msg);
        }
        
        function dr_ajax_alert_error(HttpRequest, ajax, thrownError) {
            alert('发生错误：' + thrownError);
        }
        
        // 测试全局函数是否正确定义
        $(function() {
            console.log('测试页面已加载');
            
            // 采集按钮点击事件
            $('#fetch_url_btn').on('click', function() {
                console.log('采集按钮被点击');
                if (typeof window.dr_fetch_url === 'function') {
                    window.dr_fetch_url();
                } else {
                    alert('dr_fetch_url 函数未定义！请检查JavaScript文件是否正确加载。');
                }
            });
        });
    </script>
    
    <!-- 引入采集功能脚本 -->
    <script src="fetch_url_fix.js"></script>
</body>
</html> 