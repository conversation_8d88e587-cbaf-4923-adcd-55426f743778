// 添加调试日志
console.log('fetch_url_fix.js 文件已加载');

// 全局定义采集网址函数
window.dr_fetch_url = function() {
    console.log('dr_fetch_url 函数被调用');
    
    var url = $('#dr_fetch_url').val();
    if (!url) {
        dr_tips(0, '请输入要采集的网址');
        return;
    }
    
    var source = $('#dr_fetch_source').val();
    
    // 显示进度条
    var progress_html = '<div class="progress progress-striped active">' +
        '<div id="dr_fetch_progress" class="progress-bar progress-bar-info" role="progressbar" style="width: 10%">' +
        '<span id="dr_fetch_progress_text">正在准备采集...</span>' +
        '</div></div>';
    
    layer.open({
        type: 1,
        title: '采集进度',
        closeBtn: 0,
        area: ['500px', '80px'],
        shade: 0.3,
        id: 'dr_fetch_progress_layer',
        btn: [],
        content: progress_html
    });
    
    // 更新进度
    function updateProgress(percent, text) {
        $('#dr_fetch_progress').css('width', percent + '%');
        $('#dr_fetch_progress_text').html(text);
    }
    
    // 开始采集
    updateProgress(20, '正在获取网页内容...');
    
    $.ajax({
        type: "GET",
        url: "/index.php?s="+window.MODULE_NAME+"&c=home&m=fetch_url&url="+encodeURIComponent(url)+"&source="+source,
        dataType: "json",
        success: function(json) {
            updateProgress(60, '正在解析内容...');
            
            setTimeout(function() {
                updateProgress(80, '正在填充数据...');
                
                if (json.code == 1) {
                    // 填充表单
                    if (json.data.title) {
                        $('#dr_title').val(json.data.title);
                    }
                    
                    if (json.data.description) {
                        // 查找内容编辑器或描述字段
                        if ($('#dr_description').length > 0) {
                            $('#dr_description').val(json.data.description);
                        } else if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances.dr_content) {
                            CKEDITOR.instances.dr_content.setData(json.data.description);
                        } else if (typeof UE !== 'undefined' && UE.getEditor('dr_content')) {
                            UE.getEditor('dr_content').setContent(json.data.description);
                        }
                    }
                    
                    // 填充关键词
                    if (json.data.keywords) {
                        $('#dr_keywords').val(json.data.keywords);
                    }
                    
                    // 填充URL
                    if (json.data.url) {
                        $('#dr_url').val(json.data.url);
                    }
                    
                    // 填充官网地址
                    if (json.data.official_url && $('#dr_official_url').length > 0) {
                        $('#dr_official_url').val(json.data.official_url);
                    }
                    
                    // 填充缩略图（优先使用下载后的文件）
                    if (json.data.thumb_file && $('#dr_thumb').length > 0) {
                        $('#dr_thumb').val(json.data.thumb_file);
                        $('.my_thumb_img').attr('src', json.data.thumb_file);
                        console.log('使用已下载的缩略图文件：', json.data.thumb_file);
                    } else if (json.data.thumb && $('#dr_thumb').length > 0) {
                        $('#dr_thumb').val(json.data.thumb);
                        $('.my_thumb_img').attr('src', json.data.thumb);
                        console.log('使用原始缩略图URL：', json.data.thumb);
                    } else if (json.data.icon_file && $('#dr_thumb').length > 0) {
                        // 如果没有缩略图，但有图标，则使用图标作为缩略图
                        $('#dr_thumb').val(json.data.icon_file);
                        $('.my_thumb_img').attr('src', json.data.icon_file);
                        console.log('使用已下载的图标文件作为缩略图：', json.data.icon_file);
                    } else if (json.data.icon && $('#dr_thumb').length > 0) {
                        $('#dr_thumb').val(json.data.icon);
                        $('.my_thumb_img').attr('src', json.data.icon);
                        console.log('使用原始图标URL作为缩略图：', json.data.icon);
                    }
                    
                    // 填充图标
                    if (json.data.icon_file && $('#dr_icon').length > 0) {
                        $('#dr_icon').val(json.data.icon_file);
                        console.log('使用已下载的图标文件：', json.data.icon_file);
                    } else if (json.data.icon && $('#dr_icon').length > 0) {
                        $('#dr_icon').val(json.data.icon);
                        console.log('使用原始图标URL：', json.data.icon);
                    }
                    
                    // 填充分类
                    if (json.data.category && $('#dr_category').length > 0) {
                        $('#dr_category').val(json.data.category);
                    }
                    
                    // 填充价格信息
                    if (json.data.price_info) {
                        if ($('#dr_price').length > 0) {
                            $('#dr_price').val(json.data.price_info);
                        } else if ($('#dr_fee').length > 0) {
                            $('#dr_fee').val(json.data.price_info);
                        }
                    }
                    
                    // 填充详细内容
                    if (json.data.content) {
                        if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances.dr_content) {
                            CKEDITOR.instances.dr_content.setData(json.data.content);
                        } else if (typeof UE !== 'undefined' && UE.getEditor('dr_content')) {
                            UE.getEditor('dr_content').setContent(json.data.content);
                        } else if ($('#dr_content').length > 0) {
                            $('#dr_content').val(json.data.content);
                        }
                    }
                    
                    // 尝试填充功能特点和应用场景到自定义字段（如果存在）
                    if (json.data.features && json.data.features.length > 0) {
                        var features_str = json.data.features.join("\n");
                        if ($('#dr_features').length > 0) {
                            $('#dr_features').val(features_str);
                        }
                    }
                    
                    if (json.data.usage_scenarios && json.data.usage_scenarios.length > 0) {
                        var scenarios_str = json.data.usage_scenarios.join("\n");
                        if ($('#dr_usage_scenarios').length > 0) {
                            $('#dr_usage_scenarios').val(scenarios_str);
                        }
                    }
                    
                    // 完成进度
                    updateProgress(100, '采集完成！');
                    setTimeout(function() {
                        layer.closeAll();
                        dr_tips(1, '采集成功');
                    }, 500);
                } else {
                    layer.closeAll();
                    dr_tips(0, json.msg);
                }
            }, 500);
        },
        error: function(HttpRequest, ajaxOptions, thrownError) {
            layer.closeAll();
            dr_ajax_alert_error(HttpRequest, this, thrownError);
        }
    });
};

// 确保DOM加载完成后绑定事件
$(function() {
    // 如果采集按钮存在，绑定点击事件
    if ($('#fetch_url_btn').length > 0) {
        $('#fetch_url_btn').off('click').on('click', function() {
            dr_fetch_url();
        });
    }
    
    console.log('采集功能已增强，支持图标和缩略图自动下载');
}); 