<?php
/**
 * 采集错误收集工具
 * 
 * 此脚本用于收集和显示采集过程中的错误信息
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义错误日志文件
define('ERROR_LOG_FILE', __DIR__ . '/crawler_errors.txt');

// 记录错误信息
function log_error($message, $source = 'general', $url = '') {
    $time = date('Y-m-d H:i:s');
    $log = "[$time] [$source] " . ($url ? "URL: $url - " : "") . "$message\n";
    file_put_contents(ERROR_LOG_FILE, $log, FILE_APPEND);
    return $log;
}

// 测试采集URL
function test_fetch_url($url, $source = 'aibot') {
    log_error("开始测试采集: $url", 'test', $url);
    
    try {
        // 初始化curl
        $ch = curl_init();
        
        // 设置URL
        curl_setopt($ch, CURLOPT_URL, $url);
        
        // 设置用户代理
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // 返回结果而非输出
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // 允许重定向
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        // 超时设置
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        // SSL验证设置
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        // 执行请求
        $html = curl_exec($ch);
        
        // 检查是否有错误
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            log_error("CURL错误: $error", 'curl', $url);
            curl_close($ch);
            return ['success' => false, 'message' => "CURL错误: $error"];
        }
        
        // 获取HTTP状态码
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($http_code != 200) {
            log_error("HTTP状态码错误: $http_code", 'http', $url);
            curl_close($ch);
            return ['success' => false, 'message' => "HTTP状态码错误: $http_code"];
        }
        
        // 关闭CURL会话
        curl_close($ch);
        
        // 检查HTML内容
        $html_length = strlen($html);
        if ($html_length < 100) {
            log_error("获取的HTML内容过短: $html_length 字节", 'html', $url);
            return ['success' => false, 'message' => "获取的HTML内容过短: $html_length 字节"];
        }
        
        log_error("成功获取HTML内容，长度: $html_length 字节", 'success', $url);
        
        return ['success' => true, 'message' => "成功获取内容，大小: $html_length 字节", 'html_preview' => substr($html, 0, 500)];
        
    } catch (Exception $e) {
        log_error("异常: " . $e->getMessage(), 'exception', $url);
        return ['success' => false, 'message' => "异常: " . $e->getMessage()];
    }
}

// 测试AJAX请求
function test_ajax_request($url, $source = 'aibot') {
    log_error("开始测试AJAX请求: $url", 'ajax', $url);
    
    try {
        // 构建完整的API URL
        $api_url = "/index.php?s=webnav&c=home&m=fetch_url&url=" . urlencode($url) . "&source=$source";
        
        // 初始化curl
        $ch = curl_init();
        
        // 设置URL
        curl_setopt($ch, CURLOPT_URL, $api_url);
        
        // 设置用户代理
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // 返回结果而非输出
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // 超时设置
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        // SSL验证设置
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        // 执行请求
        $response = curl_exec($ch);
        
        // 检查是否有错误
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            log_error("AJAX请求CURL错误: $error", 'ajax_curl', $url);
            curl_close($ch);
            return ['success' => false, 'message' => "AJAX请求CURL错误: $error"];
        }
        
        // 获取HTTP状态码
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // 关闭CURL会话
        curl_close($ch);
        
        if ($http_code != 200) {
            log_error("AJAX请求HTTP状态码错误: $http_code", 'ajax_http', $url);
            return ['success' => false, 'message' => "AJAX请求HTTP状态码错误: $http_code"];
        }
        
        // 尝试解析JSON响应
        $json = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            log_error("AJAX响应不是有效的JSON: " . json_last_error_msg(), 'ajax_json', $url);
            return ['success' => false, 'message' => "AJAX响应不是有效的JSON: " . json_last_error_msg(), 'response' => $response];
        }
        
        log_error("成功获取AJAX响应", 'ajax_success', $url);
        
        return ['success' => true, 'message' => "成功获取AJAX响应", 'data' => $json];
        
    } catch (Exception $e) {
        log_error("AJAX请求异常: " . $e->getMessage(), 'ajax_exception', $url);
        return ['success' => false, 'message' => "AJAX请求异常: " . $e->getMessage()];
    }
}

// 测试JavaScript错误
function check_js_errors() {
    log_error("开始检查JavaScript错误", 'js');
    
    $results = [];
    
    // 检查fetch_url_fix.js文件
    $js_file = __DIR__ . '/fetch_url_fix.js';
    
    if (!file_exists($js_file)) {
        $error = "JavaScript文件不存在: $js_file";
        log_error($error, 'js_file');
        $results[] = [
            'success' => false,
            'message' => $error
        ];
    } else {
        $content = file_get_contents($js_file);
        
        // 检查文件内容
        if (preg_match('/<html|<!DOCTYPE|<body|<head/i', $content)) {
            $error = "JavaScript文件中包含HTML标签";
            log_error($error, 'js_content');
            $results[] = [
                'success' => false,
                'message' => $error,
                'preview' => substr($content, 0, 200)
            ];
        } else {
            $results[] = [
                'success' => true,
                'message' => "JavaScript文件格式正确",
                'size' => strlen($content)
            ];
        }
        
        // 检查dr_fetch_url函数定义
        if (!preg_match('/window\.dr_fetch_url\s*=\s*function/i', $content)) {
            $error = "未找到window.dr_fetch_url函数定义";
            log_error($error, 'js_function');
            
            // 检查是否被注释
            if (preg_match('/\/\*.*?function\s+dr_fetch_url\s*\(\s*\).*?\*\//s', $content)) {
                $error .= "（函数被注释掉了）";
            }
            
            $results[] = [
                'success' => false,
                'message' => $error
            ];
        } else {
            $results[] = [
                'success' => true,
                'message' => "dr_fetch_url函数定义正确"
            ];
        }
    }
    
    return $results;
}

// 页面输出
function output_error_collector($fetch_result = null, $ajax_result = null, $js_results = []) {
    $errors = file_exists(ERROR_LOG_FILE) ? file_get_contents(ERROR_LOG_FILE) : '暂无错误记录';
    
    echo '<!DOCTYPE html>
<html>
<head>
    <title>采集错误收集工具</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1000px; margin: 0 auto; }
        h1, h2, h3 { color: #333; }
        .section { background: #f5f5f5; border: 1px solid #ddd; padding: 15px; margin-top: 15px; border-radius: 4px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], select { width: 100%; padding: 8px; box-sizing: border-box; }
        button { padding: 10px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; margin-right: 10px; }
        .error-log { background: #f5f5f5; border: 1px solid #ddd; padding: 15px; margin-top: 15px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow: auto; }
        .success { color: green; }
        .error { color: red; }
        .code { font-family: monospace; background: #eee; padding: 10px; overflow: auto; white-space: pre-wrap; }
        .tabs { display: flex; margin-bottom: -1px; }
        .tab { padding: 10px 15px; border: 1px solid #ddd; background: #f9f9f9; margin-right: 5px; cursor: pointer; border-radius: 4px 4px 0 0; }
        .tab.active { background: #fff; border-bottom: 1px solid #fff; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
    <script>
        function switchTab(tabId) {
            // 隐藏所有内容
            var contents = document.getElementsByClassName("tab-content");
            for(var i = 0; i < contents.length; i++) {
                contents[i].classList.remove("active");
            }
            
            // 移除所有标签的active类
            var tabs = document.getElementsByClassName("tab");
            for(var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove("active");
            }
            
            // 显示选中的内容并激活标签
            document.getElementById(tabId).classList.add("active");
            document.querySelector(\'[onclick="switchTab(\\\'\' + tabId + \'\\\');"]\').classList.add("active");
        }
    </script>
</head>
<body>
    <h1>采集错误收集工具</h1>
    
    <div class="tabs">
        <div class="tab active" onclick="switchTab(\'tab-collector\');">错误收集器</div>
        <div class="tab" onclick="switchTab(\'tab-js\');">JavaScript检查</div>
        <div class="tab" onclick="switchTab(\'tab-logs\');">错误日志</div>
    </div>
    
    <div id="tab-collector" class="tab-content active">
        <div class="section">
            <h2>采集URL测试</h2>
            <form method="post" action="">
                <div class="form-group">
                    <label for="url">要采集的网址：</label>
                    <input type="text" id="url" name="url" placeholder="请输入要采集的网址" value="' . (isset($_POST['url']) ? htmlspecialchars($_POST['url']) : '') . '">
                </div>
                
                <div class="form-group">
                    <label for="source">采集源：</label>
                    <select id="source" name="source">
                        <option value="aibot" ' . (isset($_POST['source']) && $_POST['source'] == 'aibot' ? 'selected' : '') . '>AI工具集</option>
                        <option value="default" ' . (isset($_POST['source']) && $_POST['source'] == 'default' ? 'selected' : '') . '>默认源</option>
                    </select>
                </div>
                
                <button type="submit" name="action" value="fetch">测试直接访问</button>
                <button type="submit" name="action" value="ajax">测试AJAX请求</button>
            </form>
        </div>';
    
    if ($fetch_result) {
        echo '<div class="section">
            <h3>直接访问结果</h3>';
        if ($fetch_result['success']) {
            echo '<p class="success">✓ ' . htmlspecialchars($fetch_result['message']) . '</p>';
            if (isset($fetch_result['html_preview'])) {
                echo '<h4>HTML预览：</h4>
                <div class="code">' . htmlspecialchars($fetch_result['html_preview']) . '...</div>';
            }
        } else {
            echo '<p class="error">✗ ' . htmlspecialchars($fetch_result['message']) . '</p>';
        }
        echo '</div>';
    }
    
    if ($ajax_result) {
        echo '<div class="section">
            <h3>AJAX请求结果</h3>';
        if ($ajax_result['success']) {
            echo '<p class="success">✓ ' . htmlspecialchars($ajax_result['message']) . '</p>';
            if (isset($ajax_result['data'])) {
                echo '<h4>响应数据：</h4>
                <div class="code">' . htmlspecialchars(json_encode($ajax_result['data'], JSON_PRETTY_PRINT)) . '</div>';
            }
        } else {
            echo '<p class="error">✗ ' . htmlspecialchars($ajax_result['message']) . '</p>';
            if (isset($ajax_result['response'])) {
                echo '<h4>原始响应：</h4>
                <div class="code">' . htmlspecialchars($ajax_result['response']) . '</div>';
            }
        }
        echo '</div>';
    }
    
    echo '</div>
    
    <div id="tab-js" class="tab-content">
        <div class="section">
            <h2>JavaScript文件检查</h2>
            <form method="post" action="">
                <button type="submit" name="action" value="check_js">检查JavaScript</button>
            </form>';
    
    if (!empty($js_results)) {
        echo '<h3>检查结果：</h3>';
        foreach ($js_results as $result) {
            if ($result['success']) {
                echo '<p class="success">✓ ' . htmlspecialchars($result['message']) . '</p>';
            } else {
                echo '<p class="error">✗ ' . htmlspecialchars($result['message']) . '</p>';
                if (isset($result['preview'])) {
                    echo '<div class="code">' . htmlspecialchars($result['preview']) . '...</div>';
                }
            }
        }
    }
    
    echo '</div>
        
        <div class="section">
            <h3>修复建议</h3>
            <ol>
                <li>确保 <code>fetch_url_fix.js</code> 文件存在且格式正确</li>
                <li>检查函数定义是否被注释，应该使用 <code>window.dr_fetch_url = function() {...}</code> 格式</li>
                <li>使用绝对URL路径引用JavaScript文件</li>
                <li>检查网站根URL（ROOT_URL）是否正确定义</li>
                <li>使用 <a href="fix_js_error.php">JavaScript错误修复工具</a> 自动修复问题</li>
            </ol>
        </div>
    </div>
    
    <div id="tab-logs" class="tab-content">
        <div class="section">
            <h2>错误日志</h2>
            <form method="post" action="">
                <button type="submit" name="action" value="clear_logs">清除日志</button>
            </form>
            <div class="error-log">' . htmlspecialchars($errors) . '</div>
        </div>
    </div>
</body>
</html>';
}

// 处理请求
$fetch_result = null;
$ajax_result = null;
$js_results = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'fetch':
            if (!empty($_POST['url'])) {
                $url = $_POST['url'];
                $source = $_POST['source'] ?? 'aibot';
                $fetch_result = test_fetch_url($url, $source);
            }
            break;
        
        case 'ajax':
            if (!empty($_POST['url'])) {
                $url = $_POST['url'];
                $source = $_POST['source'] ?? 'aibot';
                $ajax_result = test_ajax_request($url, $source);
            }
            break;
        
        case 'check_js':
            $js_results = check_js_errors();
            break;
        
        case 'clear_logs':
            if (file_exists(ERROR_LOG_FILE)) {
                unlink(ERROR_LOG_FILE);
            }
            log_error("日志已清除");
            break;
    }
}

// 输出页面
output_error_collector($fetch_result, $ajax_result, $js_results); 