{template "header.html"}
<div class="note note-danger">
    {template "top.html"}
    <p>本教程仅能调用在定义的模块字段：设置-网站信息-自定义字段</p>
</div>

<script>
    function to_tag(name) {
        $.ajax({type: "POST",dataType:"json", url: '{dr_url("mbdy/site/tag")}&mid={$mid}&id='+name, data: $('#myform_'+name).serialize(),
            success: function(json) {
                if (json.code == 1) {
                    $('#result').html(json.msg)
                } else {
                    dr_cmf_tips(0, json.msg);
                }
                return false;
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
</script>

<div class="clearfix margin-bottom-20"></div>


<div class="portlet light bordered">
    <div class="portlet-title">
        <div class="caption">
            <span class="caption-subject font-green-sharp">
                网站信息字段
            </span>
            <span class="caption-helper">
               用于网站信息自定义字段输出
            </span>
        </div>
    </div>
    <div class="portlet-body form">
        <form class="form-horizontal" id="myform_2">
            {dr_form_hidden()}
            <div class="form-body">

                <div class="form-group">
                    <label class="col-md-2 control-label">选择字段</label>
                    <div class="col-md-9">
                        <label>

                            <select class="form-control" name="field">
                                {loop $field $f $v}
                                <option value="{$f}">{$v.name}（{$v.fieldname}）</option>
                                {/loop}
                            </select></label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">生成结果</label>
                    <div class="col-md-9" id="result">

                    </div>
                </div>

            </div>

            <div class="form-actions">
                <div class="row">
                    <div class="col-md-offset-2 col-md-9">
                        <button type="button" onclick="to_tag(2)" class="btn green">生成标签</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>



{template "footer.html"}