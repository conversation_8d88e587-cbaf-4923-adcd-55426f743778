{template "header.html"}
<div class="note note-danger">
    {template "top.html"}
</div>

<script>
    function to_tag(name) {
        $.ajax({type: "POST",dataType:"json", url: '{dr_now_url()}', data: $('#myform').serialize(),
            success: function(json) {
                if (json.code == 1) {
                    $("#ok").html(json.msg);
                    $("#ok2").html(json.data.field);
                    $("#return").val(json.data.return);
                    $("#mid").val(json.data.mid);
                } else {
                    dr_cmf_tips(0, json.msg);
                }
                return false;
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
    function to_field() {
        var name = '{php echo \Phpcmf\Service::M()->dbprefix(SITE_ID)}_'+$("#dr_module").val();
        dr_iframe_show('可用字段', '{dr_url('mbdy/db/show_index')}&id='+name);
    }
    function to_category() {
        var name = $("#dr_module").val();
        dr_iframe_show('可用字段', '{SELF}?s='+name+'&c=category');
    }
    function to_flag() {
        var name = $("#dr_module").val();
        dr_iframe_show('可用字段', '{dr_url('module/module/flag_edit')}&at=flag&dir='+name);
    }
    function to_field_tag(name) {
        var mid = $("#mid").val();
        if (!mid) {
            dr_tips(0, '模块未选择');
            return;
        }
        $.ajax({type: "POST",dataType:"json", url: '{dr_url("mbdy/module/tag")}&mid='+mid+'&id='+name, data: $('#myform_'+name).serialize(),
            success: function(json) {
                if (json.code == 1) {
                    layer.open({
                        type: 1,
                        title: "调用标签",
                        fix:true,
                        //scrollbar: false,
                        shadeClose: true,
                        shade: 0,
                        area: ['400px', '500px'],
                        content: "<div style='padding: 10px'>"+json.msg+"</div>"
                    });
                } else {
                    dr_cmf_tips(0, json.msg);
                }
                return false;
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
</script>

<div class="portlet light bordered">
    <div class="portlet-title">
        <div class="caption">
            <span class="caption-subject font-green-sharp">自定义字段标签</span>
        </div>
    </div>
    <div class="portlet-body form">
        <form class="form-horizontal" id="myform">
            {dr_form_hidden()}
            <div class="form-body">


                <div class="form-group">
                    <label class="col-md-2 control-label">指定前缀</label>
                    <div class="col-md-9">
                        <label><input type="text" class="form-control" name="data[return]" value=""></label>
                        {code}
                        <span class="help-block"> 如果在循环体里面，需要设置变量的前缀，例如t </span>
                        <span class="help-block"> 如果指定过return=x时，就填写x </span>
                        <span class="help-block"> 如果在本身页面直接可以用{$xxx}这样格式输出的时，就不填写 </span>
                        {/code}
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">字段类别</label>
                    <div class="col-md-9">
                        <label>
                            <select class="form-control" name="data[type]">

                            </select>
                        </label>
                        <span class="help-block">  </span>
                    </div>
                </div>



            </div>

            <div class="form-actions">
                <div class="row">
                    <div class="col-md-offset-2 col-md-9">
                        <button type="button" onclick="to_tag()" class="btn green">生成循环标签</button>
                        <button type="button" onclick="dr_help(15)" class="btn red">直接看手册</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>


<div class="row">

    <div class="col-md-6">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <span class="caption-subject font-green-sharp">生成代码</span>
                </div>
            </div>
            <div class="portlet-body form" id="ok">

            </div>
        </div>

    </div>
    <div class="col-md-6">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <span class="caption-subject font-green-sharp">生成字段标签</span>
                </div>
            </div>
            <div class="portlet-body form">
                <form class="form-horizontal" id="myform_2">
                    {dr_form_hidden()}
                    <input id="mid" type="hidden" value="">
                    <input type="hidden" id="return" name="return" value="t">
                    <div class="form-body">

                        <div class="form-group">
                            <label class="col-md-2 control-label">选择字段</label>
                            <div class="col-md-9">
                                <label id="ok2"></label>
                            </div>
                        </div>

                    </div>

                    <div class="form-actions">
                        <div class="row">
                            <div class="col-md-offset-2 col-md-9">
                                <button type="button" onclick="to_field_tag(2)" class="btn green">生成标签</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



{template "footer.html"}