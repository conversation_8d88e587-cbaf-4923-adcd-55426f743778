{template "header.html"}
<div class="note note-danger">
    {template "top.html"}
</div>

<script>
    function to_tag(name) {
        $.ajax({type: "POST",dataType:"json", url: '{dr_now_url()}', data: $('#myform').serialize(),
            success: function(json) {
                if (json.code == 1) {
                    $("#ok").html(json.msg);
                    $("#ok2").html(json.data.field);
                    $("#return").val(json.data.return);
                    $("#mid").val(json.data.mid);
                } else {
                    dr_cmf_tips(0, json.msg);
                }
                return false;
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
    function to_field() {
        var name = '{php echo \Phpcmf\Service::M()->dbprefix(SITE_ID)}_'+$("#dr_module").val();
        dr_iframe_show('可用字段', '{dr_url('mbdy/db/show_index')}&id='+name);
    }
    function to_category() {
        var name = $("#dr_module").val();
        dr_iframe_show('可用字段', '{SELF}?s='+name+'&c=category');
    }
    function to_flag() {
        var name = $("#dr_module").val();
        dr_iframe_show('可用字段', '{dr_url('module/module/flag_edit')}&at=flag&dir='+name);
    }
    function to_field_tag(name) {
        var mid = $("#mid").val();
        if (!mid) {
            dr_tips(0, '模块未选择');
            return;
        }
        $.ajax({type: "POST",dataType:"json", url: '{dr_url("mbdy/module/tag")}&mid='+mid+'&id='+name, data: $('#myform_'+name).serialize(),
            success: function(json) {
                if (json.code == 1) {
                    layer.open({
                        type: 1,
                        title: "调用标签",
                        fix:true,
                        //scrollbar: false,
                        shadeClose: true,
                        shade: 0,
                        area: ['400px', '500px'],
                        content: "<div style='padding: 10px'>"+json.msg+"</div>"
                    });
                } else {
                    dr_cmf_tips(0, json.msg);
                }
                return false;
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
</script>

<div class="portlet light bordered">
    <div class="portlet-title">
        <div class="caption">
            <span class="caption-subject font-green-sharp">模块内容循环标签</span>
        </div>
    </div>
    <div class="portlet-body form">
        <form class="form-horizontal" id="myform">
            {dr_form_hidden()}
            <div class="form-body">

                <div class="form-group">
                    <label class="col-md-2 control-label">选择模块</label>
                    <div class="col-md-9">
                        <label>
                            <select class="form-control" name="data[module]" id="dr_module">
                                <option value="">--</option>
                                {cache name=module}
                                <option {if $mid==$t.dirname} selected{/if} value="{$t.dirname}">{$t.name}（{$t.dirname}）</option>
                                {/cache}
                            </select>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">指定ID</label>
                    <div class="col-md-9">
                        <label><input type="text" class="form-control" name="data[id]" value=""></label>
                        <span class="help-block"> 可以填写单个id；多个id时以,分隔开 </span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">指定栏目ID</label>
                    <div class="col-md-9">
                        <label><input type="text" class="form-control" name="data[catid]" value="{$catid}"></label>
                        <label><button type="button" onclick="to_category()" class="btn red">可用栏目</button></label>
                        <span class="help-block"> 调用指定栏目的文章，多个栏目按,号分隔，如果在当前栏目下调用输入：$catid </span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">指定推荐位ID</label>
                    <div class="col-md-9">
                        <label><input type="text" class="form-control" name="data[flag]" value=""></label>
                        <label><button type="button" onclick="to_flag()" class="btn red">可用推荐位</button></label>
                        <span class="help-block"> 调用指定推荐位的文章，多个推荐位按,号分隔 </span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">最大数量</label>
                    <div class="col-md-9">
                        <label><input type="text" class="form-control" name="data[num]" value=""></label>
                        <span class="help-block"> 本次循环的最大数量限制 </span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">是否显示栏目模型字段</label>
                    <div class="col-md-9">
                        <div class="mt-radio-inline">
                            <label class="mt-radio"><input type="radio" name="data[lmmx]" value="1"  /> 是 <span></span></label>
                            <label class="mt-radio"><input type="radio" name="data[lmmx]" value="0" checked /> 否 <span></span></label>
                        </div>
                        <span class="help-block"> 会影响查询效率 </span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">是否显示附表字段</label>
                    <div class="col-md-9">
                        <div class="mt-radio-inline">
                            <label class="mt-radio"><input type="radio" name="data[fb]" value="1"  /> 是 <span></span></label>
                            <label class="mt-radio"><input type="radio" name="data[fb]" value="0" checked /> 否 <span></span></label>
                        </div>
                        <span class="help-block"> 会影响查询效率 </span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">排序字段方式</label>
                    <div class="col-md-9">
                        <label><input type="text" class="form-control " name="data[order]" id="dr_order" value=""></label>
                        <label><div class="mt-radio-inline">
                            <label class="mt-radio"><input type="radio" name="data[order_by]" value="1"  /> 从小到大 <span></span></label>
                            <label class="mt-radio"><input type="radio" name="data[order_by]" value="0" checked /> 从大到小 <span></span></label>
                        </div></label>
                        <label><button type="button" onclick="to_field()" class="btn red">可用字段</button></label>
                        <span class="help-block"> 按什么字段排序 </span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">标签return值</label>
                    <div class="col-md-9">
                        <label><input type="text" class="form-control" name="data[return]" value="t"></label>
                        <span class="help-block"> 循环标签的return默认返回变量为t，调用方式就是<?php echo '{'?>$t.字段值}（多级查询必须设置return=其他字母） </span>
                    </div>
                </div>

            </div>

            <div class="form-actions">
                <div class="row">
                    <div class="col-md-offset-2 col-md-9">
                        <button type="button" onclick="to_tag()" class="btn green">生成循环标签</button>
                        <button type="button" onclick="dr_help(15)" class="btn red">直接看手册</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>


<div class="row">

    <div class="col-md-6">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <span class="caption-subject font-green-sharp">生成代码</span>
                </div>
            </div>
            <div class="portlet-body form" id="ok">

            </div>
        </div>

    </div>
    <div class="col-md-6">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <span class="caption-subject font-green-sharp">生成字段标签</span>
                </div>
            </div>
            <div class="portlet-body form">
                <form class="form-horizontal" id="myform_2">
                    {dr_form_hidden()}
                    <input id="mid" type="hidden" value="">
                    <input type="hidden" id="return" name="return" value="t">
                    <div class="form-body">

                        <div class="form-group">
                            <label class="col-md-2 control-label">选择字段</label>
                            <div class="col-md-9">
                                <label id="ok2"></label>
                            </div>
                        </div>

                    </div>

                    <div class="form-actions">
                        <div class="row">
                            <div class="col-md-offset-2 col-md-9">
                                <button type="button" onclick="to_field_tag(2)" class="btn green">生成标签</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



{template "footer.html"}