{template "header.html"}
<div class="note note-danger">
    {template "top.html"}
    <p>由于迅睿cms字段比较丰富，没有识别的字段条件，请联系小波QQ1344680861来接入</p>
</div>

<script>
    function to_tag(name) {
        $.ajax({type: "POST",dataType:"json", url: '{dr_url("mbdy/search/tag")}&mid={$mid}&id='+name, data: $('#myform_'+name).serialize(),
            success: function(json) {
                if (json.code == 1) {
                    $('#where').html(json.msg);
                    $('#code').html(json.data);
                } else {
                    dr_cmf_tips(0, json.msg);
                }
                return false;
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
    function to_field_tag(name) {

        $.ajax({type: "POST",dataType:"json", url: '{dr_url("mbdy/module/tag")}&mid={$mid}&id=2', data: $('#myform_'+name).serialize(),
            success: function(json) {
                if (json.code == 1) {
                    layer.open({
                        type: 1,
                        title: "调用标签",
                        fix:true,
                        //scrollbar: false,
                        shadeClose: true,
                        shade: 0,
                        area: ['400px', '500px'],
                        content: "<div style='padding: 10px'>"+json.msg+"</div>"
                    });
                } else {
                    dr_cmf_tips(0, json.msg);
                }
                return false;
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
</script>
<div class="tabbable-line">
    <ul class="nav nav-tabs" style="float:left;">
        {loop $module $dir $m}
        <li class="{if $dir==$mid}active{/if}">
            <a href="{dr_url("mbdy/search/index")}&mid={$dir}" > <i class="{dr_icon($m.icon)}"></i> {$m.name} </a>
        </li>
        {/module}
    </ul>
</div>
<div class="clearfix margin-bottom-20"></div>

<div class="row">
    <div class="col-md-12">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <span class="caption-subject font-green-sharp">
                        【 {$my.name}（{$my.dirname}）】 搜索筛选条件
                    </span>
                    <span class="caption-helper">
                        用于搜索筛选的条件写法
                    </span>
                </div>
            </div>
            <div class="portlet-body form">
                    <div class="form-body">

                        <form class="form-horizontal" id="myform_1">
                            {dr_form_hidden()}
                            <div class="form-group">
                                <label class="col-md-2 control-label">选择字段</label>
                                <div class="col-md-9">
                                    <label>
                                        {$sfield}
                                    </label>
                                    <label>
                                        <button type="button" onclick="to_tag(1)" class="btn green">生成条件标签</button>
                                    </label>
                                </div>
                            </div>
                        </form>

                        <form class="form-horizontal" id="myform_2">
                            {dr_form_hidden()}
                            <div class="form-group">
                                <label class="col-md-2 control-label">选择字段</label>
                                <div class="col-md-9">
                                    <label>
                                        {$afield}
                                    </label>
                                    <label>
                                        <select class="form-control" name="order">
                                            <option value="0">大在前，小在后</option>
                                            <option value="1">小在前，大在后</option>
                                        </select>
                                    </label>
                                    <label>
                                        <button type="button" onclick="to_tag(2)" class="btn green">生成排序标签</button>
                                    </label>
                                </div>
                            </div>
                        </form>

                        <form class="form-horizontal" id="myform_3">
                            {dr_form_hidden(['field'=>1])}
                            <div class="form-group">
                                <label class="col-md-2 control-label">&nbsp;</label>
                                <div class="col-md-9">
                                    <label>
                                        生成已选择的条件值
                                    </label>
                                    <label>
                                        <button type="button" onclick="to_tag(3)" class="btn green">生成选择标签</button>
                                    </label>
                                </div>
                            </div>
                        </form>

                    </div>

            </div>
        </div>
    </div>

    <div class="col-md-12">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <span class="caption-subject font-green-sharp">生成的语句</span>
                </div>
            </div>
            <div class="portlet-body form">
                <textarea  class="form-control" style="width: 100%;height: 300px" id="where"></textarea>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption">
                    <span class="caption-subject font-green-sharp">循环标签</span>
                </div>
            </div>
            <div class="portlet-body form">
                <textarea  class="form-control" style="width: 100%;height: 250px" id="code"></textarea>
            </div>
            <form class="form-horizontal" id="myform_4" style="margin-top: 10px">
                {dr_form_hidden()}
                <input id="mid" type="hidden" value="{$mid}">
                <input type="hidden" id="return" name="return" value="rs">

                <label>{$field}</label>
                <label><button type="button" onclick="to_field_tag(4)" class="btn green">生成字段标签</button></label>
            </form>
        </div>

    </div>
</div>
{template "footer.html"}