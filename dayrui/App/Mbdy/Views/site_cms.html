{template "header.html"}

<script>
    function to_tag(name) {
        $('#res').html('');
        $.ajax({type: "POST",dataType:"json", url: '{dr_url("mbdy/site/tag")}&mid={$mid}&id='+name, data: $('#myform_'+name).serialize(),
            success: function(json) {
                if (json.code == 1) {
                    $('#res').html(json.msg);
                } else {
                    dr_cmf_tips(0, json.msg);
                }
                return false;
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
</script>
<pre>
{nl2br($code)}</pre>
<hr>
<form class="form-horizontal" id="myform_3">
    {dr_form_hidden(['id'=>$id])}
    <div class="form-body"><label>
        全局调用网站字段信息：
    </label>
        <label>
            <select class="form-control" name="field">
                <option>选择字段</option>
                {loop $field $f $v}
                <option value="{$f}">{$v.name}（{$v.fieldname}）</option>
                {/loop}
            </select>
        </label>
        <label><button class="btn blue" onclick="to_tag(3)" type="button">生成标签</button></label>

    </div>

</form>
<div class="clearfix margin-bottom-20"></div>

<pre id="res">  </pre>
{template "footer.html"}