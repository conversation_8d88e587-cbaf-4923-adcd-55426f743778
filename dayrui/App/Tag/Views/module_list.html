{template "header.html"}

<div class="right-card-box">
{template "api_list_date_search.html"}
<div class="row table-search-tool margin-top-20">
    <form action="{SELF}" method="get">
        {dr_form_search_hidden(['mid'=>$mid,'tid'=>$tid, 'is_iframe'=>1])}
        <div class="col-md-12 col-sm-12">
            <label><input type="text" class="form-control" placeholder="" value="{$kw}" name="kw" /></label>
        </div>

        <div class="col-md-12 col-sm-12">
            <button type="submit" class="btn blue btn-sm onloading" name="submit" > <i class="fa fa-search"></i> {dr_lang('搜索')}</button>
        </div>

        <div class="col-md-12 col-sm-12" style="padding-top:2px">
            <a class="btn green btn-sm" href="javascript:dr_add_related();"> <i class="fa fa-plus"></i> {dr_lang('关联')}</a>
        </div>

    </form>
</div>

    <script>

        function dr_add_related() {

            var url = "{$rurl}";
            layer.open({
                type: 2,
                title: '<i class="fa fa-cog"></i> 关联内容',
            fix:true,
                shadeClose: true,
                shade: 0,
                area: ["95%", "90%"],
                btn: ["关联"],
                success: function (json) {
                if (json.code == 0) {
                    layer.close();
                    dr_tips(json.code, json.msg);
                }
            },
            yes: function(index, layero){
                var body = layer.getChildFrame('body', index);
                // 延迟加载
                var loading = layer.load(2, {
                    time: 10000
                });
                $.ajax({type: "POST",dataType:"json", url: '{dr_now_url()}', data: $(body).find('#myform').serialize(),
                success: function(json) {
                    layer.close(loading);
                    if (json.code == 1) {
                        layer.close(index);
                        dr_tips(1, json.msg);
                        setTimeout("window.location.reload(true)", 3000)
                    } else {
                        dr_tips(0, json.msg);

                    }
                    return false;
                }
            });

            return false;
        },
            content: url
        });


        }

    </script>


<form class="form-horizontal" role="form" id="myform">
    {dr_form_hidden()}
    <div class="table-scrollable">
        <table class="table table-striped table-bordered table-hover table-checkable dataTable">
            <tbody>
            {module module=$mid where=$where page=1 pagesize=$pszie urlrule=$urlrule}

            <tr class="odd gradeX" id="dr_row_{$t.id}">
                {if $ci->_is_admin_auth('del') }
                <td class="myselect">
                    <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                        <input type="checkbox" class="checkboxes" name="ids[]" value="{$t.id}" />
                        <span></span>
                    </label>
                </td>
                {/if}
                <td>{Function_list::title($t.title, $param, $t)}
                </td>
            </tr>
            {/loop}
            </tbody>
        </table>
    </div>


         <div class="row fc-list-footer table-checkable ">
             <div class="col-md-5 fc-list-select">
                {if $ci->_is_admin_auth('del') || $ci->_is_admin_auth('edit')}
                <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                    <input type="checkbox" class="group-checkable" data-set=".checkboxes" />
                    <span></span>
                </label>
                <label><button type="button" onclick="dr_ajax_option('{dr_url('tag/home/<USER>', ['mid'=>$mid, 'tid'=>$tid])}', '{dr_lang('你确定要删除它们吗？')}', 1)" class="btn red btn-sm"> <i class="fa fa-trash"></i> {dr_lang('删除')}</button></label>
                 <label><button type="button" onclick="dr_ajax_option('{dr_url('tag/home/<USER>', ['mid'=>$mid, 'tid'=>$tid])}', '{dr_lang('你确定要清空全部吗？')}', 1)" class="btn red btn-sm"> <i class="fa fa-trash"></i> {dr_lang('全部删除')}</button>
                 </label>
                 {/if}
            </div>
             <div class="col-md-7 fc-list-page">
                 {$pages}
             </div>
         </div>
</form>
</div>

{template "footer.html"}