{template "header.html"}

<div class="row table-search-tool">
    <form action="{SELF}" method="get">
        {$search}
        <div class="col-xs-12">
            <label><input type="text" class="form-control" placeholder="" value="{$param['keyword']}" name="keyword" /></label>

            <label><button type="submit" class="btn blue btn-sm onloading" name="submit" > <i class="fa fa-search"></i> {dr_lang('搜索')}</button></label>
        </div>
    </form>
</div>


<form class="form-horizontal" role="form" id="myform">
    {dr_form_hidden()}
    <input type="hidden" name="isajax" value="1">
    <div class="table-scrollable">
        <table class="table table-striped table-bordered table-hover table-checkable dataTable">
            <thead>
            <tr class="heading">
                <th class="myselect">
                    <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                        <input type="checkbox" class="group-checkable" data-set=".checkboxes" />
                        <span></span>
                    </label>
                </th>
                <th>{dr_lang('Tag')}</th>
            </tr>
            </thead>
            <tbody>
            {table table_site=tag  where=$where order=updatetime page=1 pagesize=$pagesize urlrule=$urlrule}
            <tr class="odd gradeX" id="dr_row_{$t.id}">
                <td class="myselect">
                    <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                        <input type="checkbox" class="checkboxes" name="ids[]" value="{$t.id}" />
                        <span></span>
                    </label>
                </td>
                <td>{$t.name}</td>
            </tr>
            {/module}
            </tbody>
        </table>
    </div>

    <div class="row fc-list-footer ">
        <div class="col-md-12 text-center">
            {$pages}
        </div>
    </div>
</form>
<script>
    $(function () {
        $('.pagination a').click(function () {
            if ($('.checkboxes').is(':checked')) {
                var url = $(this).attr('href');
                var msg = '{dr_lang('离开本页面后，当前选择的项目将被取消')}';
                layer.confirm(
                    msg,
                    {
                        icon: 3,
                        shade: 0,
                        title: lang['ts'],
                        btn: [lang['ok'], lang['esc']]
                    }, function(index){
                        window.location.href = url;
                        return true;
                    });
                return false;
            }

        });
    })
</script>
{template "footer.html"}