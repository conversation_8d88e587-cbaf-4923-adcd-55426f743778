{template "header.html"}
<div class="note note-danger">
    <p><a href="javascript:dr_update_cache('tag', '');">{dr_lang('更改数据之后需要更新缓存之后才能生效')}</a></p>
</div>


<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="row myfbody">
        <div class="col-md-12">

            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <span class="caption-subject font-green">{if $pid}{dr_lang('批量添加子词')}{else}{dr_lang('批量添加父词')}{/if}</span>
                    </div>

                    <div class="actions">
                        <div class="btn-group">
                            <a class="btn" href="{$reply_url}"> <i class="fa fa-mail-reply"></i> {dr_lang('返回列表')}</a>
                        </div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="form-body">

                        <div class="form-group ">
                            <label class="col-md-2 control-label">{dr_lang('批量')}</label>
                            <div class="col-md-9">
                                <textarea name="all" class="form-control" style="height:220px" rows="3"></textarea>

                                <span class="help-block"> {dr_lang('换行分隔多条数据')} </span>
                            </div>
                        </div>
                    </div>
                    {$myfield}
                </div>
            </div>

            

        </div>
    </div>

    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <button type="button" onclick="dr_ajax_submit('{dr_now_url()}', 'myform', '2000')" class="btn green"> <i class="fa fa-save"></i> {dr_lang('保存内容')}</button>
            <button type="button" onclick="dr_ajax_submit('{dr_now_url()}', 'myform', '2000', '{$reply_url}')" class="btn yellow"> <i class="fa fa-mail-reply-all"></i> {dr_lang('保存并返回')}</button>
        </div>
    </div>
</form>


<link href="{THEME_PATH}assets/global/plugins/bootstrap-touchspin/bootstrap.touchspin.css" rel="stylesheet" type="text/css" />
<script src="{THEME_PATH}assets/global/plugins/fuelux/js/spinner.min.js" type="text/javascript"></script>
<script src="{THEME_PATH}assets/global/plugins/bootstrap-touchspin/bootstrap.touchspin.js" type="text/javascript"></script>
<script type="text/javascript">
    $(function(){
        $("#hits").TouchSpin({
            buttondown_class: "btn red",
            buttonup_class: "btn green",
            min: 0,
            max: 99999999
        });
    });
</script>
{template "footer.html"}