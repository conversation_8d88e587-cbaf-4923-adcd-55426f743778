{template "header.html"}

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="portlet bordered light myfbody">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-cog"></i> {dr_lang('基本设置')} </a>
                </li>
                {if $diy_tpl}
                <li class=" {if $page==2}active{/if}">
                    <a href="#tab_2" data-toggle="tab" onclick="$('#dr_page').val('2')"> <i class="fa fa-cog"></i> {dr_lang('自定义设置')} </a>
                </li>
                {/if}
                <li class=" {if $page==1}active{/if}">
                    <a href="#tab_1" data-toggle="tab" onclick="$('#dr_page').val('1')"> <i class="fa fa-table"></i> {dr_lang('后台显示')} </a>
                </li>
                <li class=" {if $page==3}active{/if}">
                    <a href="#tab_3" data-toggle="tab" onclick="$('#dr_page').val('3')"> <i class="fa fa-volume-up"></i> {dr_lang('通知提醒')} </a>
                </li>
                {if IS_USE_MEMBER}
                <li class="{if $page==4}active{/if}">
                    <a href="#tab_4" data-toggle="tab" onclick="$('#dr_page').val('4')"> <i class="fa fa-user"></i> {dr_lang('用户中心')} </a>
                </li>
                {/if}
                <li class="{if $page==5}active{/if}">
                    <a href="#tab_5" data-toggle="tab" onclick="$('#dr_page').val('5')"> <i class="fa fa-internet-explorer"></i> {dr_lang('前端SEO设置')} </a>
                </li>
                <li class=" {if $page==6}active{/if}">
                    <a href="#tab_6" data-toggle="tab" onclick="$('#dr_page').val('6')"> <i class="fa fa-user"></i> {dr_lang('发布权限设置')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">

                <div class="tab-pane {if 6 == $page}active{/if}" id="tab_6">
                    <div class="form-body">

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('前端权限')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio">
                                        <input type="radio" name="data[setting][web]" value="0" {if !$data['setting']['web']} checked{/if}> {dr_lang('公共表单')}
                                        <span></span>
                                    </label>
                                    <label class="mt-radio">
                                        <input type="radio" name="data[setting][web]" value="1" {if $data['setting']['web']} checked{/if} > {dr_lang('私有表单')}
                                        <span></span>
                                    </label>
                                </div>
                                <span class="help-block"> {dr_lang('私有表单不允许前端访问表单列表和查看表单内容')} </span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('权限划分')}</label>
                            <div class="col-md-9">

                                <div class="table-scrollable ">
                                    <table class="table table-striped table-bordered  table-checkable dataTable">
                                        <tbody>
                                        <?php
 $group = [ 0 => dr_lang('游客') ];
                                        if ($ci->member_cache['group']) {
                                        foreach ($ci->member_cache['group'] as $t) {
                                        $group[$t['id']] = dr_lang($t['name']);
                                        }
                                        }
                                        ?>
                                        {loop $group $i $t}
                                        <tr class="odd gradeX">
                                            <td width="200" style="text-align: left;padding-left:10px;">{$t}</td>
                                            <td>

                                                <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                                                    {dr_lang('发表')}
                                                    <input type="checkbox" class="checkboxes" name="data[setting][post_add][{$i}]" value="1" {if $data['setting']['post_add'][$i]}checked{/if} />
                                                    <span></span>
                                                </label>&nbsp;&nbsp;&nbsp;&nbsp;
                                                <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                                                    {dr_lang('审核')}
                                                    <input type="checkbox" class="checkboxes" name="data[setting][post_verify][{$i}]" value="1" {if $data['setting']['post_verify'][$i]}checked{/if} />
                                                    <span></span>
                                                </label>&nbsp;&nbsp;&nbsp;&nbsp;
                                                <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                                                    {dr_lang('验证码')}
                                                    <input type="checkbox" class="checkboxes" name="data[setting][post_code][{$i}]" value="1" {if $data['setting']['post_code'][$i]}checked{/if} />
                                                    <span></span>
                                                </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                {if dr_is_app('explog')}
                                                <div class="input-inline input-small hide">
                                                    <div class="input-group">
                                                        <input type="text" name="data[setting][post_exp][{$i}]" value="{$data['setting']['post_exp'][$i]}" class="form-control" placeholder="">
                                                        <span class="input-group-addon">+{SITE_EXPERIENCE}</span>
                                                    </div>
                                                </div>&nbsp;&nbsp;&nbsp;&nbsp;
                                                {/if}
                                                {if dr_is_app('scorelog')}
                                                <div class="input-inline input-small hide">
                                                    <div class="input-group">
                                                        <input type="text" name="data[setting][post_score][{$i}]" value="{$data['setting']['post_score'][$i]}" class="form-control" placeholder="">
                                                        <span class="input-group-addon">+{SITE_SCORE}</span>
                                                    </div>
                                                </div>
                                                {/if}

                                            </td>
                                        </tr>
                                        {/loop}
                                        </tbody>
                                    </table>
                                </div>


                            </div>
                        </div>




                    </div>
                </div>

                <div class="tab-pane {if $page==5}active{/if}" id="tab_5">
                    <div class="form-body">

                        <?php !$data['setting']['seo']['title'] && $data['setting']['seo']['title'] = '{title}{join}{formname}{join}{'.'SITE_NAME}';?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('内容SEO标题')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="data[setting][seo][title]">{$data['setting']['seo']['title']}</textarea>
                                <span class="help-block">
                                    <button class="btn btn-xs green" onclick="dr_seo_title_rule()" type="button"><i class="fa fa-code"></i> {dr_lang('可用通配符标签')}</button>
                                    <script>
                                        function dr_seo_title_rule() {
                                            layer.alert('通用标签<br>'+
                                                '{join}	SEO连接符号，默认“_”<br>'+
                                                '[{page}]	分页页码<br>'+
                                                '{formname}	表单名称<br>'+
                                                '支持“网站表单表”任何字段，格式：{字段名}，<br>如：{title}表示标题<br>'+
                                                '支持网站系统常量，格式：{大写的常量名称}，<br>如：{SITE_NAME}表示网站名称<br>'+
                                                ''+
                                                '', {
                                                shade: 0,
                                                title: '',
                                                btn: []
                                            });
                                        }
                                    </script>
                                </span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('内容SEO关键字')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="data[setting][seo][keywords]">{$data['setting']['seo']['keywords']}</textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('内容SEO描述信息')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="data[setting][seo][description]">{$data['setting']['seo']['description']}</textarea>
                            </div>
                        </div>


                    </div>
                </div>

                <div class="tab-pane {if $page==4}active{/if}" id="tab_4">
                    <div class="form-body">


                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('用户管理内容')}</label>
                            <div class="col-md-9">
                                <input type="checkbox" name="data[setting][is_member]" value="1" {if $data['setting']['is_member']}checked{/if} data-on-text="{dr_lang('已开启')}" data-off-text="{dr_lang('已关闭')}" data-on-color="success" data-off-color="danger" class="make-switch" data-size="small">
                                <span class="help-block">{dr_lang('用户中心的内容管理菜单下可以管理自己提交的内容')}</span>
                            </div>
                        </div>


                    </div>
                </div>

                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('表单别名')}</label>
                            <div class="col-md-9">
                                <div class="form-control-static"><label><span class="label label-success"> {$data.table} </span></label></div>
                            </div>
                        </div>
                        <div class="form-group" id="dr_row_name">
                            <label class="col-md-2 control-label ">{dr_lang('表单名称')}</label>
                            <div class="col-md-9">
                                <label><input type="text" class="form-control" id="dr_name" name="data[name]" value="{htmlspecialchars((string)$data.name)}"></label>
                                <span class="help-block"> {dr_lang('表单的描述名称')} </span>
                            </div>
                        </div>
                        <div class="form-group" id="dr_row_icon">
                            <label class="col-md-2 control-label ">{dr_lang('菜单图标')}</label>
                            <div class="col-md-9">
                                <div class="input-group" style="width:250px">
                                    <input class="form-control" id="dr_icon" type="text" name="data[setting][icon]" value="{htmlspecialchars((string)$data['setting']['icon'])}" />
                            <span class="input-group-btn">
                                <a class="btn btn-success" href="{dr_url('api/icon')}" target="_blank"><i class="fa fa-arrow-right fa-fw" /></i> {dr_lang('查看')}</a>
                            </span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('开发模式')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio">
                                        <input type="radio" onclick="dr_dev_show( 0)" name="data[setting][dev]" value="0" {if !$data['setting']['dev']} checked{/if}> {dr_lang('系统默认')}
                                        <span></span>
                                    </label>
                                    <label class="mt-radio">
                                        <input type="radio" onclick="dr_dev_show(1)" name="data[setting][dev]" value="{if $data['setting']['dev']}{$data['setting']['dev']}{else}1{/if}" {if $data['setting']['dev']} checked{/if} > {dr_lang('自定义')}
                                        <span></span>
                                    </label>
                                </div>
                                <span class="help-block"> {dr_lang('自定义模式时本表单不会自动生成到菜单之中，需要开发者手动写入菜单')} </span>
                            </div>
                        </div>



                        <div class="form-group ">
                            <label class="col-md-2 control-label">{dr_lang('提交成功提示文字')}</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control input-xlarge" name="data[setting][rt_text]" value="{htmlspecialchars((string)$data['setting']['rt_text'])}" >
                                <span class="help-block"> {dr_lang('当用户提交表单成功之后显示的文字，默认为：操作成功')} </span>
                            </div>
                        </div>

                        <div class="form-group ">
                            <label class="col-md-2 control-label">{dr_lang('提交审核提示文字')}</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control input-xlarge" name="data[setting][rt_text2]" value="{htmlspecialchars((string)$data['setting']['rt_text2'])}" >
                                <span class="help-block"> {dr_lang('当用户提交表单审核时显示的文字，默认为：操作成功，等待管理员审核')} </span>
                            </div>
                        </div>
                        <div class="form-group ">
                            <label class="col-md-2 control-label">{dr_lang('提交成功跳转URL')}</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control input-xlarge" name="data[setting][rt_url]" value="{htmlspecialchars((string)$data['setting']['rt_url'])}" >
                                <span class="help-block"> {dr_lang('当用户提交表单成功之后跳转的链接，{id}表示当前表单的id号')} </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane {if $page==1}active{/if}" id="tab_1">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('列表搜索框')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[setting][is_hide_search_bar]" value="0" {if empty($data['setting']['is_hide_search_bar'])}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="data[setting][is_hide_search_bar]" value="1" {if $data['setting']['is_hide_search_bar']}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('开启后，当进入列表时直接显示搜索框；关闭后，需要点右上角的搜索按钮才会出现')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('列表显示字段')}</label>
                            <div class="col-md-9">
                        <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                            <thead>
                            <tr class="heading">
                                <th class="myselect">
                                    {dr_lang('显示')}
                                </th>
                                <th width="180"> {dr_lang('字段')} </th>
                                <th width="100"> {dr_lang('类别')} </th>
                                <th width="150"> {dr_lang('名称')} </th>
                                <th width="100"> {dr_lang('宽度')} </th>
                                <th width="140"> {dr_lang('对其方式')} </th>
                                <th> {dr_lang('回调方法')} </th>
                            </tr>
                            </thead>
                            <tbody class="field-sort-items">
                            {loop $field $n $t}
                            {if $t.name}
                            <tr class="odd gradeX">
                                <td class="myselect">
                                    <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                                        <input type="checkbox" class="checkboxes" name="data[setting][list_field][{$t.fieldname}][use]" value="1" {if $data['setting']['list_field'][$t.fieldname]['use']} checked{/if} />
                                        <span></span>
                                    </label>
                                </td>
                                <td>{dr_lang($t.name)} ({$t.fieldname})</td>
                                <td>{$t.fieldtype}</td>
                                <td><input class="form-control" type="text" name="data[setting][list_field][{$t.fieldname}][name]" value="{php echo $data['setting']['list_field'][$t.fieldname]['name'] ? htmlspecialchars($data['setting']['list_field'][$t.fieldname]['name']) : $t.name}" /></td>
                                <td> <input class="form-control" type="text" name="data[setting][list_field][{$t.fieldname}][width]" value="{htmlspecialchars((string)$data['setting']['list_field'][$t.fieldname]['width'])}" /></td>
                                <td><input type="checkbox" name="data[setting][list_field][{$t.fieldname}][center]" {if $data['setting']['list_field'][$t.fieldname]['center']} checked{/if} value="1"  data-on-text="{dr_lang('居中')}" data-off-text="{dr_lang('默认')}" data-on-color="success" data-off-color="danger" class="make-switch" data-size="small">
                                </td>
                                <td> <div class="input-group" style="width:250px">
                                        <span class="input-group-btn">
                                            <a class="btn btn-success" href="javascript:dr_call_alert();">{dr_lang('回调')}</a>
                                        </span>
                                    <input class="form-control" type="text" name="data[setting][list_field][{$t.fieldname}][func]" value="{htmlspecialchars((string)$data['setting']['list_field'][$t.fieldname]['func'])}" />
                                </div></td>
                            </tr>
                            {/if}
                            {/loop}
                            </tbody>
                        </table>
                            </div>
                        </div>
                    </div>
                </div>

                {if $diy_tpl}
                <div class="tab-pane {if $page==2}active{/if}" id="tab_2">
                    <div class="form-body">
                         {load $diy_tpl}
                    </div>
                </div>
                {/if}

                <div class="tab-pane {if $page==3}active{/if}" id="tab_3">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('通知提醒')}</label>
                            <div class="col-md-9">
                                <input type="checkbox" name="data[setting][notice][use]" value="1" {if $data['setting']['notice']['use']}checked{/if} data-on-text="{dr_lang('已开启')}" data-off-text="{dr_lang('已关闭')}" data-on-color="success" data-off-color="danger" class="make-switch" data-size="small">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('通知模式')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio">
                                        <input type="radio" name="data[setting][notice][is_send]" value="0" {if !$data['setting']['notice']['is_send']} checked{/if}> {dr_lang('队列模式')}
                                        <span></span>
                                    </label>
                                    <label class="mt-radio">
                                        <input type="radio" name="data[setting][notice][is_send]" value="1" {if $data['setting']['notice']['is_send']} checked{/if} > {dr_lang('立即发送')}
                                        <span></span>
                                    </label>
                                </div>
                                <span class="help-block"> {dr_lang('立即发送在提交时会带来卡顿现象，推荐使用队列模式')} </span>
                            </div>
                        </div>
                        <div class="form-group" id="dr_row_username">
                            <label class="control-label col-md-2">{dr_lang('通知账号')}</label>
                            <div class="col-md-9">
								<input class="form-control " type="text" name="data[setting][notice][username]" value="{htmlspecialchars((string)$data['setting']['notice']['username'])}" style="width:80%;"   data-role="tagsinput" />
                                
                                <span class="help-block" id="dr_username_tips">{dr_lang('当有人提交表单时，会通知到这些账号')}</span>
                            </div>
                        </div>
                        {if IS_USE_MEMBER}
                        <div class="form-group">
                            <label class="control-label col-md-2">{dr_lang('通知方式')}</label>
                            <div class="col-md-9">

                                <div class="mt-checkbox-inline">
                                    <label class="mt-checkbox mt-checkbox-outline">
                                        <input type="checkbox" name="data[setting][notice][mobile]" value="1"  {if $data['setting']['notice']['mobile']}checked{/if}  /> {dr_lang('短信')} <span></span>
                                    </label>
                                    <label class="mt-checkbox mt-checkbox-outline">
                                        <input type="checkbox" name="data[setting][notice][email]" value="1"  {if $data['setting']['notice']['email']}checked{/if}  /> {dr_lang('邮件')} <span></span>
                                    </label>
                                    <label class="mt-checkbox mt-checkbox-outline">
                                        <input type="checkbox" name="data[setting][notice][weixin]" value="1"  {if $data['setting']['notice']['weixin']}checked{/if}  /> {dr_lang('微信')} <span></span>
                                    </label>
                                    <label class="mt-checkbox mt-checkbox-outline">
                                        <input type="checkbox" name="data[setting][notice][notice]" value="1"  {if $data['setting']['notice']['notice']}checked{/if}  /> {dr_lang('消息')} <span></span>
                                    </label>
                                    <label class="mt-checkbox mt-checkbox-outline">
                                        <a href="javascript:dr_iframe_show('{dr_lang('通知内容')}', '{dr_url('member/setting_notice/edit')}&file=form_{$data.table}_post', '90%','90%', 'nogo');">{dr_lang('内容设置')}</a>
                                    </label>

                                </div>
                            </div>
                        </div>
                        {else}
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('无法使用')}</label>
                            <div class="col-md-9">
                                <div class="form-control-static"><label><a class="label label-danger"> {dr_lang('本功能需要安装【用户系统】插件')} </a></label></div>
                            </div>
                        </div>
                        {/if}
                    </div>



                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn green"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
        </div>
    </div>
</form>
<script type="text/javascript">

    $(function () {
        dr_dev_show({intval($data['setting']['dev'])});
        $(".field-sort-items").sortable();
    });
    function dr_dev_show(u) {
        if (u) {
            $('.dev').hide();
        } else {
            $('.dev').show();
        }
    }
</script>
{template "footer.html"}