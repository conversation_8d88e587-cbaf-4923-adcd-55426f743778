{template "header.html"}

{template "api_list_date_search.html"}
<div class="note note-danger" {if !isset($get.submit) && !$is_show_search_bar}style="display: none"{/if} id="table-search-tool">

<div class="row table-search-tool">
        <form action="{SELF}" method="get">
            {dr_form_search_hidden()}
            <div class="col-md-12 col-sm-12">
                <label>
                    <select name="field" class="form-control">
                        <option value="id"> Id </option>
                        {loop $field $t}
                        {if dr_is_admin_search_field($t)}
                        <option value="{$t.fieldname}" {if $param.field==$t.fieldname}selected{/if}>{dr_lang($t.name)}</option>
                        {/if}
                        {/loop}
                    </select>
                </label>
                <label><i class="fa fa-caret-right"></i></label>
                <label><input type="text" class="form-control" placeholder="" value="{$param['keyword']}" name="keyword" /></label>
            </div>

            <div class="col-md-12 col-sm-12">
                <label>
                    <div class="input-group input-medium date-picker input-daterange" data-date="" data-date-format="yyyy-mm-dd">
                        <input type="text" class="form-control" value="{$param.date_form}" name="date_form">
                        <span class="input-group-addon"> {dr_lang('到')} </span>
                        <input type="text" class="form-control" value="{$param.date_to}" name="date_to">
                    </div>
                </label>
            </div>

            <div class="col-md-12 col-sm-12">
                <label><button id="table-search-tool-submit" type="button" class="btn blue btn-sm " name="submit" > <i class="fa fa-search"></i> {dr_lang('搜索')}</button></label>
                <label><button id="table-search-tool-reset" type="reset" class="btn red btn-sm " name="reset" > <i class="fa fa-refresh"></i> {dr_lang('重置')}</button></label>
            </div>
        </form>
    </div>
</div>

<div class="right-card-box">
    <div id="toolbar" class="toolbar">
    {$mytable.foot_tpl}
    </div>

    {template "mytable.html"}
</div>

{template "footer.html"}