{template "header.html"}
<div class="note note-danger">
    <p>{dr_lang('表单可以用于前端数据展示与收集，如留言板、反馈、证书展示，需要配合前端页面使用')}</p>
</div>

<div class="right-card-box">
    <form class="form-horizontal" role="form" id="myform">
        {dr_form_hidden()}
        {if $ci->_is_admin_auth('del')}
        <div class="bootstrap-table bootstrap-table2">
            <div id="toolbar" class="toolbar">
                <button type="button" onclick="dr_ajax_option('{dr_url('form/form/del')}', '{dr_lang('你确定要删除它们吗？')}', 1)" class="btn red btn-sm"> <i class="fa fa-trash"></i> {dr_lang('删除')}</button>

            </div>
        </div>   {/if}
        <div class="table-scrollable">
            <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                <thead>
                <tr class="heading">
                    {if $ci->_is_admin_auth('del')}
                    <th class="myselect">
                        <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                            <input type="checkbox" class="group-checkable" data-set=".checkboxes" />
                            <span></span>
                        </label>
                    </th>
                    {/if}
                    <th width="200"> {dr_lang('名称')} </th>
                    <th width="200"> {dr_lang('别名')} </th>
                    <th>  </th>
                </tr>
                </thead>
                <tbody>
                {loop $list $t}
                <?php $cg = dr_string2array($t['setting']);?>
                {if !$cg.dev || $cg.dev == 1}
                <tr class="odd gradeX" id="dr_row_{$t.id}">
                    {if $ci->_is_admin_auth('del')}
                    <td class="myselect">
                        <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                            <input type="checkbox" class="checkboxes" name="ids[]" value="{$t.id}" />
                            <span></span>
                        </label>
                    </td>
                    {/if}
                    <td>{dr_lang($t.name)}</td>
                    <td>{$t.table}</td>
                    <td>
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('已审核内容')}','{dr_url('form/'.$t['table'].'/index')}&is_menu=1', '80%', '90%');" class="btn btn-xs blue"> <i class="fa fa-table"></i> {dr_lang('已审核内容')} </a></label>
                        <label><a href="javascript:top.dr_iframe_show('{dr_lang('待审核内容')}','{dr_url('form/'.$t['table'].'_verify/index')}&is_menu=1', '80%', '90%');" class="btn btn-xs red"> <i class="fa fa-edit"></i> {dr_lang('待审核内容')} </a></label>
                        {if !$cg.dev}<label><a href="{SITE_URL}index.php?s=form&c={$t.table}&m=post" target="_blank" class="btn btn-xs yellow"> <i class="fa fa-send"></i> {dr_lang('预览')} </a></label>{/if}
                        {if $ci->_is_admin_auth('edit')}<label><a href="{dr_url('form/form/edit', ['id'=>$t.id])}" class="btn btn-xs green"> <i class="fa fa-edit"></i> {dr_lang('修改')} </a></label>{/if}
                        {if $ci->_is_admin_auth()}<label><a href="javascript:top.dr_iframe_show('{dr_lang('自定义字段')}','{dr_url('field/index', ['rname'=>'form-'.SITE_ID, 'rid'=>$t.id])}&is_menu=1', '80%', '90%');" class="btn btn-xs dark"> <i class="fa fa-code"></i> {dr_lang('自定义字段')} </a></label>{/if}
                        {if $ci->_is_admin_auth()}<label><a href="javascript:dr_iframe_show('{dr_lang('导出')}', '{dr_url('form/form/export', ['id'=>$t.id])}');" class="btn btn-xs red"> <i class="fa fa-sign-out"></i> {dr_lang('导出')} </a></label>{/if}
                    </td>
                </tr>
                {/if}
                {/loop}
                </tbody>
            </table>
        </div>

        {if $mypages}
        <div class="row fc-list-footer table-checkable ">
            <div class="col-md-12 fc-list-page">
                {$mypages}
            </div>
        </div>
        {/if}

    </form>
</div>

{template "footer.html"}