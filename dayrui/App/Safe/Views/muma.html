{template "header.html"}
<div class="note note-danger">
    <p>扫描文件之前需要进行文件差异对比一次，保证程序文件与最新版一致</p>
</div>

<div class="text-center">
    <button type="button" id="dr_check_button" onclick="dr_checking();" class="btn blue"> <i class="fa fa-refresh"></i> 立即扫描</button>
</div>

<div id="dr_check_result" class="margin-top-30" style="display: none">

</div>

<div id="dr_check_div"  class="well margin-top-30" style="display: none">
    <div class="scroller" style="height:300px" data-rail-visible="1"  id="dr_check_html">

    </div>
</div>

<div id="dr_check_ing" style="display: none">
    <div class="progress progress-striped">
        <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">

        </div>
    </div>
</div>

<div class="portlet light bordered" style="margin-top: 30px;">
    <div class="portlet-title">
        <div class="caption">
            <span class="caption-subject  sbold ">扫描结果</span>
        </div>

    </div>
    <div class="portlet-body" style="padding-bottom: 40px">
        <div id="dr_check_bf">

        </div>
    </div>
</div>



<script>
    function dr_checking() {
        $('#dr_check_button').attr('disabled', true);
        $('#dr_check_button').html('<i class="fa fa-refresh"></i> 准备中');
        $('#dr_check_bf').html("");
        $('#dr_check_html').html("正在准备中");
        $.ajax({
            type: "GET",
            dataType: "json",
            url: "{dr_url('safe/mm/php_count_index')}",
            success: function (json) {
                if (json.code == 0) {
                    dr_cmf_tips(0, json.msg);
                } else {
                    $('#dr_check_result').html($('#dr_check_ing').html());
                    $('#dr_check_div').show();
                    $('#dr_check_result').show();
                    dr_ajax2ajax(1);
                }
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
    function dr_ajax2ajax(page) {
        $.ajax({
            type: "GET",
            dataType: "json",
            url: "{dr_url('safe/mm/php_check_index')}&page="+page,
            success: function (json) {

                $('#dr_check_html').append(json.msg);
                document.getElementById('dr_check_html').scrollTop = document.getElementById('dr_check_html').scrollHeight;

                if (json.code == 0) {
                    $('#dr_check_button').attr('disabled', false);
                    $('#dr_check_button').html('<i class="fa fa-refresh"></i> 重新扫描');
                    dr_cmf_tips(0, '发现异常');
                } else {
                    $('#dr_check_result .progress-bar-success').attr('style', 'width:'+json.code+'%');
                    if (json.code == 100) {
                        $('#dr_check_button').attr('disabled', false);
                        $('#dr_check_button').html('<i class="fa fa-refresh"></i> 重新扫描');
                        // 对比结果
                        $("#dr_check_html .rbf").each(function(){
                            $('#dr_check_bf').append('<p>'+$(this).html()+'</p>');
                        });
                    } else {
                        $('#dr_check_button').html('<i class="fa fa-refresh"></i> 扫描中 '+json.code+'%');
                        dr_ajax2ajax(json.code);
                    }
                }
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
</script>

<style>
#dr_check_html .p_error {
    color: red;
}
#dr_check_html p {
    margin: 10px 0;
    clear: both;
}
#dr_check_html .rleft {
    float: left;
}
#dr_check_html .rright .ok {
    color: green;
}
#dr_check_html .rright .error {
    color: red;
}
#dr_check_html .rright {
    float: right;
}

#dr_check_bf .rright {
    float: right;
}
#dr_check_bf .rleft {
    float: left;
}
#dr_check_bf p {
    margin: 10px 0;
    clear: both;
    color: red;
}
</style>

{template "footer.html"}