{template "header.html"}

<div class="note note-danger">
    <p><a href="javascript:dr_update_cache();">表单提交页面活动输入的外链跳转进行安全验证</a></p>
</div>

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="portlet bordered light myfbody">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_1" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-link"></i> {dr_lang('安全外链')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">

                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">

                    <div class="form-body">

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('是否启用安全外链')}</label>
                            <div class="col-md-9">
                                <input type="checkbox" name="data[use]" value="1" {if $data['use']}checked{/if} data-on-text="{dr_lang('已开启')}" data-off-text="{dr_lang('已关闭')}" data-on-color="success" data-off-color="danger" class="make-switch" data-size="small">

                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('系统域名')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control" style="height:150px" readonly>{implode(PHP_EOL, $domain)}
                                </textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('域名白名单')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control" style="height:200px" name="data[domain]">{$data.domain}</textarea>
                                <span class="help-block">{dr_lang('用于站内向外跳转的链接验证，例如表单跳转、登录跳转、注册跳转等')}</span>
                            </div>
                        </div>

                    </div>
                </div>


            </div>
        </div>
    </div>

    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn green"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
        </div>
    </div>
</form>



{template "footer.html"}