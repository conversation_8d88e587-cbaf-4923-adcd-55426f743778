<?php namespace Phpcmf\Member;

/**
 * http://www.xunruicms.com
 * 本文件是框架系统文件，二次开发时不可以修改本文件，可以通过继承类方法来重写此文件
 **/

// 内容模块表单操作类 基于 Ftable
class Mform extends \Phpcmf\Table
{
    public $cid; // 内容id
    public $form; // 表单信息
    public $index; // 模块内容信息
    protected $is_verify; // 判断是否来自审核控制器
    protected $is_add_menu = 1; //允许有添加菜单

    // 上级公共类
    public function __construct() {
        parent::__construct();
        $this->_Extend_Init();
    }

    // 继承类初始化
    protected function _Extend_Init() {
        // 初始化模块
        $this->_module_init(APP_DIR);
        // 判断表单是否操作
        $this->form = $this->module['form'][\Phpcmf\Service::L('Router')->class];
        if (!$this->form) {
            $this->_msg(0, dr_lang('模块表单【%s】不存在', \Phpcmf\Service::L('Router')->class));
        } elseif (!$this->form['setting']['is_member']) {
            $this->_msg(0, dr_lang('模块表单【%s】没有开启管理内容功能', \Phpcmf\Service::L('Router')->class));
        }
        // 支持附表存储
        $this->is_data = 1;
        // 模板前缀(避免混淆)
        $this->tpl_prefix = 'mform_';
        // 单独模板命名
        $this->tpl_name = $this->form['table'];
        // 模块显示名称
        $this->name = dr_lang('内容模块[%s]表单（%s）', APP_DIR, $this->form['name']);
        // 获取父级内容
        $this->url_params['cid'] = $this->cid = intval(\Phpcmf\Service::L('input')->get('cid'));
        if ($this->cid) {
            $this->index = $this->content_model->get_data($this->cid);
            if ($this->index) {
                if (!$this->form['setting']['is_member_user'] && $this->index['uid'] != $this->uid) {
                    $this->_msg(0, dr_lang('模块表单【%s】父内容[%s]不是你创建', $this->form['name'], $this->cid));
                }
            } else {
                $this->_msg(0, dr_lang('模块表单【%s】父内容[%s]不存在', $this->form['name'], $this->cid));
            }
        } else {
            //$this->_msg(0, dr_lang('模块表单【%s】没有cid参数', $this->form['name']));
        }

        // 初始化数据表
        $this->_init([
            'field' => $this->form['field'],
            'table' => dr_mform_ctable(APP_DIR, $this->form['table'], $this->cid),
            'date_field' => 'inputtime',
            'show_field' => 'title',
            'list_field' => $this->form['setting']['list_field'],
            'order_by' => 'displayorder DESC,inputtime DESC',
            'where_list' => $this->cid ? 'cid='. $this->cid : ($this->form['setting']['is_member_user'] ? ' cid in (select id from '.
                \Phpcmf\Service::M()->dbprefix(SITE_ID.'_'.APP_DIR.'_index').' where uid='.$this->uid.')' : 'uid='.$this->uid), // 自定义条件，显示本内容的表单
        ]);
        $this->edit_where = $this->delete_where = $this->cid ? 'cid='. $this->cid : 'uid='.$this->uid;
        // 是否有验证码
        $this->is_post_code = $this->form['setting']['is_post_code'] ? 0 : 1;

        // 写入模板
        \Phpcmf\Service::V()->assign([
            'mform' => $this->form,
            'index' => $this->index,
            'field' => $this->init['field'],
            'form_url' => dr_member_url(APP_DIR.'/'.$this->form['table'].'/index', ['cid' =>  $this->cid]),
            'is_verify' => $this->is_verify,
            'is_post_code' => $this->is_post_code,
        ]);
    }

    // ========================

    // 查看列表
    protected function _Member_List() {

        $this->init['table'] = dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['id']);
        list($tpl) = $this->_List(['cid' => $this->cid]);

        \Phpcmf\Service::V()->assign([
            'p' => ['cid' => $this->cid],
            'is_delete' => 0,
        ]);
        return \Phpcmf\Service::V()->display($tpl);
    }

    // 添加内容
    protected function _Member_Add() {

        /*
        if (!\Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'add', $this->member)) {
            $this->_msg(0, dr_lang('您的用户组无发布权限'));
        }*/

        list($tpl) = $this->_Post(0);

        \Phpcmf\Service::V()->assign([
            'form' =>  dr_form_hidden(),
        ]);
        \Phpcmf\Service::V()->display($tpl);
    }

    // 修改内容
    protected function _Member_Edit() {

        /*
        if (!\Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'edit', $this->member)) {
            $this->_msg(0, dr_lang('您的用户组无修改权限'));
        }*/

        $id = intval(\Phpcmf\Service::L('input')->get('id'));
        list($tpl, $data) = $this->_Post($id);

        if (!$data) {
            $this->_msg(0, dr_lang('数据不存在: '.$id));
        } elseif ($this->cid != $data['cid']) {
            $this->_msg(0, dr_lang('所属主题cid不匹配'));
        }
        if ($this->form['setting']['is_member_user']) {
            if (!in_array($this->uid, [$data['uid'], $this->index['uid']])) {
                $this->_msg(0, dr_lang('无权限修改'));
            }
        } elseif ($this->uid != $data['uid']) {
            $this->_msg(0, dr_lang('无权限修改'));
        }

        \Phpcmf\Service::V()->assign([
            'form' =>  dr_form_hidden(),
        ]);
        \Phpcmf\Service::V()->display($tpl);
    }

    // 批量保存排序值
    protected function _Member_Order() {
        $this->_Display_Order(
            intval(\Phpcmf\Service::L('input')->get('id')),
            intval(\Phpcmf\Service::L('input')->get('value')),
            function ($t) {
                $table = dr_mform_ctable(APP_DIR, $this->form['table'], $t['cid']);
                if ($table != $this->init['table']) {
                    // 备用表
                    $value = intval(\Phpcmf\Service::L('input')->get('value'));
                    \Phpcmf\Service::M()->table($table)->update($t['id'], ['displayorder' => $value]);
                }
            }
        );
    }

    // 删除内容
    protected function _Member_Del() {

        /*
        if (!\Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'del', $this->member)) {
            $this->_msg(0, dr_lang('您的用户组无删除权限'));
        }*/

        $this->_Del(
            \Phpcmf\Service::L('input')->get_post_ids(),
            null,
            function ($rows) {
                // 对应删除提醒
                foreach ($rows as $t) {
                    \Phpcmf\Service::M('member')->delete_admin_notice(MOD_DIR.'/'.$this->form['table'].'_verify/edit:cid/'.$t['cid'].'/id/'.$t['id'], SITE_ID);// clear
                    \Phpcmf\Service::L('cache')->clear('module_'.MOD_DIR.'_from_'.$this->form['table'].'_show_id_'.$t['id']);
                    // 统计数量
                    \Phpcmf\Service::M('mform', 'mform')->update_form_total($t['cid'], $this->form['table']);
                    $table = dr_mform_ctable(APP_DIR, $this->form['table'], $t['cid']);
                    if ($table != $this->init['table']) {
                        // 备用表
                        \Phpcmf\Service::M()->table($table)->delete($t['id']);
                    }
                }
            },
            \Phpcmf\Service::M()->dbprefix($this->init['table'])
        );
    }

    // ===========================

    /**
     * 获取内容
     * $id      内容id,新增为0
     * */
    protected function _Data($id = 0) {

        $row = $this->content_model->get_form_row($id, $this->form['table']);
        if (!$row) {
            return [];
        }

        return $row;
    }

    // 格式化保存数据 保存之前
    protected function _Format_Data($id, $data, $old) {

        if (!$this->cid) {
            $this->_json(0, dr_lang('所属主题cid参数不能为空'));
        }

        // 默认数据
        $data[0]['uid'] = (int)$data[1]['uid'];
        $data[1]['cid'] = $data[0]['cid'] = $this->cid;
        $data[1]['catid'] = $data[0]['catid'] = (int)$this->index['catid'];

        if (!$id) {
            // 发布时

            if ($this->uid) {
                /*
                // 判断日发布量
                $day_post = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'day_post', $this->member);
                if ($day_post && \Phpcmf\Service::M()->db
                        ->table($this->init['table'])
                        ->where('uid', $this->uid)
                        ->where('DATEDIFF(from_unixtime(inputtime),now())=0')
                        ->countAllResults() >= $day_post) {
                    $this->_json(0, dr_lang('每天发布数量不能超过%s个', $day_post));
                }

                // 判断发布总量
                $total_post = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'total_post', $this->member);
                if ($total_post && \Phpcmf\Service::M()->db
                        ->table($this->init['table'])
                        ->where('uid', $this->uid)
                        ->countAllResults() >= $total_post) {
                    $this->_json(0, dr_lang('发布数量不能超过%s个', $total_post));
                }
                */
            }

            // 审核状态
            $data[1]['status'] = $this->form['setting']['is_verify'] ? 1 : 0;

            // 默认数据
            $data[0]['uid'] = $data[1]['uid'] = (int)$this->member['uid'];
            //$data[1]['author'] = $this->member['username'] ? $this->member['username'] : 'guest';
            $data[1]['cid'] = $data[0]['cid'] =  $this->cid;
            $data[1]['catid'] = $data[0]['catid'] = (int)$this->index['catid'];
            $data[1]['inputip'] = \Phpcmf\Service::L('input')->ip_info();
            $data[1]['inputtime'] = SYS_TIME;
            $data[1]['tableid'] = $data[1]['displayorder'] = 0;
        } else {
			// 修改时
			 // 审核状态
            $data[1]['status'] = $this->form['setting']['is_verify'] ? 1 : 0;
        }

        return $data;
    }

    /**
     * 保存内容
     * $id      内容id,新增为0
     * $data    提交内容数组,留空为自动获取
     * $func    格式化提交的数据
     * */
    protected function _Save($id = 0, $data = [], $old = [], $func = null, $func2 = null) {

        return parent::_Save($id, $data, $old, null,
            function ($id, $data, $old) {
                // 保存之后
                $member = dr_member_info($data[1]['uid']);
                //审核通知
                if ($data[1]['status']) {
                    // 增减金币
                    /*
                    $score = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'score', $member);
                    $score && \Phpcmf\Service::M('member')->add_score($member['uid'], $score, dr_lang('%s[%s]: %s发布', MODULE_NAME, $this->index['title'], $this->form['name']), $this->index['curl']);
                    // 增减经验
                    $exp = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'exp', $member);
                    $exp && \Phpcmf\Service::M('member')->add_experience($member['uid'], $exp, dr_lang('%s[%s]: %s发布', MODULE_NAME, $this->index['title'], $this->form['name']), $this->index['curl']);
                    */
                    $row = dr_array2array($data[1], $data[0]);
                    $row['muid'] = $row['uid'];
                    $row['form'] = $this->form;
                    $row['index'] = $this->index;
                    $row['module'] = $this->module;
                    \Phpcmf\Service::L('Notice')->send_notice('module_form_post_1', $row);
                    $row['uid'] = $this->index['uid'];
                    \Phpcmf\Service::L('Notice')->send_notice('module_form_post_2', $row);
                } else {
                    \Phpcmf\Service::M('member')->admin_notice(SITE_ID, 'content', $member, dr_lang('%s[%s]: %s提交内容审核', MODULE_NAME, $this->index['title'], $this->form['name']), MOD_DIR.'/'.$this->form['table'].'_verify/edit:cid/'. $this->cid.'/id/'.$id);
                }
                $table = dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['id']);
                if ($table != $this->init['table']) {
                    // 备用表
                    \Phpcmf\Service::M()->table($table)->replace($data[1]);
                }

                //更新total字段
                \Phpcmf\Service::M('mform', 'mform')->update_form_total( $this->cid, $this->form['table']);
                $row = dr_array2array($data[1], $data[0]);
                $row['index'] = $this->index;
                if (!$old) {
                    // 挂钩点
                    \Phpcmf\Hooks::trigger('module_form_post_after', $row);
                } else {
                    \Phpcmf\Hooks::trigger('module_form_edit_after', $row, $old);
                }
            }
        );
    }

    /**
     * 回调处理结果
     * $data
     * */
    protected function _Call_Post($data) {

        $data['url'] = $this->form['setting']['rt_url'] ? str_replace(['{id}', '{cid}'], [$data[1]['id'],  $data[1]['cid']], $this->form['setting']['rt_url']) : '';
        if ($data[1]['status']) {
            return dr_return_data($data[1]['id'], dr_lang($this->form['setting']['rt_text'] ? $this->form['setting']['rt_text'] : '操作成功'), $data);
        } else {
            return dr_return_data($data[1]['id'], dr_lang($this->form['setting']['rt_text2'] ? $this->form['setting']['rt_text2'] : '操作成功，等待管理员审核'), $data);
        }
    }

}
