<?php namespace Phpcmf\Home;

/**
 * http://www.xunruicms.com
 * 本文件是框架系统文件，二次开发时不可以修改本文件，可以通过继承类方法来重写此文件
 **/

// 内容模块表单操作类 基于 Ftable
class Mform extends \Phpcmf\Table
{
    public $cid; // 内容id
    public $form; // 表单信息
    public $index; // 模块内容信息

    // 上级公共类
    public function __construct() {
        parent::__construct();
        $this->_Extend_Init();
    }

    // 继承类初始化
    protected function _Extend_Init() {
        // 初始化模块
        $this->_module_init(APP_DIR);

        // 判断表单是否操作
        $this->form = $this->module['form'][\Phpcmf\Service::L('Router')->class];
        if (!$this->form) {
            $this->_msg(0, dr_lang('模块表单【%s】不存在',\Phpcmf\Service::L('Router')->class));
        }

        // 支持附表存储
        $this->is_data = 1;
        // 模板前缀(避免混淆)
        $this->tpl_name = $this->form['table'];
        $this->tpl_prefix = 'mform_';
        // 预留cid
        $this->cid = intval(\Phpcmf\Service::L('input')->get('cid'));
        \Phpcmf\Service::V()->module(MOD_DIR);
    }

    // ========================

    // 内容列表
    protected function _Home_List() {

        // 无权限访问表单
        /*
        if (!\Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'show', $this->member)) {
            $this->_msg(0, dr_lang('您的用户组无权限访问表单'), $this->uid ? '' : dr_member_url('login/index'));
            return;
        }*/

        // 获取父级内容
        $this->index = $this->_Module_Row($this->cid);
        if (!$this->index) {
            $this->_msg(0, dr_lang('模块内容【id#%s】不存在',  $this->cid));
        }

        // 初始化数据表
        $this->_init([
            'table' => dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['cid']),
            'field' => $this->form['field'],
            'show_field' => 'title'
        ]);

        // 写入模板
        \Phpcmf\Service::V()->assign([
            'form_name' => $this->form['name'],
            'form_table' => $this->form['table'],
        ]);

        // seo
        \Phpcmf\Service::V()->assign([
            'meta_title' => dr_lang($this->form['name']).SITE_SEOJOIN.$this->index['title'],
            'meta_keywords' => $this->index['keywords'],
            'meta_description' => $this->index['description'],
        ]);

        \Phpcmf\Service::V()->assign([
            'index' => $this->index,
            'catid' => intval($this->index['catid']),
            'markid' => 'module-'.MOD_DIR.'-'.intval($this->index['catid']),
            'urlrule' =>\Phpcmf\Service::L('Router')->mform_list_url($this->form['table'], $this->index['id'], MOD_DIR, '[page]'),
        ]);
        \Phpcmf\Service::V()->display($this->_tpl_filename('list'));
    }

    // 添加内容
    protected function _Home_Post() {

        if ($this->form['setting']['is_close_post']) {
            $this->_msg(0, dr_lang('禁止前端提交表单'));
        }

        // 无权限访问表单
        /*
        if (!\Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'add', $this->member)) {
            $this->_msg(0, dr_lang('您的用户组无发布权限'), $this->uid ? '' : dr_member_url('login/index'));
            return;
        }*/

        // 判断会员权限
        //$this->member && $this->_member_option(0);

        // 是否有验证码
        $this->is_post_code = $this->form['setting']['is_post_code'] ? 0 : 1;

        // 获取父级内容
        $this->index = $this->_Module_Row($this->cid);
        if (!$this->index) {
            $this->_msg(0, dr_lang('所属主题【cid#%s】不存在',  $this->cid));
        };


        // 初始化数据表
        $this->_init([
            'table' => dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['cid']),
            'field' => $this->form['field'],
            'show_field' => 'title'
        ]);
        list($tpl) = $this->_Post(0);

        // seo
        \Phpcmf\Service::V()->assign([
            'meta_title' => dr_lang($this->form['name']).SITE_SEOJOIN.$this->index['title'],
            'meta_keywords' => $this->index['keywords'],
            'meta_description' => $this->index['description'],
        ]);

        \Phpcmf\Service::V()->assign([
            'form' => dr_form_hidden(),
            'index' => $this->index,
            'catid' => intval($this->index['catid']),
            'markid' => 'module-'.MOD_DIR.'-'.intval($this->index['catid']),
            'rt_url' => $this->form['setting']['rt_url'] ? '' : dr_now_url(),
            'is_post_code' => $this->is_post_code,
        ]);
        \Phpcmf\Service::V()->display($tpl);
    }

    // 显示内容
    protected function _Home_Show() {

        /*
        if (!\Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'show', $this->member)) {
            $this->_msg(0, dr_lang('您的用户组无权限访问表单'), $this->uid ? '' : dr_member_url('login/index'));
            return;
        }*/

        $id = intval(\Phpcmf\Service::L('input')->get('id'));
        $name = 'module_'.MOD_DIR.'_form_'.$this->form['table'].'_show_id_'.$id;
        $cache = \Phpcmf\Service::L('cache')->get_data($name);
        if (!$cache) {
            // 初始化数据表
            $this->_init([
                'table' => dr_mform_ctable(APP_DIR, $this->form['table'], 0),
                'field' => $this->form['field'],
                'show_field' => 'title'
            ]);
            list($tpl, $data) = $this->_Show($id);
            if (!$data) {
                $this->_msg(0, dr_lang('表单内容【id#%s】不存在', $id));
            }
            // 获取父级内容
            $this->cid = intval($data['cid']);
            $this->index = $this->_Module_Row($this->cid);
            if (!$this->index) {
                $this->_msg(0, dr_lang('模块内容【id#%s】不存在',  $this->cid));
            }
            // 模块的处理
            $data = $this->_Call_Show($data);
            $cache = [
                $tpl,
                $data,
                $this->cid,
                $this->index,
            ];
            // 缓存结果
            if ($data['uid'] != $this->uid && SYS_CACHE) {
                if ($this->member && $this->member['is_admin']) {
                    // 管理员时不进行缓存
                    \Phpcmf\Service::L('cache')->init()->delete($name);
                } else {
                    \Phpcmf\Service::L('cache')->set_data($name, $cache, SYS_CACHE_SHOW * 3600);
                }
            }
        } else {
            list($tpl, $data, $this->cid, $this->index) = $cache;
        }

        // 无权限访问表单
        if ($this->form['setting']['is_read'] && !in_array($this->uid, [$data['uid'], $this->index['uid']])) {
            $this->_msg(0, dr_lang('无权限访问表单'));
        }

        if ($data['status'] != 1) {
            $this->_msg(0, dr_lang('内容正在审核中'));
        }

        $data['cat'] = [];
        if (isset($data['catid']) && $data['catid']) {
            $data['cat'] = dr_cat_value($this->module['mid'], $data['catid']);
        }

        \Phpcmf\Service::V()->assign($data);

        $data['cat'] = [];
        $data['catname'] = $data['catpname'] = '';
        if (isset($data['catid']) && $data['catid']) {
            $data['cat'] = dr_cat_value($this->module['mid'], $data['catid']);
            $data['catname'] = $data['cat']['name'];
            $data['catpname'] = dr_get_cat_pname($this->module, $data['catid'], SITE_SEOJOIN);
        } else {

        }

        $data['formname'] = dr_lang($this->form['name']);
        $data['modulename'] = $data['modname'] = dr_lang($this->module['name']);

        // seo
        \Phpcmf\Service::V()->assign(\Phpcmf\Service::L('Seo')->get_seo_value($data, [
            'meta_title' => isset($this->form['setting']['seo']['title']) && $this->form['setting']['seo']['title'] ? $this->form['setting']['seo']['title'] : $data['title'].SITE_SEOJOIN.dr_lang($this->form['name']),
            'meta_keywords' => isset($this->form['setting']['seo']['keywords']) && $this->form['setting']['seo']['keywords'] ? $this->form['setting']['seo']['keywords'] : $data['title'].SITE_SEOJOIN.dr_lang($this->form['name']),
            'meta_description' => isset($this->form['setting']['seo']['description']) && $this->form['setting']['seo']['description'] ? $this->form['setting']['seo']['description'] : $data['title'].SITE_SEOJOIN.dr_lang($this->form['name']),
        ]));

        \Phpcmf\Service::V()->assign([
            'index' => $this->index,
            'catid' => intval($this->index['catid']),
            'markid' => 'module-'.MOD_DIR.'-'.intval($this->index['catid']),
            'urlrule' =>\Phpcmf\Service::L('Router')->mform_show_url($this->form['table'], $this->index['id'], MOD_DIR, '[page]'),
        ]);
        \Phpcmf\Service::V()->display($tpl);
    }

    // ===========================

    /**
     * 获取内容
     * $id      内容id,新增为0
     * */
    /*
    protected function _Data($id = 0) {

        if (!$id) {
            return [];
        }

        $name = 'module_'.MOD_DIR.'_formxx_'.$this->form['table'].'_show_id_'.$id;
        $data = \Phpcmf\Service::L('cache')->get_data($name);
        if (!$data) {
            // 处理缓存机制
            $data = $this->content_model->get_form_row($id, $this->form['table']);
            if (!$data) {
                return [];
            }
            if ($data['uid'] != $this->uid && SYS_CACHE) {
                if ($this->member && $this->member['is_admin']) {
                    // 管理员时不进行缓存
                    \Phpcmf\Service::L('cache')->init()->delete($name);
                } else {
                    \Phpcmf\Service::L('cache')->set_data($name, $data, SYS_CACHE_SHOW * 3600);
                }
            }
        }


        return $data;
    }*/

    // 格式化保存数据 保存之前
    protected function _Format_Data($id, $data, $old) {

        // 验证父数据
        if (!$this->index) {
            $this->_json(0, dr_lang('关联内容不存在'));
        }

        if ($this->uid) {

            // 判断日发布量
            /*
            $day_post = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'day_post', $this->member);
            if ($day_post && \Phpcmf\Service::M()->db
                    ->table($this->init['table'])
                    ->where('uid', $this->uid)
                    ->where('DATEDIFF(from_unixtime(inputtime),now())=0')
                    ->countAllResults() >= $day_post) {
                $this->_json(0, dr_lang('每天发布数量不能超过%s个', $day_post));
            }

            // 判断发布总量
            $total_post = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'total_post', $this->member);
            if ($total_post && \Phpcmf\Service::M()->db
                    ->table($this->init['table'])
                    ->where('uid', $this->uid)
                    ->countAllResults() >= $total_post) {
                $this->_json(0, dr_lang('发布数量不能超过%s个', $total_post));
            }*/
        }

        // 审核状态
        $data[1]['status'] = $this->form['setting']['is_verify'] ? 1 : 0;

        // 默认数据
        $data[0]['uid'] = $data[1]['uid'] = (int)$this->member['uid'];
        //$data[1]['author'] = $this->member['username'] ? $this->member['username'] : 'guest';
        $data[1]['cid'] = $data[0]['cid'] =  $this->cid;
        $data[1]['catid'] = $data[0]['catid'] = (int)$this->index['catid'];
        $data[1]['inputip'] = \Phpcmf\Service::L('input')->ip_info();
        $data[1]['inputtime'] = SYS_TIME;
        $data[1]['tableid'] = $data[1]['displayorder'] = 0;

        return $data;
    }

    /**
     * 保存内容
     * $id      内容id,新增为0
     * $data    提交内容数组,留空为自动获取
     * $func    格式化提交的数据
     * */
    protected function _Save($id = 0, $data = [], $old = [], $func = null, $func2 = null) {

        return parent::_Save($id, $data, $old, null,
            function ($id, $data, $old) {
                // 保存之后
                //审核通知
                if ($data[1]['status']) {
                    /*
                    // 增减金币
                    $score = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'score', $this->member);
                    $score && \Phpcmf\Service::M('member')->add_score($this->member['uid'], $score, dr_lang('%s[%s]: %s发布', MODULE_NAME, $this->index['title'], $this->form['name']), $this->index['curl']);
                    // 增减经验
                    $exp = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'exp', $this->member);
                    $exp && \Phpcmf\Service::M('member')->add_experience($this->member['uid'], $exp, dr_lang('%s[%s]: %s发布', MODULE_NAME, $this->index['title'], $this->form['name']), $this->index['curl']);
                    */
                    $row = dr_array2array($data[1], $data[0]);
                    $row['muid'] = $row['uid'];
                    $row['form'] = $this->form;
                    $row['index'] = $this->index;
                    $row['module'] = $this->module;
                    \Phpcmf\Service::L('Notice')->send_notice('module_form_post_1', $row);
                    $row['uid'] = $this->index['uid'];
                    \Phpcmf\Service::L('Notice')->send_notice('module_form_post_2', $row);
                } else {
                    \Phpcmf\Service::M('member')->admin_notice(SITE_ID, 'content', $this->member, dr_lang('%s[%s]: %s提交内容审核', MODULE_NAME, $this->index['title'], $this->form['name']), MOD_DIR.'/'.$this->form['table'].'_verify/edit:cid/'. $this->cid.'/id/'.$id);
                }
                $table = dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['id']);
                if ($table != $this->init['table']) {
                    // 备用表
                    \Phpcmf\Service::M()->table($table)->replace($data[1]);
                }
                //更新total字段
                \Phpcmf\Service::M('mform', 'mform')->update_form_total( $this->cid, $this->form['table']);
                // 挂钩点
                $row = dr_array2array($data[1], $data[0]);
                $row['index'] = $this->index;
                \Phpcmf\Hooks::trigger('module_form_post_after', $row);
            }
        );
    }

    // 操作主内容
    protected function _Module_Row($id) {

        $data = \Phpcmf\Service::L('cache')->get_data('module_'.MOD_DIR.'_show_id_'.$id);
        if ($data) {
            return $data;
        }

        $data = $this->content_model->get_data($id);
        if (!$data) {
            return [];
        }

        // 格式化输出自定义字段
        $cat = dr_cat_value($this->module['mid'], $data['catid']);
        $fields = $cat['field'] ? array_merge($this->module['field'], $cat['field']) : $this->module['field'];
        $fields['inputtime'] = ['fieldtype' => 'Date'];
        $fields['updatetime'] = ['fieldtype' => 'Date'];

        $data['url'] = dr_url_prefix($data['url'], MOD_DIR);
        return \Phpcmf\Service::L('Field')->app(MOD_DIR)->format_value($fields, $data);
    }

    /**
     * 回调处理结果
     * $data
     * */
    protected function _Call_Post($data) {

        $data['url'] = $this->form['setting']['rt_url'] ? str_replace(['{id}', '{cid}'], [$data[1]['id'],  $data[1]['cid']], $this->form['setting']['rt_url']) : '';
        if ($data[1]['status']) {
            return dr_return_data($data[1]['id'], dr_lang($this->form['setting']['rt_text'] ? $this->form['setting']['rt_text'] : '操作成功'), $data);
        } else {
            return dr_return_data($data[1]['id'], dr_lang($this->form['setting']['rt_text2'] ? $this->form['setting']['rt_text2'] : '操作成功，等待管理员审核'), $data);
        }
    }

    // 前端回调处理类
    protected function _Call_Show($data) {
        return $data;
    }
}
