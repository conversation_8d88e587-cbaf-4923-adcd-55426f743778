<?php namespace Phpcmf\Admin;

// 内容模块表单操作类 基于 Ftable
class Mform extends \Phpcmf\Table
{
    public $cid; // 内容id
    public $index; //
    public $form; // 表单信息
    public $is_verify; // 判断是否来自审核控制器
    protected $is_add_menu = 1; //允许有添加菜单

    // 上级公共类
    public function __construct() {
        parent::__construct();
        $this->_Extend_Init();
        $this->fix_admin_tpl_path = dr_get_app_dir('mform').'Views/';
    }

    // 继承类初始化
    protected function _Extend_Init() {
        // 初始化模块
        $this->_module_init(APP_DIR);
        // 判断是否来自审核控制器
        $this->is_verify = strpos(\Phpcmf\Service::L('Router')->class, '_verify') !== false;
        // 判断表单是否操作
        $this->form = $this->module['form'][str_replace('_verify', '',\Phpcmf\Service::L('Router')->class)];
        if (!$this->form) {
            $this->_admin_msg(0, dr_lang('模块表单【%s】不存在', str_replace('_verify', '',\Phpcmf\Service::L('Router')->class)));
        }
        // 支持附表存储
        $this->is_data = 1;
        // 模板前缀(避免混淆)
        $this->tpl_prefix = 'share_mform_';
        // 单独模板命名
        $this->tpl_name = $this->form['table'];
        // 模块显示名称
        $this->name = dr_lang('内容模块[%s]表单（%s）', APP_DIR, $this->form['name']);
        // 获取父级内容
        $this->url_params['cid'] = $this->cid = intval(\Phpcmf\Service::L('input')->get('cid'));
        $this->index = $this->cid ? $this->content_model->get_data( $this->cid) : [];
        // 自定义条件
        $where = $this->is_verify ? 'status<>1' : 'status=1';
        $this->cid && $where.= ' and cid='. $this->cid;
        $cwhere = $this->content_model->get_admin_list_where();
        $cwhere && $where.= ' AND '. $cwhere;
        $sysfield = ['inputtime', 'inputip', 'displayorder', 'uid', 'status'];
        if ($this->is_verify) {
            if (is_array($this->form['setting']['list_field'])) {
                $this->form['setting']['list_field']['status'] = [
                    'use' => '1', // 1是显示，0是不显示
                    'name' => dr_lang('状态'), //显示名称
                    'width' => '100', // 显示宽度
                    'func' => 'dr_mform_status_name', // 回调函数见：http://help.xunruicms.com/463.html
                    'center' => '1', // 1是居中，0是默认
                ];
            }
        }

        // 初始化数据表
        $this->_init([
            'field' => $this->form['field'],
            'table' => SITE_ID.'_'.APP_DIR.'_form_'.$this->form['table'],
            'sys_field' => $sysfield,
            'date_field' => $this->form['setting']['search_time'] ? $this->form['setting']['search_time'] : 'inputtime',
            'show_field' => 'title',
            'list_field' => $this->form['setting']['list_field'],
            'order_by' => $this->form['setting']['order'] ? dr_safe_replace($this->form['setting']['order']) : 'displayorder DESC,inputtime DESC',
            'where_list' => $where,
        ]);

        // 写入模板
        \Phpcmf\Service::V()->assign([
            'menu' => $this->_get_menu(),
            'mform' => $this->form,
            'index' => $this->index,
            'field' => $this->init['field'],
            'form_url' => \Phpcmf\Service::L('Router')->url(APP_DIR.'/'.$this->form['table'].'/index', ['cid' =>  $this->cid]),
            'is_verify' => $this->is_verify,
            'form_table' => $this->form['table'],
        ]);

        if ($this->module['setting']['is_hide_search_bar']) {
            $this->is_show_search_bar = 0;
        }
    }

    protected function _get_menu() {
        if ($this->is_verify) {
            if ($this->cid) {
                $menu = [
                    dr_lang('%s管理', $this->module['name']) => [MOD_DIR.'/home/<USER>',  dr_icon($this->module['setting']['icon'])],
                    dr_lang('%s管理', $this->form['name']) => [MOD_DIR.'/'.$this->form['table'].'/index{cid='.$this->cid.'}', dr_icon($this->form['setting']['icon'])],
                    dr_lang('%s审核', $this->form['name']) => [MOD_DIR.'/'.$this->form['table'].'_verify/index{cid='.$this->cid.'}', dr_icon($this->form['setting']['icon'])],
                ];
            } else {
                $menu = [
                    dr_lang('%s审核', $this->form['name']) => [MOD_DIR.'/'.$this->form['table'].'/index', 'fa fa-edit'],
                ];
            }
        } else {
            $menu = [
                dr_lang('%s管理', $this->module['name']) => [MOD_DIR.'/home/<USER>',  dr_icon($this->module['setting']['icon'])],
                dr_lang('%s管理', $this->form['name']) => [MOD_DIR.'/'.$this->form['table'].'/index{cid='.$this->cid.'}', dr_icon($this->form['setting']['icon'])],
                dr_lang('%s审核', $this->form['name']) => [MOD_DIR.'/'.$this->form['table'].'_verify/index{cid='.$this->cid.'}', dr_icon($this->form['setting']['icon'])],
            ];
            if ($this->cid && $this->is_add_menu) {
                $menu[dr_lang('添加')] = [APP_DIR.'/'.$this->form['table'].'/add{cid='.$this->cid.'}', 'fa fa-plus'];
                $menu[dr_lang('修改')] = ['hide:'.APP_DIR.'/'.$this->form['table'].'/edit', 'fa fa-edit'];
            }
        }
        return \Phpcmf\Service::M('auth')->_admin_menu($menu);
    }

    // ========================

    // 后台查看列表
    protected function _Admin_List() {

        $this->init['table'] = dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['id']);
        list($tpl) = $this->_List(['cid' => $this->cid]);

        if ($this->_is_admin_auth('edit')) {
            if ($this->is_verify) {
            } else {
                $this->mytable['link_var'].= 'html = html.replace(/\{cid\}/g, "'.$this->index['id'].'");';
                $this->mytable['link_var'].= 'html = html.replace(/\{mid\}/g, "'.APP_DIR.'");';
                $this->mytable['link_var'].= 'html = html.replace(/\{fid\}/g, "'.$this->form['table'].'");';
                $clink = $this->_app_clink('mform');
                if ($clink) {
                    foreach ($clink as $a) {
                        if ($a['model'] && $a['check']
                            && method_exists($a['model'], $a['check'])
                            && call_user_func(array($a['model'], $a['check']), APP_DIR, []) == 0) {
                            continue;
                        }
                        $this->mytable['link_tpl'].= ' <label><a class="btn '.$a['color'].' btn-xs" href="'.$a['url'].'"><i class="'.$a['icon'].'"></i> '.dr_lang($a['name']);
                        if ($a['field'] && \Phpcmf\Service::M()->is_field_exists($this->init['table'], $a['field'])) {
                            $this->mytable['link_tpl'].= '（{'.$a['field'].'}）';
                            $this->mytable['link_var'].= 'html = html.replace(/\{'.$a['field'].'\}/g, row.'.$a['field'].');';
                        }
                        $this->mytable['link_tpl'].= '</a></label>';
                    }
                }

                $cbottom = $this->_app_cbottom('mform');
                if ($cbottom) {
                    $this->mytable['foot_tpl'].= '<label>
                    <div class="btn-group dropup">
                        <a class="btn  blue btn-sm dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" data-close-others="true" aria-expanded="false" href="javascript:;"> '.dr_lang('批量').'
                            <i class="fa fa-angle-up"></i>
                        </a>
                        <ul class="dropdown-menu">';
                    foreach ($cbottom as $a) {
                        $this->mytable['foot_tpl'].= '<li>
                                <a href="'.urldecode($a['url']).'"> <i class="'.$a['icon'].'"></i> '.dr_lang($a['name']).' </a>
                            </li>';
                    }
                    $this->mytable['foot_tpl'].= '
                           
                        </ul>
                    </div>
                </label>';
                }
            }
        }

        \Phpcmf\Service::V()->assign([
            'mytable' => $this->mytable,
        ]);

        \Phpcmf\Service::V()->assign([
            'p' => ['cid' => $this->cid],
        ]);
        return \Phpcmf\Service::V()->display($tpl);
    }

    // 后台添加内容
    protected function _Admin_Add() {

        if (!$this->cid) {
            $this->_admin_msg(0, dr_lang('缺少cid参数'));
        }

        list($tpl) = $this->_Post(0);

        \Phpcmf\Service::V()->assign([
            'form' =>  dr_form_hidden(),
        ]);
        \Phpcmf\Service::V()->display($tpl);
    }

    // 后台修改内容
    protected function _Admin_Edit() {


        $id = intval(\Phpcmf\Service::L('input')->get('id'));
        list($tpl, $data) = $this->_Post($id);


        if (!$this->cid) {
            $this->_admin_msg(0, dr_lang('缺少cid参数'));
        }

        if (!$data) {
            $this->_admin_msg(0, dr_lang('数据不存在: '.$id));
        } elseif ($this->cid != $data['cid']) {
            $this->_admin_msg(0, dr_lang('cid不匹配'));
        } elseif ($this->is_verify && $data['status'] == 1) {
            $this->_admin_msg(0, dr_lang('已经通过了审核'));
        }

        \Phpcmf\Service::V()->assign([
            'form' => dr_form_hidden(),
        ]);
        \Phpcmf\Service::V()->display($tpl);
    }

    // 后台查看内容
    protected function _Admin_Show() {

        $id = intval(\Phpcmf\Service::L('input')->get('id'));
        list($tpl, $data) = $this->_Show($id);

        if (!$this->cid) {
            $this->_admin_msg(0, dr_lang('缺少cid参数'));
        }

        if (!$data) {
            $this->_admin_msg(0, dr_lang('数据不存在: '.$id));
        } elseif ($this->cid != $data['cid']) {
            $this->_admin_msg(0, dr_lang('cid不匹配'));
        } elseif ($this->is_verify && $data['status']) {
            $this->_admin_msg(0, dr_lang('已经通过了审核'));
        }

        \Phpcmf\Service::V()->assign([
            'form' =>  dr_form_hidden(),
        ]);
        \Phpcmf\Service::V()->display($tpl);
    }

    // 后台批量保存排序值
    protected function _Admin_Order() {
        $this->_Display_Order(
            intval(\Phpcmf\Service::L('input')->get('id')),
            intval(\Phpcmf\Service::L('input')->get('value')),
            function ($t) {
                $table = dr_mform_ctable(APP_DIR, $this->form['table'], $t['cid']);
                if ($table != $this->init['table']) {
                    // 备用表
                    $value = intval(\Phpcmf\Service::L('input')->get('value'));
                    \Phpcmf\Service::M()->table($table)->update($t['id'], ['displayorder' => $value]);
                }
            }
        );
    }

    // 后台删除内容
    protected function _Admin_Del() {
        $this->_Del(
            \Phpcmf\Service::L('input')->get_post_ids(),
            null,
            function ($rows) {
                // 对应删除提醒
                foreach ($rows as $t) {
                    \Phpcmf\Service::M('member')->delete_admin_notice(MOD_DIR.'/'.$this->form['table'].'_verify/edit:cid/'.$t['cid'].'/id/'.$t['id'], SITE_ID);// clear
                    \Phpcmf\Service::L('cache')->clear('module_'.MOD_DIR.'_from_'.$this->form['table'].'_show_id_'.$t['id']);
                    // 统计数量
                    \Phpcmf\Service::M('mform', 'mform')->update_form_total($t['cid'], $this->form['table']);
                    $table = dr_mform_ctable(APP_DIR, $this->form['table'], $t['cid']);
                    if ($table != $this->init['table']) {
                        // 备用表
                        \Phpcmf\Service::M()->table($table)->delete($t['id']);
                    }
                }
            },
            \Phpcmf\Service::M()->dbprefix($this->init['table'])
        );
    }

    // 后台批量审核
    protected function _Admin_Status() {

        $tid = intval(\Phpcmf\Service::L('input')->get('tid'));
        $ids = \Phpcmf\Service::L('input')->get_post_ids();
        if (!$ids) {
            $this->_json(0, dr_lang('所选数据不存在'));
        }

        // 格式化
        $in = [];
        foreach ($ids as $i) {
            $i && $in[] = intval($i);
        }
        if (!$in) {
            $this->_json(0, dr_lang('所选数据不存在'));
        }

        $rows = \Phpcmf\Service::M()->db->table($this->init['table'])->whereIn('id', $in)->get()->getResultArray();
        if (!$rows) {
            $this->_json(0, dr_lang('所选数据不存在'));
        }

        foreach ($rows as $row) {
            if ($row['status'] != 1) {
                if ($tid) {
                    // 拒绝
                    $this->_verify_refuse($row);
                } else {
                    // 通过
                    $this->_verify($row);
                }
                \Phpcmf\Service::M('mform', 'mform')->update_form_total($row['cid'], $this->form['table']);
            }

        }

        $this->_json(1, dr_lang('操作成功'));
    }

    // ===========================

    /**
     * 获取内容
     * $id      内容id,新增为0
     * */
    protected function _Data($id = 0) {

        $row = $this->content_model->get_form_row($id, $this->form['table']);
        if (!$row) {
            return [];
        }

        $this->cid = $row['cid'];
        if (!$this->index) {
            $this->index = $this->content_model->get_data($row['cid']);
            \Phpcmf\Service::V()->assign([
                'menu' => $this->_get_menu(),
                'index' => $this->index,
            ]);
        }


        return $row;
    }

    // 格式化保存数据 保存之前
    protected function _Format_Data($id, $data, $old) {

        // 验证父数据
        if (!$this->index) {
            $this->_json(0, dr_lang('关联内容不存在'));
        }

        // 默认数据
        $data[1]['uid'] = intval($data[1]['uid']);
        $data[0]['uid'] = (int)$data[1]['uid'];
        $data[1]['cid'] = $data[0]['cid'] =  $this->cid;
        $data[1]['catid'] = $data[0]['catid'] = (int)$this->index['catid'];

        // 后台添加时默认通过
        if (!$id) {
            // !$this->is_verify &&
            $data[1]['status'] = 1;
            $data[1]['tableid'] = 0;
        }

        return $data;
    }

    /**
     * 保存内容
     * $id      内容id,新增为0
     * $data    提交内容数组,留空为自动获取
     * $func    格式化提交的数据
     * */
    protected function _Save($id = 0, $data = [], $old = [], $func = null, $func2 = null) {

        return parent::_Save($id, $data, $old, null,
            function ($id, $data, $old) {
                if ($this->is_verify) {
                    if ($data[1]['status'] == 1) {
                        // 审核通过时
                        $data[1]['status'] = 0;
                        $this->_verify($data[1]);
                    } elseif ($data[1]['status'] == 2) {
                        $data[1]['status'] = 0;
                        $this->_verify_refuse($data[1]);
                    }
                }
                // 保存之后的更新total字段
                \Phpcmf\Service::M('mform', 'mform')->update_form_total( $this->cid, $this->form['table']);
                \Phpcmf\Service::M('member')->todo_admin_notice(MOD_DIR.'/'.$this->form['table'].'_verify/edit:cid/'.$old['cid'].'/id/'.$old['id'], SITE_ID);
                // clear
                \Phpcmf\Service::L('cache')->clear('module_'.MOD_DIR.'_from_'.$this->form['table'].'_show_id_'.$id);

                $table = dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['id']);
                if ($table != $this->init['table']) {
                    // 备用表
                    \Phpcmf\Service::M()->table($table)->replace($data[1]);
                }

                $row = dr_array2array($data[1], $data[0]);
                $row['index'] = $this->index;
                if (!$old) {
                    // 挂钩点
                    \Phpcmf\Hooks::trigger('module_form_post_after', $row);
                } else {
                    \Phpcmf\Hooks::trigger('module_form_edit_after', $row, $old);
                }
            }
        );
    }

    // 审核拒绝
    protected function _verify_refuse($row) {

        if ($row['status'] == 2) {
            return;
        }

        $row['form'] = $this->form;
        $row['index'] = $this->index;
        $row['module'] = $this->module;
        \Phpcmf\Service::M()->db->table($this->init['table'])->where('id', $row['id'])->update(['status' => 2]);

        $table = dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['id']);
        if ($table != $this->init['table']) {
            // 备用表
            $value = intval(\Phpcmf\Service::L('input')->get('value'));
            \Phpcmf\Service::M()->table($table)->update($row['id'], ['status' => 2]);
        }

        \Phpcmf\Service::M('member')->todo_admin_notice(MOD_DIR.'/'.$this->form['table'].'_verify/edit:cid/'.$row['cid'].'/id/'.$row['id'], SITE_ID);
        \Phpcmf\Service::L('Notice')->send_notice('module_form_verify_0', $row);
    }

    // 审核通过
    protected function _verify($row) {

        if ($row['status'] == 1) {
            return;
        }

        /*
        // 增减金币
        $score = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'score', $this->member);
        $score && \Phpcmf\Service::M('member')->add_score($row['uid'], $score, dr_lang('%s: %s发布', MODULE_NAME, $this->form['name']), $row['curl']);

        // 增减经验
        $exp = \Phpcmf\Service::M('member_auth')->mform_auth(MOD_DIR, $this->form['id'], 'exp', $this->member);
        $exp && \Phpcmf\Service::M('member')->add_experience($row['uid'], $exp, dr_lang('%s: %s发布', MODULE_NAME, $this->form['name']), $row['curl']);
*/
        \Phpcmf\Service::M('member')->todo_admin_notice(MOD_DIR.'/'.$this->form['table'].'_verify/edit:cid/'.$row['cid'].'/id/'.$row['id'], SITE_ID);

        \Phpcmf\Service::M()->db->table($this->init['table'])->where('id', $row['id'])->update(['status' => 1]);

        $table = dr_mform_ctable(APP_DIR, $this->form['table'], $this->index['id']);
        if ($table != $this->init['table']) {
            // 备用表
            $value = intval(\Phpcmf\Service::L('input')->get('value'));
            \Phpcmf\Service::M()->table($table)->update($row['id'], ['status' => 1]);
        }

        if (!$this->index) {
            $this->index = $this->content_model->get_data($row['cid']);
        }


        $row['form'] = $this->form;
        $row['index'] = $this->index;
        $row['module'] = $this->module;
        \Phpcmf\Service::L('Notice')->send_notice('module_form_verify_1', $row);

        $row['muid'] = $row['uid'];
        $row['uid'] = $this->index['uid'];
        \Phpcmf\Service::L('Notice')->send_notice('module_form_verify_2', $row);
    }
}
