{template "header.html"}

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="portlet bordered light myfbody">
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="{if $page==0}active{/if}">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-cog"></i> {dr_lang('基本设置')} </a>
                </li>
                {if $diy_tpl}
                <li class=" {if $page==3}active{/if}">
                    <a href="#tab_3" data-toggle="tab" onclick="$('#dr_page').val('3')"> <i class="fa fa-cog"></i> {dr_lang('自定义设置')} </a>
                </li>
                {/if}
                <li class="{if $page==1}active{/if}">
                    <a href="#tab_1" data-toggle="tab" onclick="$('#dr_page').val('1')"> <i class="fa fa-table"></i> {dr_lang('后台显示设置')} </a>
                </li>
                <li class="{if $page==2}active{/if}">
                    <a href="#tab_2" data-toggle="tab" onclick="$('#dr_page').val('2')"> <i class="fa fa-user"></i> {dr_lang('用户中心')} </a>
                </li>
                <li class="{if $page==5}active{/if}">
                    <a href="#tab_5" data-toggle="tab" onclick="$('#dr_page').val('5')"> <i class="fa fa-internet-explorer"></i> {dr_lang('前端SEO设置')} </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
            <div class="tab-content">


                <div class="tab-pane {if $page==5}active{/if}" id="tab_5">
                    <div class="form-body">

                        <?php !$data['setting']['seo']['title'] && $data['setting']['seo']['title'] = '{title}{join}{formname}{join}{'.'SITE_NAME}';?>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('内容SEO标题')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="data[setting][seo][title]">{$data['setting']['seo']['title']}</textarea>
                                <span class="help-block">
                                    <button class="btn btn-xs green" onclick="dr_seo_title_rule()" type="button"><i class="fa fa-code"></i> {dr_lang('可用通配符标签')}</button>
                                    <script>
                                        function dr_seo_title_rule() {
                                            layer.alert('通用标签<br>'+
                                                '{join}	SEO连接符号，默认“_”<br>'+
                                                '[{page}]	分页页码<br>'+
                                                '{modulename} 模块名称<br>'+
                                                '{formname}	表单名称<br>'+
                                                '{catname}	当前栏目名称<br>'+
                                                '{catpname}	当前栏目带层次的栏目名称<br>'+
                                                '支持“网站表单表”任何字段，格式：{字段名}，<br>如：{title}表示标题<br>'+
                                                '支持网站系统常量，格式：{大写的常量名称}，<br>如：{SITE_NAME}表示网站名称<br>'+
                                                ''+
                                                '', {
                                                shade: 0,
                                                title: '',
                                                btn: []
                                            });
                                        }
                                    </script>
                                </span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('内容SEO关键字')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="data[setting][seo][keywords]">{$data['setting']['seo']['keywords']}</textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('内容SEO描述信息')}</label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="data[setting][seo][description]">{$data['setting']['seo']['description']}</textarea>
                            </div>
                        </div>


                    </div>
                </div>

                {if $diy_tpl}
                <div class="tab-pane {if $page==3}active{/if}" id="tab_3">
                    <div class="form-body">
                        {load $diy_tpl}
                    </div>
                </div>
                {/if}

                <div class="tab-pane {if $page==0}active{/if}" id="tab_0">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('表单别名')}</label>
                            <div class="col-md-9">
                                <div class="form-control-static"><label><span class="label label-success"> {$data.table} </span></label></div>
                            </div>
                        </div>
                        <div class="form-group" id="dr_row_name">
                            <label class="col-md-2 control-label ">{dr_lang('表单名称')}</label>
                            <div class="col-md-9">
                                <label><input type="text" class="form-control" id="dr_name" name="data[name]" value="{htmlspecialchars((string)$data.name)}"></label>
                                <span class="help-block"> {dr_lang('表单的描述名称')} </span>
                            </div>
                        </div>
                        <div class="form-group" id="dr_row_icon">
                            <label class="col-md-2 control-label ">{dr_lang('菜单图标')}</label>
                            <div class="col-md-9">
                                <div class="input-group" style="width:250px">
                                    <input class="form-control" id="dr_icon" type="text" name="data[setting][icon]" value="{htmlspecialchars((string)$data['setting']['icon'])}" />
                            <span class="input-group-btn">
                                <a class="btn btn-success" href="{dr_url('api/icon')}" target="_blank"><i class="fa fa-arrow-right fa-fw" /></i> {dr_lang('查看')}</a>
                            </span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('前端阅读权限')}</label>
                            <div class="col-md-9">

                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-checkbox-outline"><input type="radio" name="data[setting][is_read]" value="0" {if !$data['setting']['is_read']}checked{/if} /> {dr_lang('全部开放')} <span></span></label>
                                    <label class="mt-radio mt-checkbox-outline"><input type="radio" name="data[setting][is_read]" value="1" {if $data['setting']['is_read']}checked{/if} /> {dr_lang('仅自己和主题归属者')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('开启表示仅自己和主题归属者才能阅读内容')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('前端发布功能')}</label>
                            <div class="col-md-9">

                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-checkbox-outline"><input type="radio" name="data[setting][is_close_post]" value="0" {if !$data['setting']['is_close_post']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-checkbox-outline"><input type="radio" name="data[setting][is_close_post]" value="1" {if $data['setting']['is_close_post']}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('前端用户（不是用户中心）是否开启发布功能；关闭后，仅前端用户的发布权限将会无效')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('前端发布验证码')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-checkbox-outline"><input type="radio" name="data[setting][is_post_code]" value="0" {if !$data['setting']['is_post_code']}checked{/if} /> {dr_lang('开启')} <span></span></label>
                                    <label class="mt-radio mt-checkbox-outline"><input type="radio" name="data[setting][is_post_code]" value="1" {if $data['setting']['is_post_code']}checked{/if} /> {dr_lang('关闭')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('前端发布内容时的图片验证码开关')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('前端发布是否审核')}</label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-checkbox-outline"><input type="radio" name="data[setting][is_verify]" value="0" {if !$data['setting']['is_verify']}checked{/if} /> {dr_lang('审核')} <span></span></label>
                                    <label class="mt-radio mt-checkbox-outline"><input type="radio" name="data[setting][is_verify]" value="1" {if $data['setting']['is_verify']}checked{/if} /> {dr_lang('不审核')} <span></span></label>
                                </div>
                                <span class="help-block">{dr_lang('开启审核将进入审核管理中')}</span>
                            </div>
                        </div>

                        <div class="form-group ">
                            <label class="col-md-2 control-label">{dr_lang('提交成功提示文章')}</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control input-xlarge" name="data[setting][rt_text]" value="{htmlspecialchars((string)$data['setting']['rt_text'])}" >
                                <span class="help-block"> {dr_lang('当用户提交表单成功之后显示的文字，默认为：操作成功')} </span>
                            </div>
                        </div>

                        <div class="form-group ">
                            <label class="col-md-2 control-label">{dr_lang('提交审核提示文章')}</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control input-xlarge" name="data[setting][rt_text2]" value="{htmlspecialchars((string)$data['setting']['rt_text2'])}" >
                                <span class="help-block"> {dr_lang('当用户提交表单审核时显示的文字，默认为：操作成功，等待管理员审核')} </span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('提交成功跳转URL')}</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control input-xlarge" name="data[setting][rt_url]" value="{htmlspecialchars((string)$data['setting']['rt_url'])}" >
                                <span class="help-block"> {dr_lang('当用户提交表单成功之后跳转的链接，{cid}表示当前表单的归属内容的id号，{id}表示当前表单的id号')} </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane {if $page==1}active{/if}" id="tab_1">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('列表默认排序')}</label>
                            <div class="col-md-9">
                                <label><input class="form-control input-xlarge" type="text" name="data[setting][order]" value="{if $data['setting']['order']}{htmlspecialchars((string)$data['setting']['order'])}{else}inputtime DESC{/if}" ></label>
                                <span class="help-block">{dr_lang('排序格式符号MySQL的语法，例如：主表字段 desc')}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('列表时间搜索')}</label>
                            <div class="col-md-9">
                                <label><input class="form-control" type="text" name="data[setting][search_time]" value="{if $data['setting']['search_time']}{htmlspecialchars((string)$data['setting']['search_time'])}{else}inputtime{/if}" ></label>
                                <span class="help-block">{dr_lang('设置后台时间范围搜索字段，默认为发布时间字段：inputtime')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('列表显示字段')}</label>
                            <div class="col-md-9">
                                <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                                    <thead>
                                    <tr class="heading">
                                        <th class="myselect">
                                            {dr_lang('显示')}
                                        </th>
                                        <th width="180"> {dr_lang('字段')} </th>
                                        <th width="100"> {dr_lang('类别')} </th>
                                        <th width="150"> {dr_lang('名称')} </th>
                                        <th width="100"> {dr_lang('宽度')} </th>
                                        <th width="120"> {dr_lang('对其方式')} </th>
                                        <th> {dr_lang('回调方法')} </th>
                                    </tr>
                                    </thead>
                                    <tbody class="field-sort-items">
                                    {loop $field $n $t}
                                    <tr class="odd gradeX">
                                        <td class="myselect">
                                            <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                                                <input type="checkbox" class="checkboxes" name="data[setting][list_field][{$t.fieldname}][use]" value="1" {if $data['setting']['list_field'][$t.fieldname]['use']} checked{/if} />
                                                <span></span>
                                            </label>
                                        </td>
                                        <td>{dr_lang($t.name)} ({$t.fieldname})</td>
                                        <td>{$t.fieldtype}</td>
                                        <td><input class="form-control" type="text" name="data[setting][list_field][{$t.fieldname}][name]" value="{php echo $data['setting']['list_field'][$t.fieldname]['name'] ? htmlspecialchars($data['setting']['list_field'][$t.fieldname]['name']) : $t.name}" /></td>
                                        <td> <input class="form-control" type="text" name="data[setting][list_field][{$t.fieldname}][width]" value="{htmlspecialchars((string)$data['setting']['list_field'][$t.fieldname]['width'])}" /></td>
                                        <td><input type="checkbox" name="data[setting][list_field][{$t.fieldname}][center]" {if $data['setting']['list_field'][$t.fieldname]['center']} checked{/if} value="1"  data-on-text="{dr_lang('居中')}" data-off-text="{dr_lang('默认')}" data-on-color="success" data-off-color="danger" class="make-switch" data-size="small">
                                        </td>
                                        <td> <div class="input-group" style="width:250px">
                                                <span class="input-group-btn">
                                                    <a class="btn btn-success" href="javascript:dr_call_alert();">{dr_lang('回调')}</a>
                                                </span>
                                            <input class="form-control" type="text" name="data[setting][list_field][{$t.fieldname}][func]" value="{htmlspecialchars((string)$data['setting']['list_field'][$t.fieldname]['func'])}" />
                                        </div></td>
                                    </tr>
                                    {/loop}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane {if $page==2}active{/if}" id="tab_2">
                    <div class="form-body">

                        {if !IS_USE_MEMBER}
                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('无法使用')}</label>
                            <div class="col-md-9">
                                <div class="form-control-static"><label><a class="label label-danger"> {dr_lang('本功能需要安装【用户系统】插件')} </a></label></div>
                            </div>
                        </div>
                        {else}

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('用户管理内容')}</label>
                            <div class="col-md-9">
                                <input type="checkbox" name="data[setting][is_member]" value="1" {if $data['setting']['is_member']}checked{/if} data-on-text="{dr_lang('已开启')}" data-off-text="{dr_lang('已关闭')}" data-on-color="success" data-off-color="danger" class="make-switch" data-size="small">
                                <span class="help-block">{dr_lang('用户中心的内容管理右侧的入口链接显示、模块表单在用户中心可以管理表单数据')}</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label">{dr_lang('是否可管理别人提交的')}</label>
                            <div class="col-md-9">
                                <input type="checkbox" name="data[setting][is_member_user]" value="1" {if $data['setting']['is_member_user']}checked{/if} data-on-text="{dr_lang('已开启')}" data-off-text="{dr_lang('已关闭')}" data-on-color="success" data-off-color="danger" class="make-switch" data-size="small">
                                <span class="help-block">{dr_lang('关闭情况下只能看到和管理自己提交的表数据，开启时可以管理别人提交的表单数据')}</span>
                            </div>
                        </div>
                        {/if}


                    </div>
                </div>


            </div>
        </div>
    </div>

    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <button type="button" onclick="dr_ajax_submit('{dr_now_url()}&page='+$('#dr_page').val(), 'myform', '2000')" class="btn green"> <i class="fa fa-save"></i> {dr_lang('保存')}</button>
        </div>
    </div>
</form>
<script type="text/javascript">
$(function () {
        $(".field-sort-items").sortable();
    });
</script>
{template "footer.html"}