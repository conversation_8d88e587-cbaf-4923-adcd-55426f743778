{template "header.html"}

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    {$form}
    <div class="row myfbody">
        <div class="col-md-12">

            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <span class="caption-subject font-green sbold ">{dr_lang('广告内容')}</span>
                    </div>

                    <div class="actions">
                        <div class="btn-group">
                            <a class="btn" href="{$reply_url}"> <i class="fa fa-mail-reply"></i> {dr_lang('返回列表')}</a>
                        </div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="form-body">

                        <div class="form-group" id="dr_row_adtype">
                            <label class="control-label col-md-2"><span class="required" aria-required="true"> * </span>广告类型</label>
                            <div class="col-md-10">
                                <div class="mt-radio-inline">
                                    {loop $adstyledata $i $t}
                                    <label class="mt-radio mt-radio-outline">
                                        <input type="radio" name="data[adstyle]" value="{$i}" {if $adstyle == $i}checked{/if} onclick="show_field(this.value)"> {$t} <span></span>
                                    </label>
                                    {/loop}
                                </div>
                            </div>
                        </div>

                        <div id="dr_option">

                        </div>

                        {$myfield}
                        {$diyfield}

                    </div>
                </div>
            </div>

        </div>

    </div>

    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">

            <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}', 'myform', '2000')" class="btn blue"> <i class="fa fa-save"></i> {dr_lang('保存内容')}</button></label>
            <label><button type="button" onclick="dr_ajax_submit('{dr_now_url()}', 'myform', '2000', '{$reply_url}')" class="btn yellow"> <i class="fa fa-mail-reply-all"></i> {dr_lang('保存并返回')}</button></label>

        </div>
    </div>
</form>

<script type="text/javascript">
    $(function() {
        show_field('{$adstyle}');
    });

    function show_field(style) {
        var adstyle = '{$adstyle}';
        var st = 1;
        if (style != adstyle) {
            st = 0;
        }
        $.ajax({type: "POST",dataType:"html", url: "{dr_url($uriprefix.'/getfield')}",
            data: {adstyle:style, id:'{$id}', status:st},
            success: function(data) {
                $('#dr_option').html(data);
                App.init();
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                dr_ajax_alert_error(HttpRequest, this, thrownError);
            }
        });
    }
</script>

{template "footer.html"}