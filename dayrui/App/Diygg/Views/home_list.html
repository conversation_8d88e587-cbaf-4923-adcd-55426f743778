{template "header.html"}

<div class="right-card-box">
    {template "api_list_date_search.html"}

    <div class="row table-search-tool">
        <form action="{SELF}" method="get">
            {dr_form_search_hidden()}
            <div class="col-md-12 col-sm-12">
                <label>
                    <select name="field" class="form-control">
                        <option value="id"> 编号 </option>
                        {loop $field $t}
                        {if $t.issearch}
                        <option value="{$t.fieldname}" {if $param.field==$t.fieldname}selected{/if}>{$t.name}</option>
                        {/if}
                        {/loop}
                    </select>
                </label>
                <label><i class="fa fa-caret-right"></i></label>
                <label><input type="text" class="form-control" placeholder="" value="{$param['keyword']}" name="keyword" /></label>
            </div>

            <div class="col-md-12 col-sm-12">
                <label><button type="submit" class="btn blue btn-sm onloading" name="submit" > <i class="fa fa-search"></i> {dr_lang('搜索')}</button></label>
            </div>
        </form>
    </div>

    <form class="form-horizontal" role="form" id="myform">
        {dr_form_hidden()}
        <div class="table-scrollable">
            <table class="table table-striped table-bordered table-hover table-checkable dataTable">
                <thead>
                <tr class="heading">
                    <th class="myselect">
                        <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                            <input type="checkbox" class="group-checkable" data-set=".checkboxes" />
                            <span></span>
                        </label>
                    </th>
                    <th width="60" style="text-align:center"> {dr_lang('编号')} </th>
                    <th width="100" style="text-align:center"> {dr_lang('分类')} </th>
                    <th width="100" style="text-align:center"> {dr_lang('广告类型')} </th>
                    <th width="220" style="text-align:center"> {dr_lang('广告位名称')} </th>
                    <th width="80" style="text-align:center"> {dr_lang('广告内容')} </th>
                    <th width="150" style="text-align:center"> {dr_lang('时间限制')} </th>
                    <th width="160" style="text-align:center"> {dr_lang('开始时间')} </th>
                    <th width="160" style="text-align:center"> {dr_lang('结束时间')} </th>
                    <th width="" style="text-align:center"> {dr_lang('过期占位图')} </th>
                    <th width="" style="text-align:center"> {dr_lang('备注')} </th>
                    <th width="160">{dr_lang('操作')}</th>
                </tr>
                </thead>
                <tbody>
                {loop $list $t}
                <?php
                $odata = \Phpcmf\Service::C()->get_attachment($t.overpic);
                $overpicurl = "javascript:dr_preview_image('".dr_get_file_url($odata)."');";
                ?>
                <tr class="odd gradeX" id="dr_row_{$t.id}">
                    <td class="myselect">
                        <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                            <input type="checkbox" class="checkboxes" name="ids[]" value="{$t.id}" />
                            <span></span>
                        </label>
                    </td>
                    <td style="text-align:center">{$t.id}</td>
                    <td style="text-align:center"><?php echo dr_jms_typeid2name($t.typeid); ?></td>
                    <td style="text-align:center"><?php echo dr_jms_adstyle2name($t.adstyle); ?></td>
                    <td style="text-align:center">{$t.adname}</td>
                    <td style="text-align:center"><a href="javascript:dr_iframe_show('{dr_lang('广告内容')}', '{dr_url($uriprefix.'/adshow', ['id'=>$t.id])}', '80%', '75%');" class="btn btn-xs green"> <i class="fa fa-plus"></i> {dr_lang('查看')}</a></td>
                    <td style="text-align:center"><?php echo dr_jms_timeset2name($t.timeset); ?></td>
                    <td style="text-align:center">{Function_list::datetime($t.starttime)}</td>
                    <td style="text-align:center">{Function_list::datetime($t.endtime)}</td>
                    <td style="text-align:center"><a href="{$overpicurl}">{Function_list::title($odata.filename, $param)}</a></td>
                    <td style="text-align:center">{$t.remark}</td>
                    <td>
                        <label><a href="{dr_url($uriprefix.'/edit', ['id'=>$t.id])}" class="btn btn-xs red"> <i class="fa fa-edit"></i> {dr_lang('修改')}</a></label>
                        <label><a href="javascript:dr_iframe_show('{dr_lang('广告调用标签')}', '{dr_url($uriprefix.'/callcode', ['id'=>$t.id])}', '50%', '40%');" class="btn btn-xs blue"> <i class="fa fa-code"></i> {dr_lang('调用标签')}</a></label>
                    </td>
                </tr>
                {/loop}
                </tbody>
            </table>
        </div>

        <div class="row fc-list-footer table-checkable ">
            <div class="col-md-5 fc-list-select">
                <label class="mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline">
                    <input type="checkbox" class="group-checkable" data-set=".checkboxes" />
                    <span></span>
                </label>
                <button type="button" onclick="dr_ajax_option('{dr_url($uriprefix.'/del')}', '{dr_lang('你确定要删除它们吗？')}', 1)" class="btn red btn-sm"> <i class="fa fa-trash"></i> {dr_lang('删除')}</button>
            </div>

            <div class="col-md-7 fc-list-page">
                {$mypages}
            </div>
        </div>
    </form>

</div>
<script src="{dr_url('tools/home/<USER>')}"></script>
{template "footer.html"}

