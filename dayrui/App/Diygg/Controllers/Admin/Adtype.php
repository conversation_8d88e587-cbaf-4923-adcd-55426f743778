<?php namespace Phpcmf\Controllers\Admin;

// 广告分类
class Adtype extends \Phpcmf\Table
{

    public function __construct()
    {
        parent::__construct();
        // 表单显示名称
        $this->name = dr_lang('广告分类');
        // 模板前缀(避免混淆)
        $this->tpl_prefix = 'adtype_';

        // 用于表储存的字段，后台可修改的表字段，设置字段类别参考：http://help.xunruicms.com/1138.html
        $field = array (
            'typename' =>
                array (
                    'name' => '分类名称',
                    'fieldname' => 'typename',
                    'ismain' => '1',
                    'fieldtype' => 'Text',
                    'setting' =>
                        array (
                            'option' =>
                                array (
                                    'fieldtype' => '',
                                    'fieldlength' => '',
                                    'value' => '',
                                    'width' => '400',
                                    'css' => '',
                                ),
                            'validate' =>
                                array (
                                    'required' => '1',
                                    'pattern' => '',
                                    'errortips' => '',
                                    'check' => '',
                                    'filter' => '',
                                    'formattr' => '',
                                    'tips' => '',
                                ),
                        ),
                ),
        );

        // 用于列表显示的字段
        $list_field = array (
            'id' =>
                array (
                    'use' => '1',
                    'name' => 'ID',
                    'width' => '',
                    'func' => '',
                    'center' => '0',
                ),
            'typename' =>
                array (
                    'use' => '1',
                    'name' => '分类名称',
                    'width' => '',
                    'func' => '',
                    'center' => '0',
                ),
        );

        // 初始化数据表
        $this->_init([
            'table' => 'diygg_type',  // （不带前缀的）表名字
            'field' => $field, // 可查询的字段
            'list_field' => $list_field,
            'order_by' => 'id desc', // 列表排序，默认的排序方式
            'date_field' => '', // 按时间段搜索字段，没有时间字段留空
        ]);

        // 把公共变量传入模板
        \Phpcmf\Service::V()->assign([
            // 搜索字段
            'field' => $field,
            'is_time_where' => $this->init['date_field'],
            // 后台的菜单
            'menu' => \Phpcmf\Service::M('auth')->_admin_menu(
                [
                    $this->name => [APP_DIR.'/'.\Phpcmf\Service::L('Router')->class.'/index', 'fa fa-reorder'],
                    '添加分类' => [APP_DIR.'/'.\Phpcmf\Service::L('Router')->class.'/add', 'fa fa-plus'],
//                    '修改' => ['hide:'.APP_DIR.'/'.\Phpcmf\Service::L('Router')->class.'/edit', 'fa fa-edit'],
                    '广告管理' => [APP_DIR.'/home/<USER>', 'fa fa-wrench'],
                    '添加广告' => [APP_DIR.'/home/<USER>', 'fa fa-plus'],
                ])
        ]);
    }

    // 查看列表
    public function index() {
        list($tpl) = $this->_List();
        \Phpcmf\Service::V()->display($tpl);
    }

    // 添加内容
    public function add() {
        list($tpl) = $this->_Post(0);
        \Phpcmf\Service::V()->display($tpl);
    }

    // 修改内容
    public function edit() {
        list($tpl) = $this->_Post(intval(\Phpcmf\Service::L('input')->get('id')));
        \Phpcmf\Service::V()->display($tpl);
    }

    // 删除内容
    public function del() {
        $this->_Del(
            \Phpcmf\Service::L('Input')->get_post_ids(),
            function($rows) {
                // 删除前的验证
                return dr_return_data(1, 'ok', $rows);
            },
            function($rows) {
                // 删除后的处理
                return dr_return_data(1, 'ok');
            },
            \Phpcmf\Service::M()->dbprefix($this->init['table'])
        );
    }

    /**
     * 获取内容
     * $id      内容id,新增为0
     * */
    protected function _Data($id = 0) {
        $row = parent::_Data($id);
        // 这里可以对内容进行格式化显示操处理
        return $row;
    }

    // 格式化保存数据
    protected function _Format_Data($id, $data, $old) {
        if (!$id) {
            // 当提交新数据时，把当前时间插入进去
            //$data[1]['inputtime'] = SYS_TIME;
        }
        return $data;
    }


    // 保存内容
    protected function _Save($id = 0, $data = [], $old = [], $func = null, $func2 = null) {
        return parent::_Save($id, $data, $old, function($id, $data, $old){
            // 验证数据
            if (!$data[1]['typename']) {
                return dr_return_data(0, '分类名称不能为空！', ['field' => 'typename']);
            }
            // 保存之前执行的函数，并返回新的数据
            if (!$id) {
                // 当提交新数据时，把当前时间插入进去
                //$data[1]['inputtime'] = SYS_TIME;
            }

            return dr_return_data(1, null, $data);
        }, function ($id, $data, $old) {
            // 保存之后执行的动作
        });
    }

}
