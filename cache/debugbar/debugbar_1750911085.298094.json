{"url": "https://www.kuhao123.com/index.php/webnav/ai-audio-tools/6.html", "method": "GET", "isAJAX": false, "startTime": **********.205676, "totalTime": 89.2, "totalMemory": "9.427", "segmentDuration": 15, "segmentCount": 6, "CI_VERSION": "4.4.7", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.221503, "duration": 0.012350082397460938}, {"name": "Routing", "component": "Timer", "start": **********.233856, "duration": 7.796287536621094e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.23477, "duration": 1.8835067749023438e-05}, {"name": "Controller", "component": "Timer", "start": **********.234794, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.234795, "duration": 0.031152963638305664}, {"name": "After Filters", "component": "Timer", "start": **********.294934, "duration": 0.00039005279541015625}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(6 total Queries, 6 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.58 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Member.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": "  8    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": "  9    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "99bba06578426b1c7287ed6e239cc923"}, {"hover": "", "class": "", "duration": "0.47 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Member.php:221", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": "  8    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": "  9    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "0807efd58c110ab5cfa868a96e896591"}, {"hover": "", "class": "", "duration": "0.71 ms", "sql": "SHOW TABLES <strong>FROM</strong> `kh123`", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1410", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1429", "function": "        CodeIgniter\\Database\\BaseConnection->listTables()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php:12", "function": "        CodeIgniter\\Database\\BaseConnection->tableExists()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php:313", "function": "        call_user_func()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 13    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 14    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1410", "qid": "786479dd82a812f44f151182804f7c9c"}, {"hover": "", "class": "", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:615", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php:29", "function": "        Phpcmf\\Model->getRow()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php:313", "function": "        call_user_func()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 13    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 14    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "3476762cbe9b4d7f327eabe049c13f46"}, {"hover": "", "class": "", "duration": "0.62 ms", "sql": "<strong>SELECT</strong> count(*) as c <strong>FROM</strong> `dr_1_webnav` <strong>WHERE</strong> (`dr_1_webnav`.`catid` = 6 <strong>OR</strong> `dr_1_webnav`.`catids` <strong>LIKE</strong> &quot;%\\&quot;6\\&quot;%&quot;) <strong>ORDER</strong> <strong>BY</strong> <strong>NULL</strong>", "trace": [{"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1336", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Action/Module.php:287", "function": "        Phpcmf\\View->_query()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1284", "args": ["/home/<USER>/web/dayrui/App/Module/Action/Module.php"], "function": "        require()", "index": "  3    "}, {"file": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_list.html.cache.php:52", "function": "        Phpcmf\\View->list_tag()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:284", "args": ["/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_list.html.cache.php"], "function": "        include()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:458", "function": "        Phpcmf\\View->display()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Category.php:14", "function": "        Phpcmf\\Home\\Module->_Category()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Category->index()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1336", "qid": "4d4f81ca4cbfe3b93ff56a54deb1b7ae"}, {"hover": "", "class": "", "duration": "0.89 ms", "sql": "<strong>SELECT</strong> * <strong>FROM</strong> `dr_1_webnav` <strong>WHERE</strong> (`dr_1_webnav`.`catid` = 6 <strong>OR</strong> `dr_1_webnav`.`catids` <strong>LIKE</strong> &quot;%\\&quot;6\\&quot;%&quot;) <strong>ORDER</strong> <strong>BY</strong> `dr_1_webnav`.`displayorder` <strong>ASC</strong>,`dr_1_webnav`.`updatetime` <strong>DESC</strong> <strong>LIMIT</strong> 0,36", "trace": [{"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1336", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Action/Module.php:313", "function": "        Phpcmf\\View->_query()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1284", "args": ["/home/<USER>/web/dayrui/App/Module/Action/Module.php"], "function": "        require()", "index": "  3    "}, {"file": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_list.html.cache.php:52", "function": "        Phpcmf\\View->list_tag()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:284", "args": ["/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_list.html.cache.php"], "function": "        include()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:458", "function": "        Phpcmf\\View->display()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Category.php:14", "function": "        Phpcmf\\Home\\Module->_Category()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Category->index()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1336", "qid": "7b2273447f0ebd10fb0194b7926db92b"}]}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.2491, "duration": "0.000865"}, {"name": "Query", "component": "Database", "start": **********.250533, "duration": "0.000579", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.252101, "duration": "0.000466", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.26368, "duration": "0.000707", "query": "SHOW TABLES <strong>FROM</strong> `kh123`"}, {"name": "Query", "component": "Database", "start": **********.264541, "duration": "0.000549", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.284954, "duration": "0.000619", "query": "<strong>SELECT</strong> count(*) as c <strong>FROM</strong> `dr_1_webnav` <strong>WHERE</strong> (`dr_1_webnav`.`catid` = 6 <strong>OR</strong> `dr_1_webnav`.`catids` <strong>LIKE</strong> &quot;%\\&quot;6\\&quot;%&quot;) <strong>ORDER</strong> <strong>BY</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.286364, "duration": "0.000886", "query": "<strong>SELECT</strong> * <strong>FROM</strong> `dr_1_webnav` <strong>WHERE</strong> (`dr_1_webnav`.`catid` = 6 <strong>OR</strong> `dr_1_webnav`.`catids` <strong>LIKE</strong> &quot;%\\&quot;6\\&quot;%&quot;) <strong>ORDER</strong> <strong>BY</strong> `dr_1_webnav`.`displayorder` <strong>ASC</strong>,`dr_1_webnav`.`updatetime` <strong>DESC</strong> <strong>LIMIT</strong> 0,36"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": {"vars": [{"name": "meta_title", "value": "'AI音频工具_AI工具集'"}, {"name": "meta_keywords", "value": "'AI视频工具,AI视频工具官网,AI视频工具APP，AI视频工具大全,AI视频网站大全,AI视频软件大全,AI视频工具集合,AI视频工具库,AI视频工具箱,AI视频网址导航'"}, {"name": "meta_description", "value": "'酷好AI视频工具导航（kuhao123.com）收录了站长亲测的国内外数百个免费AI视频制作工具网站,包括最新最尖端的AI视频工具、AI图生视频、AI文生视频制作、AI视频配音频工具、AI视频有奖活动举办，AI视频提示词分享，AI视频mcp工作流，AI电影，AI短剧，AI自媒体项目等推荐一站式的AI视频工具制作分享网站'"}, {"name": "id", "value": "'6'"}, {"name": "cat", "value": "array (\n  'id' => '6',\n  'pid' => '0',\n  'pids' => '0',\n  'name' => 'AI音频工具',\n  'dirname' => 'ai-audio-tools',\n  'pdirname' => '',\n  'child' => '0',\n  'disabled' => '0',\n  'ismain' => '1',\n  'childids' => '6',\n  'thumb' => '',\n  'show' => '1',\n  'setting' => \n  array (\n    'edit' => 1,\n    'disabled' => 0,\n    'template' => \n    array (\n      'pagesize' => '36',\n      'mpagesize' => '20',\n      'list' => 'list.html',\n      'category' => 'category.html',\n      'search' => 'search.html',\n      'show' => 'show.html',\n    ),\n    'seo' => \n    array (\n      'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n      'show_title' => '[第{page}页{join}]{title}{join}{catname}{join}{SITE_NAME}',\n    ),\n    'getchild' => 0,\n    'chtml' => 0,\n    'html' => 0,\n    'urlrule' => 4,\n  ),\n  'displayorder' => '3',\n  'tubiao' => 'fas fa-music',\n  'mid' => 'webnav',\n  'topid' => '6',\n  'pcatpost' => 0,\n  'catids' => \n  array (\n    0 => '6',\n  ),\n  'is_post' => 1,\n  'tid' => 1,\n  'url' => 'https://www.kuhao123.com/webnav/ai-audio-tools/6.html',\n  'total' => '-',\n  'field' => \n  array (\n  ),\n)"}, {"name": "top", "value": "array (\n  'id' => '6',\n  'pid' => '0',\n  'pids' => '0',\n  'name' => 'AI音频工具',\n  'dirname' => 'ai-audio-tools',\n  'pdirname' => '',\n  'child' => '0',\n  'disabled' => '0',\n  'ismain' => '1',\n  'childids' => '6',\n  'thumb' => '',\n  'show' => '1',\n  'setting' => \n  array (\n    'edit' => 1,\n    'disabled' => 0,\n    'template' => \n    array (\n      'pagesize' => '36',\n      'mpagesize' => '20',\n      'list' => 'list.html',\n      'category' => 'category.html',\n      'search' => 'search.html',\n      'show' => 'show.html',\n    ),\n    'seo' => \n    array (\n      'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n      'show_title' => '[第{page}页{join}]{title}{join}{catname}{join}{SITE_NAME}',\n    ),\n    'getchild' => 0,\n    'chtml' => 0,\n    'html' => 0,\n    'urlrule' => 4,\n  ),\n  'displayorder' => '3',\n  'tubiao' => 'fas fa-music',\n  'mid' => 'webnav',\n  'topid' => '6',\n  'pcatpost' => 0,\n  'catids' => \n  array (\n    0 => '6',\n  ),\n  'is_post' => 1,\n  'tid' => 1,\n  'url' => 'https://www.kuhao123.com/webnav/ai-audio-tools/6.html',\n  'total' => '-',\n  'field' => \n  array (\n  ),\n)"}, {"name": "catid", "value": "'6'"}, {"name": "params", "value": "array (\n  'catid' => '6',\n)"}, {"name": "pageid", "value": "1"}, {"name": "parent", "value": "array (\n)"}, {"name": "related", "value": "array (\n  3 => \n  array (\n    'id' => '3',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => 'AI视频工具',\n    'dirname' => 'ai-video-tools',\n    'pdirname' => '',\n    'child' => '0',\n    'disabled' => '0',\n    'ismain' => '1',\n    'childids' => '3',\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'edit' => 1,\n      'disabled' => 0,\n      'template' => \n      array (\n        'pagesize' => '36',\n        'mpagesize' => '20',\n        'list' => 'list.html',\n        'category' => 'category.html',\n        'search' => 'search.html',\n        'show' => 'show.html',\n      ),\n      'seo' => \n      array (\n        'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n        'show_title' => '[第{page}页{join}]{title}{join}{catname}{join}{SITE_NAME}',\n      ),\n      'getchild' => 0,\n      'chtml' => 0,\n      'html' => 0,\n      'urlrule' => 4,\n    ),\n    'displayorder' => '1',\n    'tubiao' => 'fas fa-video',\n    'mid' => 'webnav',\n    'topid' => '3',\n    'pcatpost' => 0,\n    'catids' => \n    array (\n      0 => '3',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'https://www.kuhao123.com/webnav/ai-video-tools/3.html',\n    'total' => '-',\n    'field' => \n    array (\n    ),\n  ),\n  2 => \n  array (\n    'id' => '2',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => 'AI生图工具',\n    'dirname' => 'ai-image-tools',\n    'pdirname' => '',\n    'child' => '0',\n    'disabled' => '0',\n    'ismain' => '1',\n    'childids' => '2',\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'disabled' => '0',\n      'linkurl' => '',\n      'getchild' => '1',\n      'notedit' => '0',\n      'seo' => \n      array (\n        'list_title' => '[第{page}页{join}]{catpname}{join}{SITE_NAME}',\n        'list_keywords' => '',\n        'list_description' => '',\n      ),\n      'template' => \n      array (\n        'pagesize' => '36',\n        'mpagesize' => '20',\n        'list' => 'list.html',\n        'category' => 'category.html',\n        'search' => 'search.html',\n        'show' => 'show.html',\n      ),\n      'cat_field' => NULL,\n      'module_field' => NULL,\n      'chtml' => 0,\n      'html' => 0,\n      'urlrule' => 4,\n    ),\n    'displayorder' => '2',\n    'tubiao' => 'fas fa-images',\n    'mid' => 'webnav',\n    'topid' => '2',\n    'pcatpost' => 0,\n    'catids' => \n    array (\n      0 => '2',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'https://www.kuhao123.com/webnav/ai-image-tools/2.html',\n    'total' => '-',\n    'field' => \n    array (\n    ),\n  ),\n  6 => \n  array (\n    'id' => '6',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => 'AI音频工具',\n    'dirname' => 'ai-audio-tools',\n    'pdirname' => '',\n    'child' => '0',\n    'disabled' => '0',\n    'ismain' => '1',\n    'childids' => '6',\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'edit' => 1,\n      'disabled' => 0,\n      'template' => \n      array (\n        'pagesize' => '36',\n        'mpagesize' => '20',\n        'list' => 'list.html',\n        'category' => 'category.html',\n        'search' => 'search.html',\n        'show' => 'show.html',\n      ),\n      'seo' => \n      array (\n        'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n        'show_title' => '[第{page}页{join}]{title}{join}{catname}{join}{SITE_NAME}',\n      ),\n      'getchild' => 0,\n      'chtml' => 0,\n      'html' => 0,\n      'urlrule' => 4,\n    ),\n    'displayorder' => '3',\n    'tubiao' => 'fas fa-music',\n    'mid' => 'webnav',\n    'topid' => '6',\n    'pcatpost' => 0,\n    'catids' => \n    array (\n      0 => '6',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'https://www.kuhao123.com/webnav/ai-audio-tools/6.html',\n    'total' => '-',\n    'field' => \n    array (\n    ),\n  ),\n  5 => \n  array (\n    'id' => '5',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => 'AI视频文案',\n    'dirname' => 'ai-chatbots',\n    'pdirname' => '',\n    'child' => '0',\n    'disabled' => '0',\n    'ismain' => '1',\n    'childids' => '5',\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'disabled' => '0',\n      'linkurl' => '',\n      'getchild' => '0',\n      'notedit' => '0',\n      'seo' => \n      array (\n        'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n        'list_keywords' => '',\n        'list_description' => '',\n      ),\n      'template' => \n      array (\n        'pagesize' => '36',\n        'mpagesize' => '20',\n        'list' => 'list.html',\n        'category' => 'category.html',\n        'search' => 'search.html',\n        'show' => 'show.html',\n      ),\n      'cat_field' => NULL,\n      'module_field' => NULL,\n      'chtml' => 0,\n      'html' => 0,\n      'urlrule' => 4,\n    ),\n    'displayorder' => '4',\n    'tubiao' => 'fab fa-facebook-messenger',\n    'mid' => 'webnav',\n    'topid' => '5',\n    'pcatpost' => 0,\n    'catids' => \n    array (\n      0 => '5',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'https://www.kuhao123.com/webnav/ai-chatbots/5.html',\n    'total' => '-',\n    'field' => \n    array (\n    ),\n  ),\n)"}, {"name": "url<PERSON>le", "value": "'/webnav/ai-audio-tools/6/p[page].html'"}, {"name": "fix_html_now_url", "value": "''"}, {"name": "my_web_url", "value": "'https://www.kuhao123.com/webnav/ai-audio-tools/6.html'"}, {"name": "get", "value": "array (\n  's' => 'webnav',\n  'c' => 'category',\n  'dir' => 'ai-audio-tools',\n  'm' => 'index',\n)"}], "tips": [{"name": "header.html", "tips": "由于模板文件[/home/<USER>/web/template/pc/ainav/home/<USER>/header.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/template/pc/ainav/home/<USER>"}, {"name": "search_block.html", "tips": "由于模板文件[/home/<USER>/web/template/pc/ainav/home/<USER>/search_block.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/template/pc/ainav/home/<USER>"}, {"name": "footer.html", "tips": "由于模板文件[/home/<USER>/web/template/pc/ainav/home/<USER>/footer.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/template/pc/ainav/home/<USER>"}], "times": [{"tpl": 0.02}], "files": {"/home/<USER>/web/template/pc/ainav/home/<USER>/list.html": {"name": "list.html", "path": "/home/<USER>/web/template/pc/ainav/home/<USER>/list.html"}, "/home/<USER>/web/template/pc/ainav/home/<USER>": {"name": "footer.html", "path": "/home/<USER>/web/template/pc/ainav/home/<USER>"}}}, "badgeValue": 4, "isEmpty": false, "hasTabContent": true, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 191 )", "display": {"coreFiles": [], "userFiles": [{"path": "/home/<USER>/web/cache/config/domain_app.php", "name": "domain_app.php"}, {"path": "/home/<USER>/web/cache/config/domain_client.php", "name": "domain_client.php"}, {"path": "/home/<USER>/web/cache/config/site.php", "name": "site.php"}, {"path": "/home/<USER>/web/cache/config/system.php", "name": "system.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_footer.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_footer.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_header.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_header.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_search_block.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_search_block.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_list.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_list.html.cache.php"}, {"path": "/home/<USER>/web/dayrui/App/Form/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Action/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Action/Module.php", "name": "Module.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Module_init.php", "name": "Module_init.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Run.php", "name": "Run.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php", "name": "Module.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Libraries/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Models/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php", "name": "Login.php"}, {"path": "/home/<USER>/web/dayrui/App/Tag/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Autoload.php", "name": "Autoload.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Constants.php", "name": "Constants.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Feature.php", "name": "Feature.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Hook.php", "name": "Hook.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Common.php", "name": "Common.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseService.php", "name": "BaseService.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factories.php", "name": "Factories.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factory.php", "name": "Factory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Query.php", "name": "Query.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Timer.php", "name": "Timer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Events/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Header.php", "name": "Header.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Message.php", "name": "Message.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Response.php", "name": "Response.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/URI.php", "name": "URI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/Time.php", "name": "Time.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Log/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Modules/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouter.php", "name": "AutoRouter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Superglobals.php", "name": "Superglobals.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Escaper/Escaper.php", "name": "Escaper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LogLevel.php", "name": "LogLevel.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Helper.php", "name": "Helper.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php", "name": "Phpcmf.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Service.php", "name": "Service.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Catids.php", "name": "Catids.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Date.php", "name": "Date.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/File.php", "name": "File.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Text.php", "name": "Text.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Textarea.php", "name": "Textarea.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Field.php", "name": "Field.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Input.php", "name": "Input.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Lang.php", "name": "Lang.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Page.php", "name": "Page.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Seo.php", "name": "Seo.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Member.php", "name": "Member.php"}, {"path": "/home/<USER>/web/dayrui/My/Config/Version.php", "name": "Version.php"}, {"path": "/home/<USER>/web/public/api/language/zh-cn/lang.php", "name": "lang.php"}, {"path": "/home/<USER>/web/public/config/custom.php", "name": "custom.php"}, {"path": "/home/<USER>/web/public/config/database.php", "name": "database.php"}, {"path": "/home/<USER>/web/public/config/hooks.php", "name": "hooks.php"}, {"path": "/home/<USER>/web/public/config/page/pc/page.php", "name": "page.php"}, {"path": "/home/<USER>/web/public/config/rewrite.php", "name": "rewrite.php"}, {"path": "/home/<USER>/web/public/index.php", "name": "index.php"}]}, "badgeValue": 191, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"uri": "webnav/category/index", "url": "https://www.kuhao123.com/webnav/ai-audio-tools/6.html", "app": "webnav", "controller": "category", "method": "index", "file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Category.php"}], "get": {"s": "webnav", "c": "category", "dir": "ai-audio-tools", "m": "index"}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "6.63", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.18", "count": 6}}}, "badgeValue": 7, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.221588, "duration": 0.006632089614868164}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.25112, "duration": 3.3855438232421875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.252572, "duration": 2.6941299438476562e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.264393, "duration": 3.1948089599609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.265095, "duration": 2.5987625122070312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.285578, "duration": 3.1948089599609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.287255, "duration": 3.0994415283203125e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1750910995</pre>", "uid": "1", "_ci_previous_url": "https://www.kuhao123.com/index.php?s=diygg&amp;c=home&amp;m=index&amp;id=1", "auth_csrf_token": "c4b7ab6cf996162298be3d0173150d38"}, "get": {"s": "webnav", "c": "category", "dir": "ai-audio-tools", "m": "index"}, "headers": {"Cookie": "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJtbFh0amEzdWZFVnZpZ2JMdEZsZEE9PSIsInZhbHVlIjoiN0tlRlFRY1YyUFJQVnJDSStPVldLc1NPMldxblJYbW1PbnlZLzBMSlhXOUNnUkxCN0tzZnhYbTExZnBabmJZRTcva0lyazFzUUozT1NZMG96N2Z5RnUzZE5CM1lXQml2a2V5UTdoSUNuVU9nbXgyeGlSVDd4anBxd3R3R2hFODBEQmx4bUN4YXYxWHdLWXJGUGljMGVIb0dRam85Zk16Mk1xUytBMW0vcUpxeFQ2SHM0VVZvQjgybUlJTFpyTU5KRFdraWdjRG1pZzZ2eDZwSGs5ZWRQbVEwbGRYTWVab3pSY3Qyb043bkdkUT0iLCJtYWMiOiJhNjM0ZDIzYmYxZmJmOTM4YTA5ZDU5YzZlMWI0ZGFmNjQ2OTM1NjA3YmZmYmNiNTRhZDRmZjk2YTlmNWEyMmI5IiwidGFnIjoiIn0%3D; PHPSESSID=kjb18ivsdqceau93qdhn34vd0d; d41d8cd98f00b204e9800998ecf8427e_member_uid=1; d41d8cd98f00b204e9800998ecf8427e_member_cookie=5424b2695385ffdccec8d972124ec65f; csrf_cookie_name=cd8376769004a149d672615f02225890; ci_session=bfsq0jfq4aau88p4hrnbre22m219kqkh; d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-638=638; d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-447=447", "Priority": "u=0, i", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8", "Accept-Encoding": "gzip, deflate, br, zstd", "Referer": "https://www.kuhao123.com/", "Sec-Fetch-Dest": "document", "Sec-Fetch-User": "?1", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "same-origin", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Upgrade-Insecure-Requests": "1", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Host": "www.kuhao123.com"}, "cookies": {"remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6ImJtbFh0amEzdWZFVnZpZ2JMdEZsZEE9PSIsInZhbHVlIjoiN0tlRlFRY1YyUFJQVnJDSStPVldLc1NPMldxblJYbW1PbnlZLzBMSlhXOUNnUkxCN0tzZnhYbTExZnBabmJZRTcva0lyazFzUUozT1NZMG96N2Z5RnUzZE5CM1lXQml2a2V5UTdoSUNuVU9nbXgyeGlSVDd4anBxd3R3R2hFODBEQmx4bUN4YXYxWHdLWXJGUGljMGVIb0dRam85Zk16Mk1xUytBMW0vcUpxeFQ2SHM0VVZvQjgybUlJTFpyTU5KRFdraWdjRG1pZzZ2eDZwSGs5ZWRQbVEwbGRYTWVab3pSY3Qyb043bkdkUT0iLCJtYWMiOiJhNjM0ZDIzYmYxZmJmOTM4YTA5ZDU5YzZlMWI0ZGFmNjQ2OTM1NjA3YmZmYmNiNTRhZDRmZjk2YTlmNWEyMmI5IiwidGFnIjoiIn0=", "PHPSESSID": "kjb18ivsdqceau93qdhn34vd0d", "d41d8cd98f00b204e9800998ecf8427e_member_uid": "1", "d41d8cd98f00b204e9800998ecf8427e_member_cookie": "5424b2695385ffdccec8d972124ec65f", "csrf_cookie_name": "cd8376769004a149d672615f02225890", "ci_session": "bfsq0jfq4aau88p4hrnbre22m219kqkh", "d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-638": "638", "d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-447": "447"}, "request": "HTTPS/2.0", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.7", "phpVersion": "8.2.28", "phpSAPI": "fpm-fcgi", "environment": "development", "baseURL": "https://www.kuhao123.com/", "timezone": "PRC", "locale": "zh-cn", "cspEnabled": false}}