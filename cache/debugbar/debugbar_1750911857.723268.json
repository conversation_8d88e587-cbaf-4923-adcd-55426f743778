{"url": "https://www.kuhao123.com/index.php/webnav/ai-chatbots/show/440.html", "method": "GET", "isAJAX": false, "startTime": **********.627535, "totalTime": 92.3, "totalMemory": "9.321", "segmentDuration": 15, "segmentCount": 7, "CI_VERSION": "4.4.7", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.642198, "duration": 0.011406898498535156}, {"name": "Routing", "component": "Timer", "start": **********.653608, "duration": 4.482269287109375e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.654435, "duration": 2.9087066650390625e-05}, {"name": "Controller", "component": "Timer", "start": **********.654468, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.654469, "duration": 0.011728048324584961}, {"name": "After Filters", "component": "Timer", "start": **********.719893, "duration": 0.0005290508270263672}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(5 total Queries, 5 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.64 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav`\n<strong>WHERE</strong> `id` = 440", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:498", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Models/Content.php:944", "function": "        Phpcmf\\Model->get()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:483", "function": "        Phpcmf\\Model\\Module\\Content->get_data()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Show.php:12", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "5a26b96d1c6acaf2da4fe8b1913e1f49"}, {"hover": "", "class": "", "duration": "0.46 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_data_0`\n<strong>WHERE</strong> `id` = 440", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:498", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Models/Content.php:965", "function": "        Phpcmf\\Model->get()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:483", "function": "        Phpcmf\\Model\\Module\\Content->get_data()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Show.php:12", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "2747fadee51f19a65b981b0e4b3d6531"}, {"hover": "", "class": "", "duration": "9.76 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav`\n<strong>WHERE</strong> `catid` = 5\n<strong>AND</strong> `id` &lt; 440\n<strong>ORDER</strong> <strong>BY</strong> `id` desc\n <strong>LIMIT</strong> 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:788", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:503", "function": "        Phpcmf\\Home\\Module->_Show_Data()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Show.php:12", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": "  9    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 10    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "d483fd102deefde6f24d69ab8e8c289f"}, {"hover": "", "class": "", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav`\n<strong>WHERE</strong> `catid` = 5\n<strong>AND</strong> `id` &gt; 440\n<strong>ORDER</strong> <strong>BY</strong> `id` asc\n <strong>LIMIT</strong> 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:798", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:503", "function": "        Phpcmf\\Home\\Module->_Show_Data()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Show.php:12", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": "  9    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 10    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "42d80d85177c2df57ff3d5a772b3a1c5"}, {"hover": "", "class": "", "duration": "1.59 ms", "sql": "<strong>SELECT</strong> * <strong>FROM</strong> `dr_1_webnav` <strong>WHERE</strong> `dr_1_webnav`.`id` &lt;&gt; 440 <strong>AND</strong> ((`title` <strong>LIKE</strong> &quot;%AI对话工具%&quot; <strong>OR</strong> `keywords` <strong>LIKE</strong> &quot;%AI对话工具%&quot;) <strong>OR</strong> (`title` <strong>LIKE</strong> &quot;%热门AI办公工具%&quot; <strong>OR</strong> `keywords` <strong>LIKE</strong> &quot;%热门AI办公工具%&quot;)) <strong>ORDER</strong> <strong>BY</strong> `dr_1_webnav`.`updatetime` <strong>DESC</strong> <strong>LIMIT</strong> 9", "trace": [{"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1336", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Action/Module.php:313", "function": "        Phpcmf\\View->_query()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Action/Related.php:50", "args": ["/home/<USER>/web/dayrui/App/Module/Action/Module.php"], "function": "        require()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1284", "args": ["/home/<USER>/web/dayrui/App/Module/Action/Related.php"], "function": "        require()", "index": "  4    "}, {"file": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_show.html.cache.php:128", "function": "        Phpcmf\\View->list_tag()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:284", "args": ["/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_show.html.cache.php"], "function": "        include()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php:604", "function": "        Phpcmf\\View->display()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Show.php:12", "function": "        Phpcmf\\Home\\Module->_Show()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Show->index()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 13    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 14    "}], "trace-file": "/home/<USER>/web/dayrui/Fcms/Core/View.php:1336", "qid": "27dd412e42368f06d0d4ab520e9c075f"}]}, "badgeValue": 5, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.673566, "duration": "0.000864"}, {"name": "Query", "component": "Database", "start": **********.674951, "duration": "0.000636", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav`\n<strong>WHERE</strong> `id` = 440"}, {"name": "Query", "component": "Database", "start": **********.676891, "duration": "0.000457", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_data_0`\n<strong>WHERE</strong> `id` = 440"}, {"name": "Query", "component": "Database", "start": **********.686477, "duration": "0.009757", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav`\n<strong>WHERE</strong> `catid` = 5\n<strong>AND</strong> `id` &lt; 440\n<strong>ORDER</strong> <strong>BY</strong> `id` desc\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.696498, "duration": "0.000599", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav`\n<strong>WHERE</strong> `catid` = 5\n<strong>AND</strong> `id` &gt; 440\n<strong>ORDER</strong> <strong>BY</strong> `id` asc\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.709889, "duration": "0.001590", "query": "<strong>SELECT</strong> * <strong>FROM</strong> `dr_1_webnav` <strong>WHERE</strong> `dr_1_webnav`.`id` &lt;&gt; 440 <strong>AND</strong> ((`title` <strong>LIKE</strong> &quot;%AI对话工具%&quot; <strong>OR</strong> `keywords` <strong>LIKE</strong> &quot;%AI对话工具%&quot;) <strong>OR</strong> (`title` <strong>LIKE</strong> &quot;%热门AI办公工具%&quot; <strong>OR</strong> `keywords` <strong>LIKE</strong> &quot;%热门AI办公工具%&quot;)) <strong>ORDER</strong> <strong>BY</strong> `dr_1_webnav`.`updatetime` <strong>DESC</strong> <strong>LIMIT</strong> 9"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": {"vars": [{"name": "id", "value": "'440'"}, {"name": "catid", "value": "'5'"}, {"name": "title", "value": "'钉钉·个人版'"}, {"name": "thumb", "value": "'1025'"}, {"name": "keywords", "value": "'AI对话工具,热门AI办公工具'"}, {"name": "description", "value": "'钉钉推出的个人版办公应用程序，内置AI智能助手，可进行AI创作、AI对话、AI绘画'"}, {"name": "hits", "value": "'54'"}, {"name": "uid", "value": "'1'"}, {"name": "author", "value": "'创始人'"}, {"name": "status", "value": "'9'"}, {"name": "url", "value": "'/webnav/ai-chatbots/show/440.html'"}, {"name": "link_id", "value": "'0'"}, {"name": "tableid", "value": "'0'"}, {"name": "inputip", "value": "'127.0.0.1'"}, {"name": "inputtime", "value": "'2023-12-14'"}, {"name": "updatetime", "value": "'2023-12-14'"}, {"name": "displayorder", "value": "'0'"}, {"name": "tubiao", "value": "'1026'"}, {"name": "catids", "value": "array (\n)"}, {"name": "website", "value": "'https://workspace.dingtalk.com/welcome?utm_source=ai-bot.cn'"}, {"name": "support", "value": "'0'"}, {"name": "oppose", "value": "'0'"}, {"name": "fankui_total", "value": "'0'"}, {"name": "content", "value": "'<p>钉钉·个人版正式启动内测，所有用户皆可在钉钉官网申请加入测试，实测申请速度只需几分钟，其个人空间是基于钉钉收购的「wolai我来」在线协同文档并结合阿里的AI大模型打造而来。据介绍，钉钉个人版以AI为核心，提供“贾维斯”文生文AI和“缪斯”文生图AI。用户可通过自然语言对话，解答各种问题、制定策划方案或者绘画。</p>\n<h2>钉钉·个人版的AI功能</h2>\n<ol>\n<li>AI文档创作助手：在个人文档空间中，输入/ai即可召唤出AI助手，进行AI写作、文章大纲撰写、内容简介创作、小红书种草笔记、短视频脚本、电子邮件、广告文案、短篇故事、周会汇报、职位描述、待办清单等。</li>\n<li>贾维斯AI对话助手：基于通义千问大模型，贾维斯智能助手可以随时为你解答各种问题，并内置了常用指令，支持创建自定义指令。</li>\n<li>缪斯AI绘图：基于通义万相图像生成大模型，缪斯AI可以进行人像、摄影、游戏、插画、科幻、建筑、中国风等风格的AI图片生成，同样支持创建自定义指令</li>\n</ol>\n<img  class=\"alignnone size-full\" src=\"/uploadfile/202312/e3deba63fca42e6.png\"  alt=\"钉钉个人版AI功能\" width=\"801\" height=\"501\">\n<h2>如何使用钉钉·个人版</h2>\n<ol>\n<li>访问钉钉·个人版的官网，点击申请体验按钮</li>\n<li>使用手机版钉钉扫描二维码即可申请，等待几分钟，刷新网站，若右上角显示已获取资格便说明可以使用</li>\n<li>申请通过后可以在官网下载Mac/Windows桌面端软件，或直接访问workspace.dingtalk.com在线使用AI文档功能</li>\n<li>若要使用贾维斯和缪斯，则需要在桌面端软件使用，点击左侧的AI按钮，然后选择对应功能即可</li>\n</ol>'"}, {"name": "_inputtime", "value": "'1702548857'"}, {"name": "_updatetime", "value": "'1702548857'"}, {"name": "_catids", "value": "''"}, {"name": "tag", "value": "'AI对话工具,热门AI办公工具'"}, {"name": "kws", "value": "array (\n  'AI对话工具' => 'https://www.kuhao123.com/webnav/search/keyword-AI%E5%AF%B9%E8%AF%9D%E5%B7%A5%E5%85%B7.html',\n  '热门AI办公工具' => 'https://www.kuhao123.com/webnav/search/keyword-%E7%83%AD%E9%97%A8AI%E5%8A%9E%E5%85%AC%E5%B7%A5%E5%85%B7.html',\n)"}, {"name": "tags", "value": "array (\n  'AI对话工具' => '/#没有找到对应的URL（关闭开发者模式将不会显示本词）',\n  '热门AI办公工具' => '/#没有找到对应的URL（关闭开发者模式将不会显示本词）',\n)"}, {"name": "prev_page", "value": "array (\n  'id' => '439',\n  'catid' => '5',\n  'title' => 'Google Bard',\n  'thumb' => '1023',\n  'keywords' => 'AI对话工具',\n  'description' => 'Google推出的AI聊天对话机器人Bard',\n  'hits' => '96',\n  'uid' => '1',\n  'author' => '创始人',\n  'status' => '9',\n  'url' => 'https://www.kuhao123.com/webnav/ai-chatbots/show/439.html',\n  'link_id' => '0',\n  'tableid' => '0',\n  'inputip' => '127.0.0.1',\n  'inputtime' => '1702548887',\n  'updatetime' => '1702548887',\n  'displayorder' => '0',\n  'tubiao' => '1024',\n  'catids' => NULL,\n  'website' => 'https://cn.bing.com/search?q=Google Bard&ensearch=1',\n  'support' => '0',\n  'oppose' => '0',\n  'fankui_total' => '0',\n)"}, {"name": "next_page", "value": "array (\n  'id' => '441',\n  'catid' => '5',\n  'title' => '<PERSON>新必应',\n  'thumb' => '1028',\n  'keywords' => 'AI对话工具',\n  'description' => '微软推出的新版结合了ChatGPT功能的必应',\n  'hits' => '25',\n  'uid' => '1',\n  'author' => '创始人',\n  'status' => '9',\n  'url' => 'https://www.kuhao123.com/webnav/ai-chatbots/show/441.html',\n  'link_id' => '0',\n  'tableid' => '0',\n  'inputip' => '127.0.0.1',\n  'inputtime' => '1702548854',\n  'updatetime' => '1702548854',\n  'displayorder' => '0',\n  'tubiao' => '884',\n  'catids' => NULL,\n  'website' => 'https://cn.bing.com/search?q=Bing新必应&ensearch=1',\n  'support' => '0',\n  'oppose' => '0',\n  'fankui_total' => '0',\n)"}, {"name": "meta_title", "value": "'钉钉·个人版_AI视频文案_AI工具集'"}, {"name": "meta_keywords", "value": "'AI对话工具,热门AI办公工具'"}, {"name": "meta_description", "value": "'钉钉推出的个人版办公应用程序，内置AI智能助手，可进行AI创作、AI对话、AI绘画'"}, {"name": "cat", "value": "array (\n  'id' => '5',\n  'pid' => '0',\n  'pids' => '0',\n  'name' => 'AI视频文案',\n  'dirname' => 'ai-chatbots',\n  'pdirname' => '',\n  'child' => '0',\n  'disabled' => '0',\n  'ismain' => '1',\n  'childids' => '5',\n  'thumb' => '',\n  'show' => '1',\n  'setting' => \n  array (\n    'disabled' => '0',\n    'linkurl' => '',\n    'getchild' => '0',\n    'notedit' => '0',\n    'seo' => \n    array (\n      'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n      'list_keywords' => '',\n      'list_description' => '',\n    ),\n    'template' => \n    array (\n      'pagesize' => '36',\n      'mpagesize' => '20',\n      'list' => 'list.html',\n      'category' => 'category.html',\n      'search' => 'search.html',\n      'show' => 'show.html',\n    ),\n    'cat_field' => NULL,\n    'module_field' => NULL,\n    'chtml' => 0,\n    'html' => 0,\n    'urlrule' => 4,\n  ),\n  'displayorder' => '4',\n  'tubiao' => 'fab fa-facebook-messenger',\n  'mid' => 'webnav',\n  'topid' => '5',\n  'pcatpost' => 0,\n  'catids' => \n  array (\n    0 => '5',\n  ),\n  'is_post' => 1,\n  'tid' => 1,\n  'url' => 'https://www.kuhao123.com/webnav/ai-chatbots/5.html',\n  'total' => '-',\n  'field' => \n  array (\n  ),\n)"}, {"name": "top", "value": "array (\n  'id' => '5',\n  'pid' => '0',\n  'pids' => '0',\n  'name' => 'AI视频文案',\n  'dirname' => 'ai-chatbots',\n  'pdirname' => '',\n  'child' => '0',\n  'disabled' => '0',\n  'ismain' => '1',\n  'childids' => '5',\n  'thumb' => '',\n  'show' => '1',\n  'setting' => \n  array (\n    'disabled' => '0',\n    'linkurl' => '',\n    'getchild' => '0',\n    'notedit' => '0',\n    'seo' => \n    array (\n      'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n      'list_keywords' => '',\n      'list_description' => '',\n    ),\n    'template' => \n    array (\n      'pagesize' => '36',\n      'mpagesize' => '20',\n      'list' => 'list.html',\n      'category' => 'category.html',\n      'search' => 'search.html',\n      'show' => 'show.html',\n    ),\n    'cat_field' => NULL,\n    'module_field' => NULL,\n    'chtml' => 0,\n    'html' => 0,\n    'urlrule' => 4,\n  ),\n  'displayorder' => '4',\n  'tubiao' => 'fab fa-facebook-messenger',\n  'mid' => 'webnav',\n  'topid' => '5',\n  'pcatpost' => 0,\n  'catids' => \n  array (\n    0 => '5',\n  ),\n  'is_post' => 1,\n  'tid' => 1,\n  'url' => 'https://www.kuhao123.com/webnav/ai-chatbots/5.html',\n  'total' => '-',\n  'field' => \n  array (\n  ),\n)"}, {"name": "pageid", "value": "1"}, {"name": "params", "value": "array (\n  'catid' => '5',\n)"}, {"name": "parent", "value": "array (\n)"}, {"name": "related", "value": "array (\n  3 => \n  array (\n    'id' => '3',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => 'AI视频工具',\n    'dirname' => 'ai-video-tools',\n    'pdirname' => '',\n    'child' => '0',\n    'disabled' => '0',\n    'ismain' => '1',\n    'childids' => '3',\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'edit' => 1,\n      'disabled' => 0,\n      'template' => \n      array (\n        'pagesize' => '36',\n        'mpagesize' => '20',\n        'list' => 'list.html',\n        'category' => 'category.html',\n        'search' => 'search.html',\n        'show' => 'show.html',\n      ),\n      'seo' => \n      array (\n        'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n        'show_title' => '[第{page}页{join}]{title}{join}{catname}{join}{SITE_NAME}',\n      ),\n      'getchild' => 0,\n      'chtml' => 0,\n      'html' => 0,\n      'urlrule' => 4,\n    ),\n    'displayorder' => '1',\n    'tubiao' => 'fas fa-video',\n    'mid' => 'webnav',\n    'topid' => '3',\n    'pcatpost' => 0,\n    'catids' => \n    array (\n      0 => '3',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'https://www.kuhao123.com/webnav/ai-video-tools/3.html',\n    'total' => '-',\n    'field' => \n    array (\n    ),\n  ),\n  2 => \n  array (\n    'id' => '2',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => 'AI生图工具',\n    'dirname' => 'ai-image-tools',\n    'pdirname' => '',\n    'child' => '0',\n    'disabled' => '0',\n    'ismain' => '1',\n    'childids' => '2',\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'disabled' => '0',\n      'linkurl' => '',\n      'getchild' => '1',\n      'notedit' => '0',\n      'seo' => \n      array (\n        'list_title' => '[第{page}页{join}]{catpname}{join}{SITE_NAME}',\n        'list_keywords' => '',\n        'list_description' => '',\n      ),\n      'template' => \n      array (\n        'pagesize' => '36',\n        'mpagesize' => '20',\n        'list' => 'list.html',\n        'category' => 'category.html',\n        'search' => 'search.html',\n        'show' => 'show.html',\n      ),\n      'cat_field' => NULL,\n      'module_field' => NULL,\n      'chtml' => 0,\n      'html' => 0,\n      'urlrule' => 4,\n    ),\n    'displayorder' => '2',\n    'tubiao' => 'fas fa-images',\n    'mid' => 'webnav',\n    'topid' => '2',\n    'pcatpost' => 0,\n    'catids' => \n    array (\n      0 => '2',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'https://www.kuhao123.com/webnav/ai-image-tools/2.html',\n    'total' => '-',\n    'field' => \n    array (\n    ),\n  ),\n  6 => \n  array (\n    'id' => '6',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => 'AI音频工具',\n    'dirname' => 'ai-audio-tools',\n    'pdirname' => '',\n    'child' => '0',\n    'disabled' => '0',\n    'ismain' => '1',\n    'childids' => '6',\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'edit' => 1,\n      'disabled' => 0,\n      'template' => \n      array (\n        'pagesize' => '36',\n        'mpagesize' => '20',\n        'list' => 'list.html',\n        'category' => 'category.html',\n        'search' => 'search.html',\n        'show' => 'show.html',\n      ),\n      'seo' => \n      array (\n        'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n        'show_title' => '[第{page}页{join}]{title}{join}{catname}{join}{SITE_NAME}',\n      ),\n      'getchild' => 0,\n      'chtml' => 0,\n      'html' => 0,\n      'urlrule' => 4,\n    ),\n    'displayorder' => '3',\n    'tubiao' => 'fas fa-music',\n    'mid' => 'webnav',\n    'topid' => '6',\n    'pcatpost' => 0,\n    'catids' => \n    array (\n      0 => '6',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'https://www.kuhao123.com/webnav/ai-audio-tools/6.html',\n    'total' => '-',\n    'field' => \n    array (\n    ),\n  ),\n  5 => \n  array (\n    'id' => '5',\n    'pid' => '0',\n    'pids' => '0',\n    'name' => 'AI视频文案',\n    'dirname' => 'ai-chatbots',\n    'pdirname' => '',\n    'child' => '0',\n    'disabled' => '0',\n    'ismain' => '1',\n    'childids' => '5',\n    'thumb' => '',\n    'show' => '1',\n    'setting' => \n    array (\n      'disabled' => '0',\n      'linkurl' => '',\n      'getchild' => '0',\n      'notedit' => '0',\n      'seo' => \n      array (\n        'list_title' => '[第{page}页{join}]{name}{join}{SITE_NAME}',\n        'list_keywords' => '',\n        'list_description' => '',\n      ),\n      'template' => \n      array (\n        'pagesize' => '36',\n        'mpagesize' => '20',\n        'list' => 'list.html',\n        'category' => 'category.html',\n        'search' => 'search.html',\n        'show' => 'show.html',\n      ),\n      'cat_field' => NULL,\n      'module_field' => NULL,\n      'chtml' => 0,\n      'html' => 0,\n      'urlrule' => 4,\n    ),\n    'displayorder' => '4',\n    'tubiao' => 'fab fa-facebook-messenger',\n    'mid' => 'webnav',\n    'topid' => '5',\n    'pcatpost' => 0,\n    'catids' => \n    array (\n      0 => '5',\n    ),\n    'is_post' => 1,\n    'tid' => 1,\n    'url' => 'https://www.kuhao123.com/webnav/ai-chatbots/5.html',\n    'total' => '-',\n    'field' => \n    array (\n    ),\n  ),\n)"}, {"name": "url<PERSON>le", "value": "'/webnav/ai-chatbots/show/440/p[page].html'"}, {"name": "fix_html_now_url", "value": "''"}, {"name": "my_web_url", "value": "'https://www.kuhao123.com/webnav/ai-chatbots/show/440.html'"}, {"name": "get", "value": "array (\n  's' => 'webnav',\n  'c' => 'show',\n  'id' => '440',\n  'm' => 'index',\n)"}], "tips": [{"name": "header.html", "tips": "由于模板文件[/home/<USER>/web/template/pc/ainav/home/<USER>/header.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/template/pc/ainav/home/<USER>"}, {"name": "footer.html", "tips": "由于模板文件[/home/<USER>/web/template/pc/ainav/home/<USER>/footer.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/template/pc/ainav/home/<USER>"}], "times": [{"tpl": 0.02}], "files": {"/home/<USER>/web/template/pc/ainav/home/<USER>/show.html": {"name": "show.html", "path": "/home/<USER>/web/template/pc/ainav/home/<USER>/show.html"}, "/home/<USER>/web/template/pc/ainav/home/<USER>": {"name": "footer.html", "path": "/home/<USER>/web/template/pc/ainav/home/<USER>"}}}, "badgeValue": 3, "isEmpty": false, "hasTabContent": true, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 187 )", "display": {"coreFiles": [], "userFiles": [{"path": "/home/<USER>/web/cache/config/domain_app.php", "name": "domain_app.php"}, {"path": "/home/<USER>/web/cache/config/domain_client.php", "name": "domain_client.php"}, {"path": "/home/<USER>/web/cache/config/site.php", "name": "site.php"}, {"path": "/home/<USER>/web/cache/config/system.php", "name": "system.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_footer.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_footer.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_header.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_header.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_show.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_template_DS_pc_DS_ainav_DS_home_DS_webnav_DS_show.html.cache.php"}, {"path": "/home/<USER>/web/dayrui/App/Form/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Action/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Action/Module.php", "name": "Module.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Action/Related.php", "name": "Related.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Module_init.php", "name": "Module_init.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Run.php", "name": "Run.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Extends/Home/Module.php", "name": "Module.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Libraries/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Models/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Tag/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Tag/Models/Tag.php", "name": "Tag.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Show.php", "name": "Show.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Autoload.php", "name": "Autoload.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Constants.php", "name": "Constants.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Feature.php", "name": "Feature.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Hook.php", "name": "Hook.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Common.php", "name": "Common.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseService.php", "name": "BaseService.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factories.php", "name": "Factories.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factory.php", "name": "Factory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Query.php", "name": "Query.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Timer.php", "name": "Timer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Events/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Header.php", "name": "Header.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Message.php", "name": "Message.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Response.php", "name": "Response.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/URI.php", "name": "URI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/Time.php", "name": "Time.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Log/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Modules/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouter.php", "name": "AutoRouter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Security/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Security/SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Superglobals.php", "name": "Superglobals.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Escaper/Escaper.php", "name": "Escaper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LogLevel.php", "name": "LogLevel.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Helper.php", "name": "Helper.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php", "name": "Phpcmf.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Service.php", "name": "Service.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Catids.php", "name": "Catids.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Date.php", "name": "Date.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Editor.php", "name": "Editor.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/File.php", "name": "File.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Text.php", "name": "Text.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Textarea.php", "name": "Textarea.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Field.php", "name": "Field.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Input.php", "name": "Input.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Lang.php", "name": "Lang.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Seo.php", "name": "Seo.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Member.php", "name": "Member.php"}, {"path": "/home/<USER>/web/dayrui/My/Config/Version.php", "name": "Version.php"}, {"path": "/home/<USER>/web/public/api/language/zh-cn/lang.php", "name": "lang.php"}, {"path": "/home/<USER>/web/public/config/custom.php", "name": "custom.php"}, {"path": "/home/<USER>/web/public/config/database.php", "name": "database.php"}, {"path": "/home/<USER>/web/public/config/field.php", "name": "field.php"}, {"path": "/home/<USER>/web/public/config/hooks.php", "name": "hooks.php"}, {"path": "/home/<USER>/web/public/config/rewrite.php", "name": "rewrite.php"}, {"path": "/home/<USER>/web/public/index.php", "name": "index.php"}]}, "badgeValue": 187, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"uri": "webnav/show/index", "url": "https://www.kuhao123.com/webnav/ai-chatbots/show/440.html", "app": "webnav", "controller": "show", "method": "index", "file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Show.php"}], "get": {"s": "webnav", "c": "show", "id": "440", "m": "index"}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "5.26", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.19", "count": 5}}}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.642311, "duration": 0.0052568912506103516}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.675594, "duration": 5.4836273193359375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.677355, "duration": 3.2901763916015625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.696254, "duration": 4.00543212890625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.697103, "duration": 2.7894973754882812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.711487, "duration": 3.695487976074219e-05}]}], "vars": {"varData": {"View Data": []}, "get": {"s": "webnav", "c": "show", "id": "440", "m": "index"}, "headers": {"Upgrade-Insecure-Requests": "1", "Pragma": "no-cache", "Connection": "keep-alive, close", "Cache-Control": "no-cache", "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7", "Accept-Encoding": "gzip", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1", "Host": "www.kuhao123.com"}, "request": "HTTPS/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.7", "phpVersion": "8.2.28", "phpSAPI": "fpm-fcgi", "environment": "development", "baseURL": "https://www.kuhao123.com/", "timezone": "PRC", "locale": "zh-cn", "cspEnabled": false}}