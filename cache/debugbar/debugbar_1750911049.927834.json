{"url": "https://www.kuhao123.com/admin3faf81d65fd6.php", "method": "GET", "isAJAX": false, "startTime": **********.756872, "totalTime": 166.5, "totalMemory": "10.300", "segmentDuration": 25, "segmentCount": 7, "CI_VERSION": "4.4.7", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.796926, "duration": 0.027652978897094727}, {"name": "Routing", "component": "Timer", "start": **********.824584, "duration": 0.00013494491577148438}, {"name": "Before Filters", "component": "Timer", "start": **********.826616, "duration": 6.890296936035156e-05}, {"name": "Controller", "component": "Timer", "start": **********.826696, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.8267, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.923417, "duration": 0.00044798851013183594}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(9 total Queries, 9 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "1.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Member.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "a0e534dbdfe25b63794620aa4e5d5b08"}, {"hover": "", "class": "", "duration": "0.63 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Member.php:221", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "33398249a860fd56733d84efe432a256"}, {"hover": "", "class": "", "duration": "0.52 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:328", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:593", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "f5c55e7a40bdb0bedfbeb3f92162b2fc"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:103", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:340", "function": "        Phpcmf\\Model\\Auth->_role()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:593", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 13    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 14    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "d61db0f128e904c4e1f4d2a3f9e68cd4"}, {"hover": "", "class": "", "duration": "0.73 ms", "sql": "SHOW TABLES <strong>FROM</strong> `kh123`", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1410", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1429", "function": "        CodeIgniter\\Database\\BaseConnection->listTables()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php:12", "function": "        CodeIgniter\\Database\\BaseConnection->tableExists()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php:313", "function": "        call_user_func()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 15    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 16    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 17    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1410", "qid": "e555335ea8e0f1ce1de36ecde88b0fa1"}, {"hover": "", "class": "", "duration": "0.44 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:615", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php:29", "function": "        Phpcmf\\Model->getRow()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php:313", "function": "        call_user_func()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 15    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 16    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 17    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "f64cdd2e988c79c7c126349f7f0a7de7"}, {"hover": "", "class": "", "duration": "1.13 ms", "sql": "<strong>SELECT</strong> count(*) as total\n<strong>FROM</strong> `dr_1_webnav`", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:1026", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:753", "function": "        Phpcmf\\Model->limit_page()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:132", "function": "        Phpcmf\\Table->_List()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Home.php:11", "function": "        Phpcmf\\Admin\\Module->_Admin_List()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Home->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "ebb291531783ff8ff76c001f9c97bc83"}, {"hover": "", "class": "", "duration": "0.49 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav`\n<strong>ORDER</strong> <strong>BY</strong> `updatetime` <strong>DESC</strong>\n <strong>LIMIT</strong> 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:1079", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:753", "function": "        Phpcmf\\Model->limit_page()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:132", "function": "        Phpcmf\\Table->_List()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Home.php:11", "function": "        Phpcmf\\Admin\\Module->_Admin_List()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Home->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "eb7f254a2f39c913e5473cdcf6dcaa2c"}, {"hover": "", "class": "", "duration": "1.33 ms", "sql": "SHOW COLUMNS <strong>FROM</strong> `dr_1_webnav`", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1486", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1514", "function": "        CodeIgniter\\Database\\BaseConnection->getFieldNames()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:173", "function": "        CodeIgniter\\Database\\BaseConnection->fieldExists()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:171", "function": "        Phpcmf\\Model->is_field_exists()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Home.php:11", "function": "        Phpcmf\\Admin\\Module->_Admin_List()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Home->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1486", "qid": "0c30e1da8be92f6f0b5656ad034aff43"}]}, "badgeValue": 9, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.868589, "duration": "0.001243"}, {"name": "Query", "component": "Database", "start": **********.871537, "duration": "0.001237", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.875617, "duration": "0.000633", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.886455, "duration": "0.000516", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.887154, "duration": "0.000382", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.888083, "duration": "0.000733", "query": "SHOW TABLES <strong>FROM</strong> `kh123`"}, {"name": "Query", "component": "Database", "start": **********.888965, "duration": "0.000440", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.899924, "duration": "0.001131", "query": "<strong>SELECT</strong> count(*) as total\n<strong>FROM</strong> `dr_1_webnav`"}, {"name": "Query", "component": "Database", "start": **********.901287, "duration": "0.000488", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav`\n<strong>ORDER</strong> <strong>BY</strong> `updatetime` <strong>DESC</strong>\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.910465, "duration": "0.001334", "query": "SHOW COLUMNS <strong>FROM</strong> `dr_1_webnav`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": {"vars": [{"name": "is_ajax", "value": "''"}, {"name": "is_mobile", "value": "0"}, {"name": "field", "value": "array (\n  'catids' => \n  array (\n    'id' => '17',\n    'name' => '副栏目',\n    'fieldname' => 'catids',\n    'fieldtype' => 'Catids',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'option' => \n      array (\n        'width' => '',\n        'css' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '-10',\n  ),\n  'title' => \n  array (\n    'id' => '9',\n    'name' => '主题',\n    'fieldname' => 'title',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '1',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => 400,\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n      ),\n      'validate' => \n      array (\n        'xss' => 1,\n        'required' => 1,\n        'formattr' => 'onblur=\"check_title();get_keywords(\\'keywords\\');\"',\n      ),\n    ),\n    'displayorder' => '-5',\n  ),\n  'tubiao' => \n  array (\n    'id' => '16',\n    'name' => '图标',\n    'fieldname' => 'tubiao',\n    'fieldtype' => 'File',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '300',\n        'input' => '1',\n        'ext' => 'jpg,gif,png,jpeg',\n        'size' => '1',\n        'chunk' => '1',\n        'attachment' => '0',\n        'image_reduce' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '-2',\n  ),\n  'thumb' => \n  array (\n    'id' => '10',\n    'name' => '缩略图',\n    'fieldname' => 'thumb',\n    'fieldtype' => 'File',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'ext' => 'jpg,gif,png,jpeg',\n        'size' => '1',\n        'attachment' => '0',\n        'image_reduce' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '0',\n  ),\n  'keywords' => \n  array (\n    'id' => '11',\n    'name' => '标签',\n    'fieldname' => 'keywords',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'value' => '',\n        'width' => '400',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => ' data-role=\"tagsinput\"',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '0',\n  ),\n  'description' => \n  array (\n    'id' => '12',\n    'name' => '描述',\n    'fieldname' => 'description',\n    'fieldtype' => 'Textarea',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '1',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => 500,\n        'height' => 60,\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n      ),\n      'validate' => \n      array (\n        'xss' => 1,\n        'filter' => 'dr_filter_description',\n      ),\n    ),\n    'displayorder' => '0',\n  ),\n  'author' => \n  array (\n    'id' => '13',\n    'name' => '笔名',\n    'fieldname' => 'author',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '1',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'is_right' => 1,\n      'option' => \n      array (\n        'width' => 200,\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'value' => '{name}',\n      ),\n      'validate' => \n      array (\n        'xss' => 1,\n      ),\n    ),\n    'displayorder' => '0',\n  ),\n  'content' => \n  array (\n    'id' => '14',\n    'name' => '内容',\n    'fieldname' => 'content',\n    'fieldtype' => 'Editor',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '0',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'show_bottom_boot' => '1',\n        'tool_select_3' => '1',\n        'imgtitle' => '0',\n        'imgalt' => '0',\n        'watermark' => '0',\n        'image_ext' => 'jpg,gif,png,webp,jpeg',\n        'image_size' => '2',\n        'attach_size' => '200',\n        'attach_ext' => 'zip,rar,txt,doc',\n        'video_ext' => 'mp4',\n        'video_size' => '500',\n        'attachment' => '0',\n        'value' => '',\n        'width' => '100%',\n        'height' => '400',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '1',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '0',\n  ),\n  'website' => \n  array (\n    'id' => '26',\n    'name' => '官网',\n    'fieldname' => 'website',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '300',\n        'value' => '',\n        'width' => '100%',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '0',\n  ),\n  'inputtime' => \n  array (\n    'name' => '录入时间',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Date',\n    'fieldname' => 'inputtime',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'value' => 'SYS_TIME',\n        'is_left' => 1,\n      ),\n      'validate' => \n      array (\n        'required' => 1,\n      ),\n    ),\n  ),\n  'updatetime' => \n  array (\n    'name' => '更新时间',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Date',\n    'fieldname' => 'updatetime',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'value' => 'SYS_TIME',\n        'is_left' => 1,\n      ),\n      'validate' => \n      array (\n        'required' => 1,\n      ),\n    ),\n  ),\n  'inputip' => \n  array (\n    'name' => '客户端IP',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Textbtn',\n    'fieldname' => 'inputip',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'name' => '查看',\n        'icon' => 'fa fa-arrow-right',\n        'func' => 'dr_show_ip',\n        'value' => '**************-23046',\n      ),\n    ),\n  ),\n  'displayorder' => \n  array (\n    'name' => '排列值',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Touchspin',\n    'fieldname' => 'displayorder',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'max' => '',\n        'min' => '0',\n        'step' => '1',\n        'show' => '1',\n        'value' => 0,\n      ),\n    ),\n  ),\n  'hits' => \n  array (\n    'name' => '浏览数',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Touchspin',\n    'fieldname' => 'hits',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'max' => '9999999',\n        'min' => '1',\n        'step' => '1',\n        'show' => '1',\n        'value' => 1,\n      ),\n    ),\n  ),\n  'uid' => \n  array (\n    'name' => '账号',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Uid',\n    'fieldname' => 'uid',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '200px',\n      ),\n      'validate' => \n      array (\n        'check' => '_check_member',\n      ),\n    ),\n  ),\n)"}, {"name": "module", "value": "array (\n  'id' => '2',\n  'name' => '网址',\n  'icon' => 'fa fa-navicon',\n  'site' => \n  array (\n    1 => \n    array (\n      'html' => '0',\n      'theme' => 'default',\n      'domain' => '',\n      'template' => 'default',\n      'urlrule' => '4',\n      'is_cat' => '0',\n      'show_title' => '[第{page}页{join}]{title}{join}{catpname}{join}{SITE_NAME}',\n      'show_keywords' => '',\n      'show_description' => '',\n      'list_title' => '[第{page}页{join}]{catpname}{join}{SITE_NAME}',\n      'list_keywords' => '',\n      'list_description' => '',\n      'search_title' => '[第{page}页{join}][{keyword}{join}][{param}{join}]{SITE_NAME}',\n      'search_keywords' => '',\n      'search_description' => '',\n      'module_title' => '',\n      'module_keywords' => '',\n      'module_description' => '',\n    ),\n  ),\n  'config' => \n  array (\n    'type' => 'module',\n    'name' => '网址',\n    'icon' => 'fa fa-navicon',\n    'system' => '1',\n  ),\n  'share' => '0',\n  'setting' => \n  array (\n    'module_index_html' => 1,\n    'sync_category' => '0',\n    'pcatpost' => '0',\n    'updatetime_select' => '0',\n    'merge' => '0',\n    'right_field' => '0',\n    'desc_auto' => '0',\n    'desc_limit' => '100',\n    'kws_limit' => '10',\n    'desc_clear' => '0',\n    'hits_min' => '',\n    'hits_max' => '',\n    'verify_num' => '10',\n    'verify_msg' => '',\n    'delete_msg' => '',\n    'is_hide_search_bar' => '0',\n    'order' => 'updatetime DESC',\n    'search_time' => 'updatetime',\n    'search_first_field' => 'title',\n    'is_op_more' => '0',\n    'list_field' => \n    array (\n      'id' => \n      array (\n        'use' => '1',\n        'name' => 'Id',\n        'width' => '80',\n        'func' => '',\n      ),\n      'title' => \n      array (\n        'use' => '1',\n        'name' => '主题',\n        'width' => '',\n        'func' => 'title',\n      ),\n      'catid' => \n      array (\n        'use' => '1',\n        'name' => '栏目',\n        'width' => '130',\n        'func' => 'catid',\n      ),\n      'catids' => \n      array (\n        'use' => '1',\n        'name' => '副栏目',\n        'width' => '',\n        'func' => 'catids',\n      ),\n      'keywords' => \n      array (\n        'use' => '1',\n        'name' => '标签',\n        'width' => '',\n        'func' => '',\n      ),\n      'author' => \n      array (\n        'use' => '1',\n        'name' => '笔名',\n        'width' => '120',\n        'func' => 'author',\n      ),\n      'updatetime' => \n      array (\n        'use' => '1',\n        'name' => '更新时间',\n        'width' => '160',\n        'func' => 'datetime',\n      ),\n    ),\n    'flag' => \n    array (\n      1 => \n      array (\n        'name' => '热门推荐',\n        'icon' => 'fa fa-fire',\n        'role' => \n        array (\n        ),\n      ),\n    ),\n    'param' => NULL,\n    'search' => \n    array (\n      'use' => '1',\n      'catsync' => '0',\n      'indexsync' => '0',\n      'show_seo' => '0',\n      'search_404' => '0',\n      'search_param' => '0',\n      'complete' => '0',\n      'is_like' => '0',\n      'is_double_like' => '0',\n      'max' => '0',\n      'length' => '1',\n      'maxlength' => '0',\n      'param_join' => '-',\n      'param_rule' => '0',\n      'param_field' => '',\n      'param_join_field' => \n      array (\n        0 => '',\n        1 => '',\n        2 => '',\n        3 => '',\n        4 => '',\n        5 => '',\n        6 => '',\n        7 => '',\n        8 => '',\n        9 => '',\n        10 => '',\n      ),\n      'param_join_default_value' => '0',\n      'tpl_field' => '',\n      'field' => 'title,keywords',\n    ),\n  ),\n  'dirname' => 'webnav',\n  'domain' => '',\n  'mobile_domain' => '',\n  'cname' => '网址',\n  'html' => '0',\n  'title' => '网址',\n  'urlrule' => '4',\n  'url' => '/webnav.html',\n  'murl' => 'https://www.kuhao123.com/webnav.html',\n  'field' => \n  array (\n    'catids' => \n    array (\n      'id' => '17',\n      'name' => '副栏目',\n      'fieldname' => 'catids',\n      'fieldtype' => 'Catids',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'option' => \n        array (\n          'width' => '',\n          'css' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '-10',\n    ),\n    'title' => \n    array (\n      'id' => '9',\n      'name' => '主题',\n      'fieldname' => 'title',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'width' => 400,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n          'required' => 1,\n          'formattr' => 'onblur=\"check_title();get_keywords(\\'keywords\\');\"',\n        ),\n      ),\n      'displayorder' => '-5',\n    ),\n    'tubiao' => \n    array (\n      'id' => '16',\n      'name' => '图标',\n      'fieldname' => 'tubiao',\n      'fieldtype' => 'File',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '300',\n          'input' => '1',\n          'ext' => 'jpg,gif,png,jpeg',\n          'size' => '1',\n          'chunk' => '1',\n          'attachment' => '0',\n          'image_reduce' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '-2',\n    ),\n    'thumb' => \n    array (\n      'id' => '10',\n      'name' => '缩略图',\n      'fieldname' => 'thumb',\n      'fieldtype' => 'File',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'ext' => 'jpg,gif,png,jpeg',\n          'size' => '1',\n          'attachment' => '0',\n          'image_reduce' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'keywords' => \n    array (\n      'id' => '11',\n      'name' => '标签',\n      'fieldname' => 'keywords',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'value' => '',\n          'width' => '400',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => ' data-role=\"tagsinput\"',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'description' => \n    array (\n      'id' => '12',\n      'name' => '描述',\n      'fieldname' => 'description',\n      'fieldtype' => 'Textarea',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'width' => 500,\n          'height' => 60,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n          'filter' => 'dr_filter_description',\n        ),\n      ),\n      'displayorder' => '0',\n    ),\n    'author' => \n    array (\n      'id' => '13',\n      'name' => '笔名',\n      'fieldname' => 'author',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'is_right' => 1,\n        'option' => \n        array (\n          'width' => 200,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'value' => '{name}',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n        ),\n      ),\n      'displayorder' => '0',\n    ),\n    'content' => \n    array (\n      'id' => '14',\n      'name' => '内容',\n      'fieldname' => 'content',\n      'fieldtype' => 'Editor',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '0',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'show_bottom_boot' => '1',\n          'tool_select_3' => '1',\n          'imgtitle' => '0',\n          'imgalt' => '0',\n          'watermark' => '0',\n          'image_ext' => 'jpg,gif,png,webp,jpeg',\n          'image_size' => '2',\n          'attach_size' => '200',\n          'attach_ext' => 'zip,rar,txt,doc',\n          'video_ext' => 'mp4',\n          'video_size' => '500',\n          'attachment' => '0',\n          'value' => '',\n          'width' => '100%',\n          'height' => '400',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '1',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'website' => \n    array (\n      'id' => '26',\n      'name' => '官网',\n      'fieldname' => 'website',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '300',\n          'value' => '',\n          'width' => '100%',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n  ),\n  'system' => '1',\n  'category_data_field' => \n  array (\n  ),\n  'category' => \n  array (\n    0 => 'webnav',\n  ),\n  'form' => \n  array (\n    'fankui' => \n    array (\n      'id' => '2',\n      'name' => '反馈',\n      'table' => 'fankui',\n      'module' => 'webnav',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'seo' => \n        array (\n          'title' => '{title}{join}{formname}{join}{SITE_NAME}',\n          'keywords' => '',\n          'description' => '',\n        ),\n        'icon' => '',\n        'is_read' => '0',\n        'is_close_post' => '0',\n        'is_post_code' => '1',\n        'is_verify' => '0',\n        'rt_text' => '',\n        'rt_text2' => '',\n        'rt_url' => '',\n        'order' => 'inputtime DESC',\n        'search_time' => 'inputtime',\n        'list_field' => \n        array (\n          'title' => \n          array (\n            'use' => '1',\n            'name' => '主题',\n            'width' => '0',\n            'func' => 'title',\n          ),\n          'uid' => \n          array (\n            'use' => '1',\n            'name' => '账号',\n            'width' => '100',\n            'func' => 'uid',\n          ),\n          'inputtime' => \n          array (\n            'use' => '1',\n            'name' => '录入时间',\n            'width' => '160',\n            'func' => 'datetime',\n          ),\n        ),\n      ),\n      'field' => \n      array (\n        'title' => \n        array (\n          'id' => '39',\n          'name' => '主题',\n          'fieldname' => 'title',\n          'fieldtype' => 'Text',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '1',\n          'ismember' => '1',\n          'issearch' => '1',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'option' => \n            array (\n              'width' => 300,\n              'fieldtype' => 'VARCHAR',\n              'fieldlength' => '255',\n            ),\n            'validate' => \n            array (\n              'xss' => 1,\n              'required' => 1,\n            ),\n          ),\n          'displayorder' => '0',\n        ),\n        'author' => \n        array (\n          'id' => '40',\n          'name' => '作者',\n          'fieldname' => 'author',\n          'fieldtype' => 'Text',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '1',\n          'ismember' => '1',\n          'issearch' => '1',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'is_right' => 1,\n            'option' => \n            array (\n              'width' => 200,\n              'fieldtype' => 'VARCHAR',\n              'fieldlength' => '255',\n            ),\n            'validate' => \n            array (\n              'xss' => 1,\n            ),\n          ),\n          'displayorder' => '0',\n        ),\n        'fknr' => \n        array (\n          'id' => '41',\n          'name' => '反馈内容',\n          'fieldname' => 'fknr',\n          'fieldtype' => 'Textarea',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '0',\n          'ismember' => '1',\n          'issearch' => '0',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'option' => \n            array (\n              'value' => '',\n              'fieldtype' => 'TEXT',\n              'fieldlength' => '',\n              'width' => '',\n              'height' => '',\n              'css' => '',\n            ),\n            'validate' => \n            array (\n              'xss' => '1',\n              'required' => '0',\n              'pattern' => '',\n              'errortips' => '',\n              'check' => '',\n              'filter' => '',\n              'tips' => '',\n              'formattr' => '',\n            ),\n            'is_right' => '0',\n          ),\n          'displayorder' => '0',\n        ),\n      ),\n    ),\n  ),\n  'mid' => 'webnav',\n  'comment' => 0,\n)"}, {"name": "post_url", "value": "'admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid=0'"}, {"name": "is_post_user", "value": "0"}, {"name": "is_hcategory", "value": "false"}, {"name": "is_right_field", "value": "1"}, {"name": "is_category_show", "value": "1"}, {"name": "list", "value": "array (\n  0 => \n  array (\n    'id' => '399',\n    'catid' => '3',\n    'title' => '一帧秒创',\n    'thumb' => '915',\n    'keywords' => 'AI视频工具',\n    'description' => '简单好用的AI智能视频创作平台',\n    'hits' => '88',\n    'uid' => '1',\n    'author' => '创始人',\n    'status' => '9',\n    'url' => '/webnav/ai-video-tools/show/399.html',\n    'link_id' => '0',\n    'tableid' => '0',\n    'inputip' => '127.0.0.1',\n    'inputtime' => '2023-12-14',\n    'updatetime' => '2023-12-14',\n    'displayorder' => '0',\n    'tubiao' => '916',\n    'catids' => \n    array (\n    ),\n    'website' => 'https://aigc.yizhentv.com/?_f=botrm',\n    'support' => '0',\n    'oppose' => '0',\n    'fankui_total' => '0',\n    '_inputtime' => '1702550200',\n    '_updatetime' => '1702550200',\n    '_catids' => NULL,\n  ),\n)"}, {"name": "total", "value": "124"}, {"name": "param", "value": "array (\n  'total' => '124',\n  'order' => '',\n  'field' => 'title',\n)"}, {"name": "mypages", "value": "'<ul class=\"pagination\"><li><a>共124条</a></li><li class=\"active\"><a>1</a></li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&total=124&order=&field=title&page=2\" data-ci-pagination-page=\"2\">2</a></li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&total=124&order=&field=title&page=3\" data-ci-pagination-page=\"3\">3</a></li><a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&total=124&order=&field=title&page=2\" data-ci-pagination-page=\"2\"></a><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&total=124&order=&field=title&page=124\" data-ci-pagination-page=\"124\">最后一页</a></li><li class=\"input-page\"><div class=\"input-group\">\n                    <input type=\"text\" id=\"dr_pagination_input_pageid\" value=\"1\" class=\"form-control\" placeholder=\"页\">\n                    <span class=\"input-group-btn\">\n                        <button onclick=\"dr_page_go_url()\" class=\"btn\" type=\"button\">跳转</button>\n                    </span>\n                    <script>\n                    function dr_page_go_url() {\n                        var u = \"admin3faf81d65fd6.php?s=webnav&c=home&m=index&total=124&order=&field=title&page={page}\";\n                        var p = $(\\'#dr_pagination_input_pageid\\').val();\n                        if (!p || p == \\'\\') {\n                            dr_tips(0, \\'输入页码\\');\n                            return false;\n                        }\n                        window.location.href= u.replace(/\\\\{page\\\\}/i, p);\n                    }\n</script>\n                </div></li></ul>'"}, {"name": "my_file", "value": "'share_table.html'"}, {"name": "uriprefix", "value": "'webnav/home'"}, {"name": "list_field", "value": "array (\n  'id' => \n  array (\n    'use' => '1',\n    'name' => 'Id',\n    'width' => '80',\n    'func' => '',\n  ),\n  'title' => \n  array (\n    'use' => '1',\n    'name' => '主题',\n    'width' => '',\n    'func' => 'title',\n  ),\n  'catid' => \n  array (\n    'use' => '1',\n    'name' => '栏目',\n    'width' => '130',\n    'func' => 'catid',\n  ),\n  'catids' => \n  array (\n    'use' => '1',\n    'name' => '副栏目',\n    'width' => '',\n    'func' => 'catids',\n  ),\n  'keywords' => \n  array (\n    'use' => '1',\n    'name' => '标签',\n    'width' => '',\n    'func' => '',\n  ),\n  'author' => \n  array (\n    'use' => '1',\n    'name' => '笔名',\n    'width' => '120',\n    'func' => 'author',\n  ),\n  'updatetime' => \n  array (\n    'use' => '1',\n    'name' => '更新时间',\n    'width' => '160',\n    'func' => 'datetime',\n  ),\n)"}, {"name": "list_query", "value": "'f4286a0d30bb9f4bddc745ecf968a510'"}, {"name": "list_table", "value": "'dr_1_webnav'"}, {"name": "extend_param", "value": "array (\n)"}, {"name": "mytable", "value": "array (\n  'foot_tpl' => '<label class=\"table_select_all\"><input onclick=\"dr_table_select_all(this)\" type=\"checkbox\"><span></span></label><label><button type=\"button\" onclick=\"dr_module_delete()\" class=\"btn red btn-sm\"> <i class=\"fa fa-trash\"></i> 删除</button></label><label style=\"margin-right:5px\"><select class=\"bs-select form-control\" name=\"catid\">\n<option value=\\'0\\'>请选择</option>\n<option _selected_3_ value=\\'3\\'>AI视频工具</option>\n<option _selected_2_ value=\\'2\\'>AI生图工具</option>\n<option _selected_6_ value=\\'6\\'>AI音频工具</option>\n<option _selected_5_ value=\\'5\\'>AI视频文案</option>\n</select>\n</label>\n                <label><button type=\"button\" onclick=\"dr_ajax_option(\\'admin3faf81d65fd6.php?s=webnav&c=home&m=move_edit\\', \\'你确定要更改栏目吗？\\', 1)\" class=\"btn green btn-sm\"> <i class=\"fa fa-edit\"></i> 更改</button></label><label>\n                    <div class=\"btn-group dropdown\">\n                        <a class=\"btn  blue btn-sm dropdown-toggle\" data-toggle=\"dropdown\" data-hover=\"dropdown\" data-close-others=\"true\" aria-expanded=\"false\" href=\"javascript:;\"> 批量\n                            <i class=\"fa fa-angle-down\"></i>\n                        </a>\n                        <ul class=\"dropdown-menu\"><li>\n                                <a href=\"javascript:;\" onclick=\"dr_module_send(\\'推荐位\\', \\'admin3faf81d65fd6.php?s=webnav&c=home&m=tui_edit&page=0\\')\"> <i class=\"fa fa-flag\"></i> 推送到推荐位 </a>\n                            </li><li>\n                                <a href=\"javascript:;\" onclick=\"dr_module_send_ajax(\\'admin3faf81d65fd6.php?s=webnav&c=home&m=tui_edit&page=4\\')\"> <i class=\"fa fa-clock-o\"></i> 更新时间 </a>\n                            </li><li>\n                                <a href=\"javascript:;\" onclick=\"dr_module_tuigao()\"> <i class=\"fa fa-sign-out\"></i> 批量退稿 </a>\n                            </li>\n                           \n                        </ul>\n                    </div>\n                </label>',\n  'link_tpl' => '<label><a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=edit&id={id}\" class=\"btn btn-xs red\"> <i class=\"fa fa-edit\"></i> 修改</a></label><label><a class=\"btn blue btn-xs\" href=\"admin3faf81d65fd6.php?s=webnav&c=fankui&m=index&cid={cid}\"><i class=\"fa fa-table\"></i> 反馈（{fankui_total}）</a></label> <label><a class=\"btn yellow btn-xs\" href=\"javascript:dr_iframe_show(\\'\\', \\'admin3faf81d65fd6.php?s=mbdy&c=module&m=show&mid={mid}&id={cid}\\')\"><i class=\"fa fa-code\"></i> 前端调用</a></label>',\n  'link_var' => 'html = html.replace(/\\\\{id\\\\}/g, row.id);\n            html = html.replace(/\\\\{cid\\\\}/g, row.id);\n            html = html.replace(/\\\\{mid\\\\}/g, \"webnav\");html = html.replace(/\\\\{fankui_total\\\\}/g, row.fankui_total);',\n)"}, {"name": "mytable_name", "value": "'内容模块【网址（webnav）】'"}, {"name": "mytable_pagesize", "value": "1"}, {"name": "is_search", "value": "1"}, {"name": "is_show_export", "value": "true"}, {"name": "is_fixed_columns", "value": "NULL"}, {"name": "is_show_search_bar", "value": "1"}, {"name": "menu", "value": "'<li class=\"dropdown\"> <a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index\" class=\"on\"> <i class=\"fa fa-navicon\"></i>  网址管理</a> <a class=\"dropdown-toggle on\"  data-hover=\"dropdown\" data-close-others=\"true\" aria-expanded=\"true\"><i class=\"fa fa-angle-double-down\"></i></a><ul class=\"dropdown-menu\"><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index\"> <i class=\"fa fa-navicon\"></i> 网址管理 </a></li><li class=\"divider\"> </li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=flag&m=index&flag=1\"> <i class=\"fa fa-fire\"></i> 热门推荐 </a></li><li class=\"divider\"> </li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=fankui&m=index\"> <i class=\"fa fa-table\"></i> 反馈管理 </a></li><li class=\"divider\"> </li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=draft&m=index\"> <i class=\"fa fa-pencil\"></i> 草稿箱管理 </a></li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=recycle&m=index\"> <i class=\"fa fa-trash-o\"></i> 回收站管理 </a></li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=time&m=index\"> <i class=\"fa fa-clock-o\"></i> 待发布管理 </a></li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=verify&m=index\"> <i class=\"fa fa-edit\"></i> 待审核管理 </a></li><li class=\"divider\"> </li><li><a href=\"javascript:dr_iframe_show(\\'模块内容字段\\', \\'admin3faf81d65fd6.php?c=field&m=index&rname=module&rid=2&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-code\"></i> 模块内容字段</a> </li><li><a href=\"javascript:dr_iframe_show(\\'栏目模型字段\\', \\'admin3faf81d65fd6.php?c=field&m=index&rname=catmodule-webnav&rid=0&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-code\"></i> 栏目模型字段</a> </li><li><a href=\"javascript:dr_iframe_show(\\'划分栏目模型字段\\', \\'admin3faf81d65fd6.php?s=module&c=module_category&m=field_index&dir=webnav&rid=0&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-edit\"></i> 划分栏目模型字段</a> </li></ul> <i class=\"fa fa-circle\"></i> </li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=category&m=index\"> <i class=\"fa fa-reorder\"></i> 栏目管理</a> <i class=\"fa fa-circle\"></i> </li><li><a href=\"javascript:dr_iframe_show(\\'批量更新内容URL\\', \\'admin3faf81d65fd6.php?c=api&m=update_url&mid=webnav\\', \\'500px\\', \\'300px\\')\"\"> <i class=\"fa fa-refresh\"></i> 更新URL</a> <i class=\"fa fa-circle\"></i> </li><li><a href=\"javascript:dr_iframe_show(\\'模块配置\\', \\'admin3faf81d65fd6.php?s=module&c=module&m=edit&id=2\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-cog\"></i> 模块配置</a> <i class=\"fa fa-circle\"></i> </li><li> <a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid=0\" class=\"\"> <i class=\"fa fa-plus\"></i> 发布</a> <i class=\"fa fa-circle\"></i> </li>'"}, {"name": "category_move", "value": "'<select class=\"bs-select form-control\" name=\"catid\">\n<option value=\\'0\\'>请选择</option>\n<option _selected_3_ value=\\'3\\'>AI视频工具</option>\n<option _selected_2_ value=\\'2\\'>AI生图工具</option>\n<option _selected_6_ value=\\'6\\'>AI音频工具</option>\n<option _selected_5_ value=\\'5\\'>AI视频文案</option>\n</select>\n'"}, {"name": "category_select", "value": "'<select class=\"bs-select form-control\" name=\"catid\">\n<option value=\\'0\\'>全部</option>\n<option _selected_3_ value=\\'3\\'>AI视频工具</option>\n<option _selected_2_ value=\\'2\\'>AI生图工具</option>\n<option _selected_6_ value=\\'6\\'>AI音频工具</option>\n<option _selected_5_ value=\\'5\\'>AI视频文案</option>\n</select>\n<script type=\"text/javascript\"> var bs_selectAllText = \\'全选\\';var bs_deselectAllText = \\'全删\\';var bs_noneSelectedText = \\'没有选择\\'; var bs_noneResultsText = \\'没有找到 {0}\\';</script>\n<link href=\"/static/assets/global/plugins/bootstrap-select/css/bootstrap-select.css\" rel=\"stylesheet\" type=\"text/css\" />\n<script src=\"/static/assets/global/plugins/bootstrap-select/js/bootstrap-select.js\" type=\"text/javascript\"></script>\n<script type=\"text/javascript\"> jQuery(document).ready(function() { $(\\'.bs-select\\').selectpicker();  }); </script>'"}, {"name": "clink", "value": "array (\n  0 => \n  array (\n    'name' => '前端调用',\n    'icon' => 'fa fa-code',\n    'color' => 'yellow',\n    'url' => 'javascript:dr_iframe_show(\\'\\', \\'admin3faf81d65fd6.php?s=mbdy&c=module&m=show&mid={mid}&id={cid}\\')',\n    'uri' => 'mbdy/module/index',\n    'field' => '',\n    'model' => \n    \\Phpcmf\\Model\\Mbdy\\Auth::__set_state(array(\n       'db' => \n      \\CodeIgniter\\Database\\MySQLi\\Connection::__set_state(array(\n         'DSN' => '',\n         'port' => '',\n         'hostname' => 'localhost',\n         'username' => 'kh123',\n         'password' => 'kh123!@#Qq',\n         'database' => 'kh123',\n         'DBDriver' => 'MySQLi',\n         'subdriver' => NULL,\n         'DBPrefix' => 'dr_',\n         'pConnect' => false,\n         'DBDebug' => true,\n         'charset' => 'utf8mb4',\n         'DBCollat' => 'utf8mb4_general_ci',\n         'swapPre' => '',\n         'encrypt' => false,\n         'compress' => false,\n         'strictOn' => false,\n         'failover' => \n        array (\n        ),\n         'lastQuery' => \n        \\CodeIgniter\\Database\\Query::__set_state(array(\n           'originalQueryString' => 'SHOW COLUMNS FROM `dr_1_webnav`',\n           'finalQueryString' => 'SHOW COLUMNS FROM `dr_1_webnav`',\n           'binds' => \n          array (\n          ),\n           'bindMarker' => '?',\n           'startTime' => **********.910465,\n           'endTime' => **********.911799,\n           'errorCode' => NULL,\n           'errorString' => NULL,\n           'db' => NULL,\n        )),\n         'connID' => \n        \\mysqli::__set_state(array(\n        )),\n         'resultID' => \n        \\mysqli_result::__set_state(array(\n        )),\n         'protectIdentifiers' => true,\n         'reservedIdentifiers' => \n        array (\n          0 => '*',\n        ),\n         'escapeChar' => '`',\n         'likeEscapeStr' => ' ESCAPE \\'%s\\' ',\n         'likeEscapeChar' => '!',\n         'pregEscapeChar' => \n        array (\n          1 => '`',\n          0 => '`',\n          3 => '`',\n          2 => '`',\n        ),\n         'dataCache' => \n        array (\n          'table_names' => \n          array (\n            0 => 'dr_1_blog',\n            1 => 'dr_1_blog_category',\n            2 => 'dr_1_blog_category_data',\n            3 => 'dr_1_blog_data_0',\n            4 => 'dr_1_blog_draft',\n            5 => 'dr_1_blog_flag',\n            6 => 'dr_1_blog_hits',\n            7 => 'dr_1_blog_index',\n            8 => 'dr_1_blog_oppose',\n            9 => 'dr_1_blog_recycle',\n            10 => 'dr_1_blog_search',\n            11 => 'dr_1_blog_support',\n            12 => 'dr_1_blog_time',\n            13 => 'dr_1_blog_verify',\n            14 => 'dr_1_form',\n            15 => 'dr_1_form_yqlj',\n            16 => 'dr_1_form_yqlj_data_0',\n            17 => 'dr_1_navigator',\n            18 => 'dr_1_share_category',\n            19 => 'dr_1_share_index',\n            20 => 'dr_1_tag',\n            21 => 'dr_1_tag_blog',\n            22 => 'dr_1_tag_webnav',\n            23 => 'dr_1_webnav',\n            24 => 'dr_1_webnav_category',\n            25 => 'dr_1_webnav_category_data',\n            26 => 'dr_1_webnav_data_0',\n            27 => 'dr_1_webnav_draft',\n            28 => 'dr_1_webnav_flag',\n            29 => 'dr_1_webnav_form_fankui',\n            30 => 'dr_1_webnav_form_fankui_data_0',\n            31 => 'dr_1_webnav_hits',\n            32 => 'dr_1_webnav_index',\n            33 => 'dr_1_webnav_oppose',\n            34 => 'dr_1_webnav_recycle',\n            35 => 'dr_1_webnav_search',\n            36 => 'dr_1_webnav_support',\n            37 => 'dr_1_webnav_time',\n            38 => 'dr_1_webnav_verify',\n            39 => 'dr_admin',\n            40 => 'dr_admin_login',\n            41 => 'dr_admin_menu',\n            42 => 'dr_admin_min_menu',\n            43 => 'dr_admin_notice',\n            44 => 'dr_admin_role',\n            45 => 'dr_admin_role_index',\n            46 => 'dr_admin_setting',\n            47 => 'dr_app_dever_field',\n            48 => 'dr_app_login',\n            49 => 'dr_app_safe_mm',\n            50 => 'dr_attachment',\n            51 => 'dr_attachment_data',\n            52 => 'dr_attachment_remote',\n            53 => 'dr_attachment_unused',\n            54 => 'dr_cron',\n            55 => 'dr_diygg',\n            56 => 'dr_diygg_type',\n            57 => 'dr_field',\n            58 => 'dr_linkage',\n            59 => 'dr_linkage_data_1',\n            60 => 'dr_mail_smtp',\n            61 => 'dr_member',\n            62 => 'dr_member_data',\n            63 => 'dr_member_setting',\n            64 => 'dr_member_verify',\n            65 => 'dr_module',\n            66 => 'dr_module_form',\n            67 => 'dr_site',\n            68 => 'dr_system',\n            69 => 'dr_urlrule',\n          ),\n          'field_names' => \n          array (\n            'dr_1_webnav' => \n            array (\n              0 => 'id',\n              1 => 'catid',\n              2 => 'title',\n              3 => 'thumb',\n              4 => 'keywords',\n              5 => 'description',\n              6 => 'hits',\n              7 => 'uid',\n              8 => 'author',\n              9 => 'status',\n              10 => 'url',\n              11 => 'link_id',\n              12 => 'tableid',\n              13 => 'inputip',\n              14 => 'inputtime',\n              15 => 'updatetime',\n              16 => 'displayorder',\n              17 => 'tubiao',\n              18 => 'catids',\n              19 => 'website',\n              20 => 'support',\n              21 => 'oppose',\n              22 => 'fankui_total',\n            ),\n          ),\n        ),\n         'connectTime' => **********.868589,\n         'connectDuration' => 0.0012431144714355469,\n         'pretend' => false,\n         'transEnabled' => true,\n         'transStrict' => true,\n         'transDepth' => 0,\n         'transStatus' => true,\n         'transFailure' => false,\n         'transException' => false,\n         'aliasedTables' => \n        array (\n        ),\n         'queryClass' => 'CodeIgniter\\\\Database\\\\Query',\n         'deleteHack' => true,\n         'mysqli' => \n        \\mysqli::__set_state(array(\n        )),\n         'resultMode' => 0,\n         'numberNative' => false,\n      )),\n       'prefix' => 'dr_',\n       'id' => 'id',\n       'key' => 'id',\n       'site' => \n      array (\n        1 => 1,\n      ),\n       'field' => NULL,\n       'siteid' => 1,\n       'table' => NULL,\n       'mytable' => NULL,\n       'stable' => NULL,\n       'sfield' => NULL,\n       'db_temp' => NULL,\n       'uid' => 1,\n       'admin' => \n      array (\n        'id' => '1',\n        'uid' => 1,\n        'setting' => \n        array (\n          'admin_min' => 0,\n        ),\n        'usermenu' => \n        array (\n        ),\n        'history' => \n        array (\n        ),\n        'module' => \n        array (\n        ),\n        'site' => \n        array (\n          0 => 1,\n        ),\n        'roleid' => \n        array (\n          1 => '1',\n        ),\n        'role' => \n        array (\n          1 => '超级管理员',\n        ),\n        'system' => \n        array (\n          'uri' => \n          array (\n          ),\n          'mark' => \n          array (\n          ),\n        ),\n        'adminid' => 1,\n        'email' => '<EMAIL>',\n        'phone' => '',\n        'username' => 'admin',\n        'password' => '62e69b828c74c50a1f6e93227cdf7e77',\n      ),\n       'member' => \n      array (\n        'id' => '1',\n        'email' => '<EMAIL>',\n        'phone' => '',\n        'username' => 'admin',\n        'password' => '62e69b828c74c50a1f6e93227cdf7e77',\n        'login_attr' => '',\n        'salt' => '8d6dc35e50',\n        'name' => '创始人',\n        'money' => '1000000.00',\n        'freeze' => '0.00',\n        'spend' => '0.00',\n        'score' => '1000000',\n        'experience' => '1000000',\n        'regip' => '',\n        'regtime' => '1699804235',\n        'randcode' => '0',\n        'is_admin' => '1',\n        'is_lock' => '0',\n        'is_verify' => '1',\n        'is_mobile' => '1',\n        'is_email' => '0',\n        'is_avatar' => '0',\n        'is_complete' => '1',\n        'uid' => '1',\n        'avatar' => '/static/assets/images/avatar.png',\n        'adminid' => 1,\n        'group_name' => \n        array (\n        ),\n        'authid' => \n        array (\n        ),\n        'levelid' => \n        array (\n        ),\n        'groupid' => \n        array (\n        ),\n        'group' => \n        array (\n        ),\n        'group_timeout' => 0,\n      ),\n       'date_field' => NULL,\n       'param' => NULL,\n       'init' => NULL,\n       '_auth_uri' => '',\n       '_is_post_user' => -1,\n       '_is_admin_min_mode' => -1,\n       '_is_post_user_status' => -1,\n    )),\n    'displayorder' => 0,\n  ),\n)"}, {"name": "c<PERSON><PERSON>", "value": "array (\n  0 => \n  array (\n    'icon' => 'fa fa-flag',\n    'name' => '推送到推荐位',\n    'uri' => 'webnav/home/<USER>',\n    'url' => 'javascript:;\" onclick=\"dr_module_send(\\'推荐位\\', \\'admin3faf81d65fd6.php?s=webnav&c=home&m=tui_edit&page=0\\')',\n    'displayorder' => 0,\n  ),\n  1 => \n  array (\n    'icon' => 'fa fa-clock-o',\n    'name' => '更新时间',\n    'uri' => 'webnav/home/<USER>',\n    'url' => 'javascript:;\" onclick=\"dr_module_send_ajax(\\'admin3faf81d65fd6.php?s=webnav&c=home&m=tui_edit&page=4\\')',\n    'displayorder' => 1,\n  ),\n  2 => \n  array (\n    'icon' => 'fa fa-sign-out',\n    'name' => '批量退稿',\n    'uri' => 'webnav/home/<USER>',\n    'url' => 'javascript:;\" onclick=\"dr_module_tuigao()',\n    'displayorder' => 2,\n  ),\n)"}, {"name": "my_web_url", "value": "'/admin3faf81d65fd6.php?s=webnav&c=home&m=index'"}, {"name": "get", "value": "array (\n  's' => 'webnav',\n  'c' => 'home',\n  'm' => 'index',\n)"}], "tips": [{"name": "share_list.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/share_list.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/App/Module/Views/share_list.html]！"}, {"name": "header.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/header.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/header.html]"}, {"name": "head.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/head.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/head.html]"}, {"name": "api_list_date_search.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/api_list_date_search.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/api_list_date_search.html]"}, {"name": "mytable.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/mytable.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/mytable.html]"}, {"name": "footer.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/footer.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/footer.html]"}], "times": [{"tpl": 0.01}], "files": {"/home/<USER>/web/dayrui/App/Module/Views/share_list.html": {"name": "share_list.html", "path": "/home/<USER>/web/dayrui/App/Module/Views/share_list.html"}, "/home/<USER>/web/dayrui/Fcms/View/header.html": {"name": "header.html", "path": "/home/<USER>/web/dayrui/Fcms/View/header.html"}, "/home/<USER>/web/dayrui/Fcms/View/head.html": {"name": "head.html", "path": "/home/<USER>/web/dayrui/Fcms/View/head.html"}, "/home/<USER>/web/dayrui/Fcms/View/api_list_date_search.html": {"name": "api_list_date_search.html", "path": "/home/<USER>/web/dayrui/Fcms/View/api_list_date_search.html"}, "/home/<USER>/web/dayrui/Fcms/View/mytable.html": {"name": "mytable.html", "path": "/home/<USER>/web/dayrui/Fcms/View/mytable.html"}, "/home/<USER>/web/dayrui/Fcms/View/footer.html": {"name": "footer.html", "path": "/home/<USER>/web/dayrui/Fcms/View/footer.html"}}}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 205 )", "display": {"coreFiles": [], "userFiles": [{"path": "/home/<USER>/web/cache/config/domain_app.php", "name": "domain_app.php"}, {"path": "/home/<USER>/web/cache/config/domain_client.php", "name": "domain_client.php"}, {"path": "/home/<USER>/web/cache/config/site.php", "name": "site.php"}, {"path": "/home/<USER>/web/cache/config/system.php", "name": "system.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_App_DS_Module_DS_Views_DS_share_list.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_App_DS_Module_DS_Views_DS_share_list.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_api_list_date_search.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_api_list_date_search.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_head.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_head.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_header.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_header.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_mytable.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_mytable.html.cache.php"}, {"path": "/home/<USER>/web/dayrui/App/Form/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mbdy/Config/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/App/Mbdy/Config/Clink.php", "name": "Clink.php"}, {"path": "/home/<USER>/web/dayrui/App/Mbdy/Models/Auth.php", "name": "Auth.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Module_init.php", "name": "Module_init.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Run.php", "name": "Run.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php", "name": "Module.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Libraries/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Models/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php", "name": "Login.php"}, {"path": "/home/<USER>/web/dayrui/App/Tag/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Home.php", "name": "Home.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Autoload.php", "name": "Autoload.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Constants.php", "name": "Constants.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Feature.php", "name": "Feature.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Hook.php", "name": "Hook.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Common.php", "name": "Common.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseService.php", "name": "BaseService.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factories.php", "name": "Factories.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factory.php", "name": "Factory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Query.php", "name": "Query.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Timer.php", "name": "Timer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Events/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Header.php", "name": "Header.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Message.php", "name": "Message.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Response.php", "name": "Response.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/URI.php", "name": "URI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/Time.php", "name": "Time.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Log/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Modules/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouter.php", "name": "AutoRouter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Security/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Security/SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Superglobals.php", "name": "Superglobals.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Escaper/Escaper.php", "name": "Escaper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LogLevel.php", "name": "LogLevel.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Config/Apage.php", "name": "Apage.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Helper.php", "name": "Helper.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php", "name": "Phpcmf.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Service.php", "name": "Service.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Table.php", "name": "Table.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Catids.php", "name": "Catids.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Date.php", "name": "Date.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/File.php", "name": "File.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Select.php", "name": "Select.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Text.php", "name": "Text.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Textarea.php", "name": "Textarea.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Textbtn.php", "name": "Textbtn.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Touchspin.php", "name": "Touchspin.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Uid.php", "name": "Uid.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Field.php", "name": "Field.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Input.php", "name": "Input.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Lang.php", "name": "Lang.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Page.php", "name": "Page.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Tree.php", "name": "Tree.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php", "name": "Auth.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Member.php", "name": "Member.php"}, {"path": "/home/<USER>/web/dayrui/My/Config/License.php", "name": "License.php"}, {"path": "/home/<USER>/web/dayrui/My/Config/Version.php", "name": "Version.php"}, {"path": "/home/<USER>/web/public/admin3faf81d65fd6.php", "name": "admin3faf81d65fd6.php"}, {"path": "/home/<USER>/web/public/api/language/zh-cn/lang.php", "name": "lang.php"}, {"path": "/home/<USER>/web/public/config/custom.php", "name": "custom.php"}, {"path": "/home/<USER>/web/public/config/database.php", "name": "database.php"}, {"path": "/home/<USER>/web/public/config/hooks.php", "name": "hooks.php"}, {"path": "/home/<USER>/web/public/index.php", "name": "index.php"}]}, "badgeValue": 205, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"uri": "webnav/home/<USER>", "url": "/admin3faf81d65fd6.php?s=webnav&c=home&m=index", "app": "webnav", "controller": "home", "method": "index", "file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Home.php"}], "get": {"s": "webnav", "c": "home", "m": "index"}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "12.67", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.68", "count": 9}}}, "badgeValue": 10, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.797209, "duration": 0.012670040130615234}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.872796, "duration": 0.0002658367156982422}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.87627, "duration": 7.200241088867188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.886978, "duration": 9.703636169433594e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.887541, "duration": 2.288818359375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.888828, "duration": 3.0994415283203125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.889409, "duration": 2.5987625122070312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.901073, "duration": 9.894371032714844e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.901779, "duration": 2.7179718017578125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.911806, "duration": 3.4809112548828125e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1750910995</pre>", "uid": "1", "_ci_previous_url": "https://www.kuhao123.com/admin3faf81d65fd6.php?s=webnav&amp;c=category&amp;m=index", "auth_csrf_token": "c4b7ab6cf996162298be3d0173150d38"}, "get": {"s": "webnav", "c": "home", "m": "index"}, "headers": {"Cookie": "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJtbFh0amEzdWZFVnZpZ2JMdEZsZEE9PSIsInZhbHVlIjoiN0tlRlFRY1YyUFJQVnJDSStPVldLc1NPMldxblJYbW1PbnlZLzBMSlhXOUNnUkxCN0tzZnhYbTExZnBabmJZRTcva0lyazFzUUozT1NZMG96N2Z5RnUzZE5CM1lXQml2a2V5UTdoSUNuVU9nbXgyeGlSVDd4anBxd3R3R2hFODBEQmx4bUN4YXYxWHdLWXJGUGljMGVIb0dRam85Zk16Mk1xUytBMW0vcUpxeFQ2SHM0VVZvQjgybUlJTFpyTU5KRFdraWdjRG1pZzZ2eDZwSGs5ZWRQbVEwbGRYTWVab3pSY3Qyb043bkdkUT0iLCJtYWMiOiJhNjM0ZDIzYmYxZmJmOTM4YTA5ZDU5YzZlMWI0ZGFmNjQ2OTM1NjA3YmZmYmNiNTRhZDRmZjk2YTlmNWEyMmI5IiwidGFnIjoiIn0%3D; PHPSESSID=kjb18ivsdqceau93qdhn34vd0d; d41d8cd98f00b204e9800998ecf8427e_member_uid=1; d41d8cd98f00b204e9800998ecf8427e_member_cookie=5424b2695385ffdccec8d972124ec65f; csrf_cookie_name=cd8376769004a149d672615f02225890; ci_session=bfsq0jfq4aau88p4hrnbre22m219kqkh; d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-638=638", "Priority": "u=0, i", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8", "Accept-Encoding": "gzip, deflate, br, zstd", "Referer": "https://www.kuhao123.com/admin3faf81d65fd6.php?time=1749532652", "Sec-Fetch-Dest": "iframe", "Sec-Fetch-User": "?1", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "same-origin", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Upgrade-Insecure-Requests": "1", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Host": "www.kuhao123.com"}, "cookies": {"remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6ImJtbFh0amEzdWZFVnZpZ2JMdEZsZEE9PSIsInZhbHVlIjoiN0tlRlFRY1YyUFJQVnJDSStPVldLc1NPMldxblJYbW1PbnlZLzBMSlhXOUNnUkxCN0tzZnhYbTExZnBabmJZRTcva0lyazFzUUozT1NZMG96N2Z5RnUzZE5CM1lXQml2a2V5UTdoSUNuVU9nbXgyeGlSVDd4anBxd3R3R2hFODBEQmx4bUN4YXYxWHdLWXJGUGljMGVIb0dRam85Zk16Mk1xUytBMW0vcUpxeFQ2SHM0VVZvQjgybUlJTFpyTU5KRFdraWdjRG1pZzZ2eDZwSGs5ZWRQbVEwbGRYTWVab3pSY3Qyb043bkdkUT0iLCJtYWMiOiJhNjM0ZDIzYmYxZmJmOTM4YTA5ZDU5YzZlMWI0ZGFmNjQ2OTM1NjA3YmZmYmNiNTRhZDRmZjk2YTlmNWEyMmI5IiwidGFnIjoiIn0=", "PHPSESSID": "kjb18ivsdqceau93qdhn34vd0d", "d41d8cd98f00b204e9800998ecf8427e_member_uid": "1", "d41d8cd98f00b204e9800998ecf8427e_member_cookie": "5424b2695385ffdccec8d972124ec65f", "csrf_cookie_name": "cd8376769004a149d672615f02225890", "ci_session": "bfsq0jfq4aau88p4hrnbre22m219kqkh", "d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-638": "638"}, "request": "HTTPS/2.0", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.7", "phpVersion": "8.2.28", "phpSAPI": "fpm-fcgi", "environment": "development", "baseURL": "https://www.kuhao123.com/", "timezone": "PRC", "locale": "zh-cn", "cspEnabled": false}}