{"url": "https://www.kuhao123.com/admin3faf81d65fd6.php", "method": "GET", "isAJAX": false, "startTime": **********.808333, "totalTime": 92.**************, "totalMemory": "10.627", "segmentDuration": 15, "segmentCount": 7, "CI_VERSION": "4.4.7", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.822962, "duration": 0.011422872543334961}, {"name": "Routing", "component": "Timer", "start": **********.834388, "duration": 3.910064697265625e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.835242, "duration": 1.7881393432617188e-05}, {"name": "Controller", "component": "Timer", "start": **********.835264, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.835264, "duration": 0.*****************}, {"name": "After Filters", "component": "Timer", "start": **********.900972, "duration": 0.00039005279541015625}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(7 total Queries, 7 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.66 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Member.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "d909bf4bc5b731f00bce4658ce9deb8d"}, {"hover": "", "class": "", "duration": "0.52 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Member.php:221", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "5b1f90daa74e45e4aedd0c56b7414d2e"}, {"hover": "", "class": "", "duration": "0.49 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:328", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:593", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "f858f89ae8f1376b9568667a8f46aed8"}, {"hover": "", "class": "", "duration": "0.43 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:103", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:340", "function": "        Phpcmf\\Model\\Auth->_role()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:593", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 13    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 14    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "a43b82147b1e0689cb92928da7b0c7a0"}, {"hover": "", "class": "", "duration": "0.77 ms", "sql": "SHOW TABLES <strong>FROM</strong> `kh123`", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1410", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1429", "function": "        CodeIgniter\\Database\\BaseConnection->listTables()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php:12", "function": "        CodeIgniter\\Database\\BaseConnection->tableExists()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php:313", "function": "        call_user_func()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 15    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 16    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 17    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1410", "qid": "64289110944cbe079e30f641629c76b6"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:615", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php:29", "function": "        Phpcmf\\Model->getRow()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php:313", "function": "        call_user_func()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:16", "function": "        Phpcmf\\Table->__construct()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Module->__construct()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 15    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 16    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 17    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "d285059210adfb1d248cdb4d6990b9c9"}, {"hover": "", "class": "", "duration": "0.52 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_draft`\n<strong>WHERE</strong> `uid` = 1\n<strong>AND</strong> cid=0\n<strong>ORDER</strong> <strong>BY</strong> `inputtime` desc\n <strong>LIMIT</strong> 10", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:559", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Models/Content.php:824", "function": "        Phpcmf\\Model->getAll()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php:336", "function": "        Phpcmf\\Model\\Module\\Content->get_draft_list()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Home.php:15", "function": "        Phpcmf\\Admin\\Module->_Admin_Add()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Home->add()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "1d8bdbf524a0cc33231f708a30758142"}]}, "badgeValue": 7, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.85171, "duration": "0.000776"}, {"name": "Query", "component": "Database", "start": **********.852906, "duration": "0.000656", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.85452, "duration": "0.000524", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.865204, "duration": "0.000487", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.865805, "duration": "0.000431", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.866764, "duration": "0.000771", "query": "SHOW TABLES <strong>FROM</strong> `kh123`"}, {"name": "Query", "component": "Database", "start": **********.867693, "duration": "0.000477", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.893398, "duration": "0.000516", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_draft`\n<strong>WHERE</strong> `uid` = 1\n<strong>AND</strong> cid=0\n<strong>ORDER</strong> <strong>BY</strong> `inputtime` desc\n <strong>LIMIT</strong> 10"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": {"vars": [{"name": "is_ajax", "value": "''"}, {"name": "is_mobile", "value": "0"}, {"name": "field", "value": "array (\n  'catids' => \n  array (\n    'id' => '17',\n    'name' => '副栏目',\n    'fieldname' => 'catids',\n    'fieldtype' => 'Catids',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'option' => \n      array (\n        'width' => '',\n        'css' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '-10',\n  ),\n  'title' => \n  array (\n    'id' => '9',\n    'name' => '主题',\n    'fieldname' => 'title',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '1',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => 400,\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n      ),\n      'validate' => \n      array (\n        'xss' => 1,\n        'required' => 1,\n        'formattr' => 'onblur=\"check_title();get_keywords(\\'keywords\\');\"',\n      ),\n    ),\n    'displayorder' => '-5',\n  ),\n  'tubiao' => \n  array (\n    'id' => '16',\n    'name' => '图标',\n    'fieldname' => 'tubiao',\n    'fieldtype' => 'File',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '300',\n        'input' => '1',\n        'ext' => 'jpg,gif,png,jpeg',\n        'size' => '1',\n        'chunk' => '1',\n        'attachment' => '0',\n        'image_reduce' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '-2',\n  ),\n  'thumb' => \n  array (\n    'id' => '10',\n    'name' => '缩略图',\n    'fieldname' => 'thumb',\n    'fieldtype' => 'File',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'ext' => 'jpg,gif,png,jpeg',\n        'size' => '1',\n        'attachment' => '0',\n        'image_reduce' => '',\n        'is_ext_tips' => '0',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '0',\n  ),\n  'keywords' => \n  array (\n    'id' => '11',\n    'name' => '标签',\n    'fieldname' => 'keywords',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'value' => '',\n        'width' => '400',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => ' data-role=\"tagsinput\"',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '0',\n  ),\n  'description' => \n  array (\n    'id' => '12',\n    'name' => '描述',\n    'fieldname' => 'description',\n    'fieldtype' => 'Textarea',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '1',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => 500,\n        'height' => 60,\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n      ),\n      'validate' => \n      array (\n        'xss' => 1,\n        'filter' => 'dr_filter_description',\n      ),\n    ),\n    'displayorder' => '0',\n  ),\n  'author' => \n  array (\n    'id' => '13',\n    'name' => '笔名',\n    'fieldname' => 'author',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '1',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'is_right' => 1,\n      'option' => \n      array (\n        'width' => 200,\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '255',\n        'value' => '{name}',\n      ),\n      'validate' => \n      array (\n        'xss' => 1,\n      ),\n    ),\n    'displayorder' => '0',\n  ),\n  'content' => \n  array (\n    'id' => '14',\n    'name' => '内容',\n    'fieldname' => 'content',\n    'fieldtype' => 'Editor',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '0',\n    'issystem' => '1',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'show_bottom_boot' => '1',\n        'tool_select_3' => '1',\n        'imgtitle' => '0',\n        'imgalt' => '0',\n        'watermark' => '0',\n        'image_ext' => 'jpg,gif,png,webp,jpeg',\n        'image_size' => '2',\n        'attach_size' => '200',\n        'attach_ext' => 'zip,rar,txt,doc',\n        'video_ext' => 'mp4',\n        'video_size' => '500',\n        'attachment' => '0',\n        'value' => '',\n        'width' => '100%',\n        'height' => '400',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '1',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '0',\n  ),\n  'website' => \n  array (\n    'id' => '26',\n    'name' => '官网',\n    'fieldname' => 'website',\n    'fieldtype' => 'Text',\n    'relatedid' => '2',\n    'relatedname' => 'module',\n    'isedit' => '1',\n    'ismain' => '1',\n    'issystem' => '0',\n    'ismember' => '1',\n    'issearch' => '0',\n    'disabled' => '0',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'fieldtype' => 'VARCHAR',\n        'fieldlength' => '300',\n        'value' => '',\n        'width' => '100%',\n        'css' => '',\n      ),\n      'validate' => \n      array (\n        'xss' => '1',\n        'required' => '0',\n        'pattern' => '',\n        'errortips' => '',\n        'check' => '',\n        'filter' => '',\n        'tips' => '',\n        'formattr' => '',\n      ),\n      'is_right' => '0',\n    ),\n    'displayorder' => '0',\n  ),\n  'inputtime' => \n  array (\n    'name' => '录入时间',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Date',\n    'fieldname' => 'inputtime',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'value' => 'SYS_TIME',\n        'is_left' => 1,\n      ),\n      'validate' => \n      array (\n        'required' => 1,\n      ),\n    ),\n  ),\n  'updatetime' => \n  array (\n    'name' => '更新时间',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Date',\n    'fieldname' => 'updatetime',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'value' => 'SYS_TIME',\n        'is_left' => 1,\n      ),\n      'validate' => \n      array (\n        'required' => 1,\n      ),\n    ),\n  ),\n  'inputip' => \n  array (\n    'name' => '客户端IP',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Textbtn',\n    'fieldname' => 'inputip',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'name' => '查看',\n        'icon' => 'fa fa-arrow-right',\n        'func' => 'dr_show_ip',\n        'value' => '**************-20587',\n      ),\n    ),\n  ),\n  'displayorder' => \n  array (\n    'name' => '排列值',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Touchspin',\n    'fieldname' => 'displayorder',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'max' => '',\n        'min' => '0',\n        'step' => '1',\n        'show' => '1',\n        'value' => 0,\n      ),\n    ),\n  ),\n  'hits' => \n  array (\n    'name' => '浏览数',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Touchspin',\n    'fieldname' => 'hits',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '100%',\n        'max' => '9999999',\n        'min' => '1',\n        'step' => '1',\n        'show' => '1',\n        'value' => 1,\n      ),\n    ),\n  ),\n  'uid' => \n  array (\n    'name' => '账号',\n    'ismain' => 1,\n    'ismember' => 1,\n    'fieldtype' => 'Uid',\n    'fieldname' => 'uid',\n    'setting' => \n    array (\n      'option' => \n      array (\n        'width' => '200px',\n      ),\n      'validate' => \n      array (\n        'check' => '_check_member',\n      ),\n    ),\n  ),\n)"}, {"name": "module", "value": "array (\n  'id' => '2',\n  'name' => '网址',\n  'icon' => 'fa fa-navicon',\n  'site' => \n  array (\n    1 => \n    array (\n      'html' => '0',\n      'theme' => 'default',\n      'domain' => '',\n      'template' => 'default',\n      'urlrule' => '4',\n      'is_cat' => '0',\n      'show_title' => '[第{page}页{join}]{title}{join}{catpname}{join}{SITE_NAME}',\n      'show_keywords' => '',\n      'show_description' => '',\n      'list_title' => '[第{page}页{join}]{catpname}{join}{SITE_NAME}',\n      'list_keywords' => '',\n      'list_description' => '',\n      'search_title' => '[第{page}页{join}][{keyword}{join}][{param}{join}]{SITE_NAME}',\n      'search_keywords' => '',\n      'search_description' => '',\n      'module_title' => '',\n      'module_keywords' => '',\n      'module_description' => '',\n    ),\n  ),\n  'config' => \n  array (\n    'type' => 'module',\n    'name' => '网址',\n    'icon' => 'fa fa-navicon',\n    'system' => '1',\n  ),\n  'share' => '0',\n  'setting' => \n  array (\n    'module_index_html' => 1,\n    'sync_category' => '0',\n    'pcatpost' => '0',\n    'updatetime_select' => '0',\n    'merge' => '0',\n    'right_field' => '0',\n    'desc_auto' => '0',\n    'desc_limit' => '100',\n    'kws_limit' => '10',\n    'desc_clear' => '0',\n    'hits_min' => '',\n    'hits_max' => '',\n    'verify_num' => '10',\n    'verify_msg' => '',\n    'delete_msg' => '',\n    'is_hide_search_bar' => '0',\n    'order' => 'updatetime DESC',\n    'search_time' => 'updatetime',\n    'search_first_field' => 'title',\n    'is_op_more' => '0',\n    'list_field' => \n    array (\n      'id' => \n      array (\n        'use' => '1',\n        'name' => 'Id',\n        'width' => '80',\n        'func' => '',\n      ),\n      'title' => \n      array (\n        'use' => '1',\n        'name' => '主题',\n        'width' => '',\n        'func' => 'title',\n      ),\n      'catid' => \n      array (\n        'use' => '1',\n        'name' => '栏目',\n        'width' => '130',\n        'func' => 'catid',\n      ),\n      'catids' => \n      array (\n        'use' => '1',\n        'name' => '副栏目',\n        'width' => '',\n        'func' => 'catids',\n      ),\n      'keywords' => \n      array (\n        'use' => '1',\n        'name' => '标签',\n        'width' => '',\n        'func' => '',\n      ),\n      'author' => \n      array (\n        'use' => '1',\n        'name' => '笔名',\n        'width' => '120',\n        'func' => 'author',\n      ),\n      'updatetime' => \n      array (\n        'use' => '1',\n        'name' => '更新时间',\n        'width' => '160',\n        'func' => 'datetime',\n      ),\n    ),\n    'flag' => \n    array (\n      1 => \n      array (\n        'name' => '热门推荐',\n        'icon' => 'fa fa-fire',\n        'role' => \n        array (\n        ),\n      ),\n    ),\n    'param' => NULL,\n    'search' => \n    array (\n      'use' => '1',\n      'catsync' => '0',\n      'indexsync' => '0',\n      'show_seo' => '0',\n      'search_404' => '0',\n      'search_param' => '0',\n      'complete' => '0',\n      'is_like' => '0',\n      'is_double_like' => '0',\n      'max' => '0',\n      'length' => '1',\n      'maxlength' => '0',\n      'param_join' => '-',\n      'param_rule' => '0',\n      'param_field' => '',\n      'param_join_field' => \n      array (\n        0 => '',\n        1 => '',\n        2 => '',\n        3 => '',\n        4 => '',\n        5 => '',\n        6 => '',\n        7 => '',\n        8 => '',\n        9 => '',\n        10 => '',\n      ),\n      'param_join_default_value' => '0',\n      'tpl_field' => '',\n      'field' => 'title,keywords',\n    ),\n  ),\n  'dirname' => 'webnav',\n  'domain' => '',\n  'mobile_domain' => '',\n  'cname' => '网址',\n  'html' => '0',\n  'title' => '网址',\n  'urlrule' => '4',\n  'url' => '/webnav.html',\n  'murl' => 'https://www.kuhao123.com/webnav.html',\n  'field' => \n  array (\n    'catids' => \n    array (\n      'id' => '17',\n      'name' => '副栏目',\n      'fieldname' => 'catids',\n      'fieldtype' => 'Catids',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'option' => \n        array (\n          'width' => '',\n          'css' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '-10',\n    ),\n    'title' => \n    array (\n      'id' => '9',\n      'name' => '主题',\n      'fieldname' => 'title',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'width' => 400,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n          'required' => 1,\n          'formattr' => 'onblur=\"check_title();get_keywords(\\'keywords\\');\"',\n        ),\n      ),\n      'displayorder' => '-5',\n    ),\n    'tubiao' => \n    array (\n      'id' => '16',\n      'name' => '图标',\n      'fieldname' => 'tubiao',\n      'fieldtype' => 'File',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '300',\n          'input' => '1',\n          'ext' => 'jpg,gif,png,jpeg',\n          'size' => '1',\n          'chunk' => '1',\n          'attachment' => '0',\n          'image_reduce' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '-2',\n    ),\n    'thumb' => \n    array (\n      'id' => '10',\n      'name' => '缩略图',\n      'fieldname' => 'thumb',\n      'fieldtype' => 'File',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'ext' => 'jpg,gif,png,jpeg',\n          'size' => '1',\n          'attachment' => '0',\n          'image_reduce' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'keywords' => \n    array (\n      'id' => '11',\n      'name' => '标签',\n      'fieldname' => 'keywords',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'value' => '',\n          'width' => '400',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => ' data-role=\"tagsinput\"',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'description' => \n    array (\n      'id' => '12',\n      'name' => '描述',\n      'fieldname' => 'description',\n      'fieldtype' => 'Textarea',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'width' => 500,\n          'height' => 60,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n          'filter' => 'dr_filter_description',\n        ),\n      ),\n      'displayorder' => '0',\n    ),\n    'author' => \n    array (\n      'id' => '13',\n      'name' => '笔名',\n      'fieldname' => 'author',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'is_right' => 1,\n        'option' => \n        array (\n          'width' => 200,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'value' => '{name}',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n        ),\n      ),\n      'displayorder' => '0',\n    ),\n    'content' => \n    array (\n      'id' => '14',\n      'name' => '内容',\n      'fieldname' => 'content',\n      'fieldtype' => 'Editor',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '0',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'show_bottom_boot' => '1',\n          'tool_select_3' => '1',\n          'imgtitle' => '0',\n          'imgalt' => '0',\n          'watermark' => '0',\n          'image_ext' => 'jpg,gif,png,webp,jpeg',\n          'image_size' => '2',\n          'attach_size' => '200',\n          'attach_ext' => 'zip,rar,txt,doc',\n          'video_ext' => 'mp4',\n          'video_size' => '500',\n          'attachment' => '0',\n          'value' => '',\n          'width' => '100%',\n          'height' => '400',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '1',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'website' => \n    array (\n      'id' => '26',\n      'name' => '官网',\n      'fieldname' => 'website',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '300',\n          'value' => '',\n          'width' => '100%',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n  ),\n  'system' => '1',\n  'category_data_field' => \n  array (\n  ),\n  'category' => \n  array (\n    0 => 'webnav',\n  ),\n  'form' => \n  array (\n    'fankui' => \n    array (\n      'id' => '2',\n      'name' => '反馈',\n      'table' => 'fankui',\n      'module' => 'webnav',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'seo' => \n        array (\n          'title' => '{title}{join}{formname}{join}{SITE_NAME}',\n          'keywords' => '',\n          'description' => '',\n        ),\n        'icon' => '',\n        'is_read' => '0',\n        'is_close_post' => '0',\n        'is_post_code' => '1',\n        'is_verify' => '0',\n        'rt_text' => '',\n        'rt_text2' => '',\n        'rt_url' => '',\n        'order' => 'inputtime DESC',\n        'search_time' => 'inputtime',\n        'list_field' => \n        array (\n          'title' => \n          array (\n            'use' => '1',\n            'name' => '主题',\n            'width' => '0',\n            'func' => 'title',\n          ),\n          'uid' => \n          array (\n            'use' => '1',\n            'name' => '账号',\n            'width' => '100',\n            'func' => 'uid',\n          ),\n          'inputtime' => \n          array (\n            'use' => '1',\n            'name' => '录入时间',\n            'width' => '160',\n            'func' => 'datetime',\n          ),\n        ),\n      ),\n      'field' => \n      array (\n        'title' => \n        array (\n          'id' => '39',\n          'name' => '主题',\n          'fieldname' => 'title',\n          'fieldtype' => 'Text',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '1',\n          'ismember' => '1',\n          'issearch' => '1',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'option' => \n            array (\n              'width' => 300,\n              'fieldtype' => 'VARCHAR',\n              'fieldlength' => '255',\n            ),\n            'validate' => \n            array (\n              'xss' => 1,\n              'required' => 1,\n            ),\n          ),\n          'displayorder' => '0',\n        ),\n        'author' => \n        array (\n          'id' => '40',\n          'name' => '作者',\n          'fieldname' => 'author',\n          'fieldtype' => 'Text',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '1',\n          'ismember' => '1',\n          'issearch' => '1',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'is_right' => 1,\n            'option' => \n            array (\n              'width' => 200,\n              'fieldtype' => 'VARCHAR',\n              'fieldlength' => '255',\n            ),\n            'validate' => \n            array (\n              'xss' => 1,\n            ),\n          ),\n          'displayorder' => '0',\n        ),\n        'fknr' => \n        array (\n          'id' => '41',\n          'name' => '反馈内容',\n          'fieldname' => 'fknr',\n          'fieldtype' => 'Textarea',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '0',\n          'ismember' => '1',\n          'issearch' => '0',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'option' => \n            array (\n              'value' => '',\n              'fieldtype' => 'TEXT',\n              'fieldlength' => '',\n              'width' => '',\n              'height' => '',\n              'css' => '',\n            ),\n            'validate' => \n            array (\n              'xss' => '1',\n              'required' => '0',\n              'pattern' => '',\n              'errortips' => '',\n              'check' => '',\n              'filter' => '',\n              'tips' => '',\n              'formattr' => '',\n            ),\n            'is_right' => '0',\n          ),\n          'displayorder' => '0',\n        ),\n      ),\n    ),\n  ),\n  'mid' => 'webnav',\n  'comment' => 0,\n)"}, {"name": "post_url", "value": "'admin3faf81d65fd6.php?s=webnav&c=home&m=add'"}, {"name": "is_post_user", "value": "0"}, {"name": "is_hcategory", "value": "false"}, {"name": "is_right_field", "value": "1"}, {"name": "is_category_show", "value": "1"}, {"name": "catid", "value": "0"}, {"name": "myfield", "value": "'<div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_catids\">\n    <label class=\"control-label col-md-2\">副栏目</label>\n    <div class=\"col-md-10\"><label style=\"min-width: 200px\"><select class=\"bs-select form-control\"  name=\\'data[catids][]\\'  multiple=\"multiple\" data-actions-box=\"true\">\n<option _selected_3_ value=\\'3\\'>AI视频工具</option>\n<option _selected_2_ value=\\'2\\'>AI生图工具</option>\n<option _selected_6_ value=\\'6\\'>AI音频工具</option>\n<option _selected_5_ value=\\'5\\'>AI视频文案</option>\n</select>\n<span class=\"help-block\"></span></label></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_title\">\n    <label class=\"control-label col-md-2\"><span class=\"required\" aria-required=\"true\"> * </span>主题</label>\n    <div class=\"col-md-10\"><input class=\"form-control dr_required \" type=\"text\" name=\"data[title]\" id=\"dr_title\" value=\"\" style=\"width:400px;\"  required=\"required\" onblur=\"check_title();get_keywords(\\'keywords\\');\" /><span class=\"help-block\" id=\"dr_title_tips\"></span></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_tubiao\">\n    <label class=\"control-label col-md-2\">图标</label>\n    <div class=\"col-md-10\">\r\n\t\t\t<div class=\"row fileupload-buttonbar\" id=\"fileupload_tubiao\">\r\n\t\t\t\t<div class=\"col-lg-12\">\r\n\t\t\t\t\t<button class=\"btn blue btn-sm fileinput-button\">\r\n\t\t\t\t\t\t<i class=\"fa fa-plus\"></i>\r\n\t\t\t\t\t\t<span> 上传 </span>\r\n\t\t\t\t\t\t<input type=\"file\" name=\"file_data\"> \r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<button type=\"button\" class=\"btn red btn-sm fileinput-unused\">\r\n\t\t\t\t\t\t<i class=\"fa fa-folder-open\"></i>\r\n\t\t\t\t\t\t<span> 浏览 </span>\r\n\t\t\t\t\t</button><button style=\"margin-left: 5px;\" type=\"button\" class=\"btn green btn-sm fileinput-url\">\r\n\t\t\t\t\t\t<i class=\"fa fa-edit\"></i>\r\n\t\t\t\t\t\t<span> 地址 </span>\r\n\t\t\t\t\t</button><button onclick=\"fileupload_file_remove(\\'tubiao\\')\" style=\"margin-left: 5px;display:none\" type=\"button\" class=\"btn red btn-sm fileinput-delete\">\r\n\t\t\t\t\t\t<i class=\"fa fa-trash\"></i>\r\n\t\t\t\t\t\t<span> 删除 </span>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<span class=\"fileupload-process\"> </span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"col-lg-12 fileupload-progress fade\" style=\"display:none\">\r\n\t\t\t\t\t<div class=\"progress progress-striped active\" role=\"progressbar\" aria-valuemin=\"0\" aria-valuemax=\"100\">\r\n\t\t\t\t\t\t<div class=\"progress-bar progress-bar-success\" style=\"width:0%;\"> </div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div><p class=\"finecms-file-ts\">上传格式要求：jpg、gif、png、jpeg（1MB）</p><div id=\"fileupload_tubiao_files\" class=\"files\"><input type=\"hidden\"  id=\"dr_tubiao\" name=\"data[tubiao]\" value=\"\" /></div>\r\n\t\t\t<link href=\"/static/assets/global/plugins/jquery-fileupload/css/jquery.fileupload.css?v=1750414398\" rel=\"stylesheet\" type=\"text/css\" />\r\n\t\t\t<script src=\"/static/assets/global/plugins/jquery-fileupload/js/jquery.fileupload.min.js?v=1750414398\" type=\"text/javascript\"></script>\r\n\t\t\t<script type=\"text/javascript\">\r\n        $(function() {\r\n            fileupload_file_init({\"name\":\"tubiao\",\"ext\":\" \\\\/(\\\\\\\\.|\\\\\\\\\\\\/)(jpg|gif|png|jpeg)$\\\\/i\",\"size\":1048576,\"url\":\"\\\\/admin3faf81d65fd6.php?c=api&is_iframe=1&token=c4b7ab6cf996162298be3d0173150d38&siteid=1&m=upload&p=e547f081fca6d88971a6f6dfafd77f2d&fid=16\",\"unused_url\":\"\\\\/admin3faf81d65fd6.php?c=api&m=input_file_list&is_iframe=1&p=e547f081fca6d88971a6f6dfafd77f2d&fid=16\",\"input_url\":\"\\\\/admin3faf81d65fd6.php?c=api&m=input_file_url&is_iframe=1&token=c4b7ab6cf996162298be3d0173150d38&siteid=1&p=e547f081fca6d88971a6f6dfafd77f2d&fid=16&file=&one=1\",\"tpl\":\"<div id=\\\\\"dr_tubiao_files_row\\\\\" class=\\\\\"file_row_html files_row\\\\\"><div class=\\\\\"files_row_preview preview\\\\\">{preview}<\\\\/div><input type=\\\\\"hidden\\\\\"  id=\\\\\"dr_tubiao\\\\\" class=\\\\\"files_row_id\\\\\" name=\\\\\"data[tubiao]\\\\\" value=\\\\\"{id}\\\\\" \\\\/><\\\\/div>\",\"area\":[\"80%\",\"80%\"],\"url_area\":[\"50%\",\"300px\"],\"chunk\":20971520});\r\n        });\r\n        </script></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_thumb\">\n    <label class=\"control-label col-md-2\">缩略图</label>\n    <div class=\"col-md-10\">\r\n\t\t\t<div class=\"row fileupload-buttonbar\" id=\"fileupload_thumb\">\r\n\t\t\t\t<div class=\"col-lg-12\">\r\n\t\t\t\t\t<button class=\"btn blue btn-sm fileinput-button\">\r\n\t\t\t\t\t\t<i class=\"fa fa-plus\"></i>\r\n\t\t\t\t\t\t<span> 上传 </span>\r\n\t\t\t\t\t\t<input type=\"file\" name=\"file_data\"> \r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<button type=\"button\" class=\"btn red btn-sm fileinput-unused\">\r\n\t\t\t\t\t\t<i class=\"fa fa-folder-open\"></i>\r\n\t\t\t\t\t\t<span> 浏览 </span>\r\n\t\t\t\t\t</button><button onclick=\"fileupload_file_remove(\\'thumb\\')\" style=\"margin-left: 5px;display:none\" type=\"button\" class=\"btn red btn-sm fileinput-delete\">\r\n\t\t\t\t\t\t<i class=\"fa fa-trash\"></i>\r\n\t\t\t\t\t\t<span> 删除 </span>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<span class=\"fileupload-process\"> </span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"col-lg-12 fileupload-progress fade\" style=\"display:none\">\r\n\t\t\t\t\t<div class=\"progress progress-striped active\" role=\"progressbar\" aria-valuemin=\"0\" aria-valuemax=\"100\">\r\n\t\t\t\t\t\t<div class=\"progress-bar progress-bar-success\" style=\"width:0%;\"> </div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div><p class=\"finecms-file-ts\">上传格式要求：jpg、gif、png、jpeg（1MB）</p><div id=\"fileupload_thumb_files\" class=\"files\"><input type=\"hidden\"  id=\"dr_thumb\" name=\"data[thumb]\" value=\"\" /></div><script type=\"text/javascript\">\r\n        $(function() {\r\n            fileupload_file_init({\"name\":\"thumb\",\"ext\":\" \\\\/(\\\\\\\\.|\\\\\\\\\\\\/)(jpg|gif|png|jpeg)$\\\\/i\",\"size\":1048576,\"url\":\"\\\\/admin3faf81d65fd6.php?c=api&is_iframe=1&token=c4b7ab6cf996162298be3d0173150d38&siteid=1&m=upload&p=e547f081fca6d88971a6f6dfafd77f2d&fid=10\",\"unused_url\":\"\\\\/admin3faf81d65fd6.php?c=api&m=input_file_list&is_iframe=1&p=e547f081fca6d88971a6f6dfafd77f2d&fid=10\",\"input_url\":\"\\\\/admin3faf81d65fd6.php?c=api&m=input_file_url&is_iframe=1&token=c4b7ab6cf996162298be3d0173150d38&siteid=1&p=e547f081fca6d88971a6f6dfafd77f2d&fid=10&file=&one=1\",\"tpl\":\"<div id=\\\\\"dr_thumb_files_row\\\\\" class=\\\\\"file_row_html files_row\\\\\"><div class=\\\\\"files_row_preview preview\\\\\">{preview}<\\\\/div><input type=\\\\\"hidden\\\\\"  id=\\\\\"dr_thumb\\\\\" class=\\\\\"files_row_id\\\\\" name=\\\\\"data[thumb]\\\\\" value=\\\\\"{id}\\\\\" \\\\/><\\\\/div>\",\"area\":[\"80%\",\"80%\"],\"url_area\":[\"50%\",\"300px\"],\"chunk\":0});\r\n        });\r\n        </script></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_keywords\">\n    <label class=\"control-label col-md-2\">标签</label>\n    <div class=\"col-md-10\"><input class=\"form-control  \" type=\"text\" name=\"data[keywords]\" id=\"dr_keywords\" value=\"\" style=\"width:400px;\"   data-role=\"tagsinput\" /></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_description\">\n    <label class=\"control-label col-md-2\">描述</label>\n    <div class=\"col-md-10\"><textarea class=\"form-control\" style=\"height:60px; width:500px;\" name=\"data[description]\" id=\"dr_description\" ></textarea></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_content\">\n    <label class=\"control-label col-md-2\"><span class=\"required\" aria-required=\"true\"> * </span>内容</label>\n    <div class=\"col-md-10\">\r\n            <link href=\"/static/assets/editor/summernote.css?v=1750414398\" rel=\"stylesheet\">\r\n            <script type=\"text/javascript\" src=\"/static/assets/editor/summernote.min.js?v=1750414398\"></script>\r\n            <textarea class=\"dr_ueditor\" name=\"data[content]\" id=\"dr_content\"></textarea><script type=\"text/javascript\">function dr_is_auto_description_content(){var v=$(\"#is_auto_description_content\").is(\":checked\");if(v==true){$(\"#dr_description\").prop(\"readonly\",true)}else{$(\"#dr_description\").prop(\"readonly\",false)}}$(function(){dr_is_auto_description_content();$(\\'#dr_content\\').summernote({isMobileWidth:\\'80%\\',llVideoUrl:\\'/admin3faf81d65fd6.php?c=api&m=input_file_list&is_iframe=1&p=ef26316cbc2ce9210e95ec59a8f1caee\\',llImageUrl:\\'/admin3faf81d65fd6.php?c=api&m=input_file_list&is_iframe=1&p=9acbcd03b01d263e5f4cd4e5010d214a&is_wm=0\\',attachUrl:\\'/admin3faf81d65fd6.php?c=api&m=input_file_list&is_iframe=1&p=55f63655dcd780e69f69faf78a059bc0\\',isImageTitle:\\'\\',isImageAlt:\\'\\',height:\\'400\\',width:\\'100%\\'})});function dr_editor_down_img_content(){var index=layer.load(2,{shade:[0.3,\\'#fff\\'],time:100000000});$.ajax({type:\\'POST\\',url:\\'/admin3faf81d65fd6.php?c=api&m=down_img&is_iframe=1&token=c4b7ab6cf996162298be3d0173150d38&rid=a4fae6dd2d8a2e0ac6bf13d4779f5a1e&p=9acbcd03b01d263e5f4cd4e5010d214a&is_wm=0\\',dataType:\\'json\\',data:{value:$(\\'#dr_content\\').summernote(\\'code\\')},success:function(json){layer.close(index);if(json.code==0){dr_cmf_tips(0,json.msg,json.data.time)}else{var width=\\'500px\\';var height=\\'70%\\';if(is_mobile_cms==1){width=\\'95%\\';height=\\'90%\\'}layer.open({type:2,title:\\'\\',fix:true,scrollbar:false,maxmin:false,resize:true,shadeClose:true,shade:0,area:[width,height],btn:[dr_lang(\\'确定\\'),dr_lang(\\'取消\\')],yes:function(index,layero){var loading=layer.load(2,{shade:[0.3,\\'#fff\\'],time:100000000});var body=layer.getChildFrame(\\'body\\',index);$.ajax({type:\\'POST\\',dataType:\\'json\\',url:json.msg,data:$(body).find(\\'#myform\\').serialize(),success:function(json){layer.close(loading);if(json.code){layer.close(index);$(\\'#dr_content\\').summernote(\\'reset\\');$(\\'#dr_content\\').summernote(\\'pasteHTML\\',json.data);dr_cmf_tips(1,json.msg)}else{dr_cmf_tips(0,json.msg,json.data.time)}return false},error:function(HttpRequest,ajaxOptions,thrownError){dr_ajax_alert_error(HttpRequest,this,thrownError)}});return false},success:function(layero,index){var body=layer.getChildFrame(\\'body\\',index);var json=$(body).html();if(json.indexOf(\\'\"code\":0\\')>0&&json.length<500){var obj=JSON.parse(json);layer.close(index);dr_cmf_tips(0,obj.msg)}},content:json.msg+\\'&is_iframe=1\\'})}},error:function(HttpRequest,ajaxOptions,thrownError){dr_ajax_alert_error(HttpRequest,this,thrownError)}})}</script><div class=\"mt-checkbox-inline\" style=\"margin-top: 10px;\">     <label style=\"margin-bottom: 0;\" class=\"mt-checkbox mt-checkbox-outline\">\r\n                  <input name=\"is_auto_thumb_content\" type=\"checkbox\"  value=\"1\"> 提取第一个图片为缩略图 <span></span>\r\n                 </label>\r\n                 <label style=\"margin-bottom: 0;\" class=\"mt-checkbox mt-checkbox-outline\">\r\n                  <input id=\"is_auto_description_content\" onclick=\"dr_is_auto_description_content()\" name=\"is_auto_description_content\" type=\"checkbox\"  value=\"1\"> 提取内容作为描述信息 <span></span>\r\n                 </label>\r\n                 <label style=\"margin-bottom: 0;\" class=\"mt-checkbox mt-checkbox-outline\">\r\n                  <input name=\"is_remove_a_content\" type=\"checkbox\"  value=\"1\"> 去除站外链接 <span></span>\r\n                 </label>\r\n                 <label style=\"margin-bottom: 0;\" class=\"mt-checkbox mt-checkbox-outline\">\r\n                  <a class=\"btn blue btn-xs\" onclick=\"dr_editor_down_img_content()\"> 一键下载远程图片 </a>\r\n                 </label></div></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_website\">\n    <label class=\"control-label col-md-2\">官网</label>\n    <div class=\"col-md-10\"><input class=\"form-control  \" type=\"text\" name=\"data[website]\" id=\"dr_website\" value=\"\" style=\"width:100%;\"   /></div>\n</div>'"}, {"name": "sysfield", "value": "'<div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_inputtime\">\n    <label class=\"control-label col-md-2\"><span class=\"required\" aria-required=\"true\"> * </span>录入时间</label>\n    <div class=\"col-md-10\"><div class=\"form-date input-group\">\r\n\t\t\t<link href=\"/static/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css?v=1750414398\" rel=\"stylesheet\" type=\"text/css\" />\r\n\t\t\t<link href=\"/static/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker.min.css?v=1750414398\" rel=\"stylesheet\" type=\"text/css\" />\r\n\t\t\t<link href=\"/static/assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css?v=1750414398\" rel=\"stylesheet\" type=\"text/css\" />\r\n\t\t\t\r\n        \t<script src=\"/static/assets/global/plugins/moment.min.js?v=1750414398\" type=\"text/javascript\"></script>\r\n\t\t\t<script src=\"/static/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.finecms.js?v=1750414398\" type=\"text/javascript\"></script>\r\n\t\t\t<script src=\"/static/assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.finecms.js?v=1750414398\" type=\"text/javascript\"></script>\r\n\t\t\t<div class=\"input-group date field_date_inputtime\"><span class=\"input-group-btn\">\r\n\t\t\t\t\t<button class=\"btn default date-set\" type=\"button\">\r\n\t\t\t\t\t\t<i class=\"fa fa-calendar\"></i>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</span><input id=\"dr_inputtime\" name=\"data[inputtime]\" type=\"text\" style=\"width:100%;\" value=\"2025-06-26 12:13:58\"  required=\"required\" class=\"form-control \"></div>\r\n\t\t\t<script>\r\n\t\t\t$(function(){\r\n\t\t\t\t$(\".field_date_inputtime\").datetimepicker({\r\n\t\t\t\t\tisRTL: false,\r\n\t\t\t\t\tformat: \"yyyy-mm-dd hh:ii:ss\",\r\n\t\t\t\t\tshowMeridian: true,\r\n\t\t\t\t\tautoclose: true,\r\n\t\t\t\t\tpickerPosition: \"bottom-right\",\r\n\t\t\t\t\ttodayBtn: \"linked\"\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\t</script></div></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_updatetime\">\n    <label class=\"control-label col-md-2\"><span class=\"required\" aria-required=\"true\"> * </span>更新时间</label>\n    <div class=\"col-md-10\"><div class=\"form-date input-group\"><div class=\"input-group date field_date_updatetime\"><span class=\"input-group-btn\">\r\n\t\t\t\t\t<button class=\"btn default date-set\" type=\"button\">\r\n\t\t\t\t\t\t<i class=\"fa fa-calendar\"></i>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</span><input id=\"dr_updatetime\" name=\"data[updatetime]\" type=\"text\" style=\"width:100%;\" value=\"2025-06-26 12:13:58\"  required=\"required\" class=\"form-control \"></div>\r\n\t\t\t<script>\r\n\t\t\t$(function(){\r\n\t\t\t\t$(\".field_date_updatetime\").datetimepicker({\r\n\t\t\t\t\tisRTL: false,\r\n\t\t\t\t\tformat: \"yyyy-mm-dd hh:ii:ss\",\r\n\t\t\t\t\tshowMeridian: true,\r\n\t\t\t\t\tautoclose: true,\r\n\t\t\t\t\tpickerPosition: \"bottom-right\",\r\n\t\t\t\t\ttodayBtn: \"linked\"\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\t</script><label><input name=\"no_time\"  class=\"dr_no_time\" type=\"checkbox\" value=\"1\" /> 不更新</label></div></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_inputip\">\n    <label class=\"control-label col-md-2\">客户端IP</label>\n    <div class=\"col-md-10\">\r\n\t\t <div class=\"input-group\" style=\"width:100%;\">\r\n\t\t\t\t<input class=\"form-control \" type=\"text\" name=\"data[inputip]\" id=\"dr_inputip\" value=\"**************-20587\"   />\r\n\t\t\t\t<span class=\"input-group-btn\">\r\n\t\t\t\t\t<a class=\"btn btn-success \" style=\"border-color:default;background-color:default\" href=\"javascript:dr_show_ip(\\'inputip\\');\" ><i class=\"fa fa-arrow-right\" /></i> 查看</a>\r\n\t\t\t\t</span>\r\n\t\t\t</div>\r\n\t\t</div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_displayorder\">\n    <label class=\"control-label col-md-2\">排列值</label>\n    <div class=\"col-md-10\">\r\n\t\t\t<link href=\"/static/assets/global/plugins/bootstrap-touchspin/bootstrap.touchspin.min.css?v=1750414398\" rel=\"stylesheet\" type=\"text/css\" />\r\n\t\t\t<script src=\"/static/assets/global/plugins/fuelux/js/spinner.min.js?v=1750414398\" type=\"text/javascript\"></script>\r\n\t\t\t<script src=\"/static/assets/global/plugins/bootstrap-touchspin/bootstrap.touchspin.js?v=1750414398\" type=\"text/javascript\"></script>\r\n\t\t\t<div style=\"width:100%;\"><input class=\"form-control \" type=\"text\" name=\"data[displayorder]\" id=\"dr_displayorder\" value=\"0\"   /></div><script type=\"text/javascript\">$(function(){$(\"#dr_displayorder\").TouchSpin({buttondown_class:\"btn default\",buttonup_class:\"btn default\",verticalbuttons:false,decimals:0,step:1,min:0,max:999999999999999})});</script></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_hits\">\n    <label class=\"control-label col-md-2\">浏览数</label>\n    <div class=\"col-md-10\"><div style=\"width:100%;\"><input class=\"form-control \" type=\"text\" name=\"data[hits]\" id=\"dr_hits\" value=\"1\"   /></div><script type=\"text/javascript\">$(function(){$(\"#dr_hits\").TouchSpin({buttondown_class:\"btn default\",buttonup_class:\"btn default\",verticalbuttons:false,decimals:0,step:1,min:1,max:9999999})});</script></div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_uid\">\n    <label class=\"control-label col-md-2\">账号</label>\n    <div class=\"col-md-10\">\r\n\t\t <div class=\"input-group\" style=\"width:200px;\">\r\n\t\t\t\t<input class=\"form-control \" type=\"text\" name=\"data[uid]\" id=\"dr_uid\" value=\"admin\"   />\r\n\t\t\t\t<span class=\"input-group-btn\">\r\n\t\t\t\t\t<a class=\"btn btn-success \" style=\"border-color:default;background-color:default\" href=\"javascript:dr_show_member(\\'uid\\');\" ><i class=\"fa fa-user\" /></i> 资料</a>\r\n\t\t\t\t</span>\r\n\t\t\t</div>\r\n\t\t</div>\n</div><div class=\"form-group\" rs=\"is_admin\" id=\"dr_row_author\">\n    <label class=\"control-label col-md-2\">笔名</label>\n    <div class=\"col-md-10\"><input class=\"form-control  \" type=\"text\" name=\"data[author]\" id=\"dr_author\" value=\"创始人\" style=\"width:200px;\"   /></div>\n</div>'"}, {"name": "diyfield", "value": "''"}, {"name": "catfield", "value": "''"}, {"name": "mymerge", "value": "array (\n)"}, {"name": "form", "value": "'<input name=\"is_form\" type=\"hidden\" value=\"1\">\n<input name=\"is_admin\" type=\"hidden\" value=\"1\">\n<input name=\"is_tips\" type=\"hidden\" value=\"\">\n<input name=\"csrf_test_name\" type=\"hidden\" value=\"cd8376769004a149d672615f02225890\">\n<input name=\"is_draft\" id=\"dr_is_draft\" type=\"hidden\" value=\"0\">\n<input name=\"module\" id=\"dr_module\" type=\"hidden\" value=\"webnav\">\n<input name=\"id\" id=\"dr_id\" type=\"hidden\" value=\"0\">\n'"}, {"name": "reply_url", "value": "'admin3faf81d65fd6.php?s=webnav&c=home&m=index&pagesize=10&order=+&_=1750911046299&page=13'"}, {"name": "uriprefix", "value": "'webnav/home'"}, {"name": "is_edit", "value": "0"}, {"name": "did", "value": "0"}, {"name": "menu", "value": "'<li class=\"dropdown\"> <a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index\" class=\"{ON}\"> <i class=\"fa fa-navicon\"></i>  网址管理</a> <a class=\"dropdown-toggle {ON}\"  data-hover=\"dropdown\" data-close-others=\"true\" aria-expanded=\"true\"><i class=\"fa fa-angle-double-down\"></i></a><ul class=\"dropdown-menu\"><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index\"> <i class=\"fa fa-navicon\"></i> 网址管理 </a></li><li class=\"divider\"> </li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=flag&m=index&flag=1\"> <i class=\"fa fa-fire\"></i> 热门推荐 </a></li><li class=\"divider\"> </li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=fankui&m=index\"> <i class=\"fa fa-table\"></i> 反馈管理 </a></li><li class=\"divider\"> </li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=draft&m=index\"> <i class=\"fa fa-pencil\"></i> 草稿箱管理 </a></li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=recycle&m=index\"> <i class=\"fa fa-trash-o\"></i> 回收站管理 </a></li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=time&m=index\"> <i class=\"fa fa-clock-o\"></i> 待发布管理 </a></li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=verify&m=index\"> <i class=\"fa fa-edit\"></i> 待审核管理 </a></li><li class=\"divider\"> </li><li><a href=\"javascript:dr_iframe_show(\\'模块内容字段\\', \\'admin3faf81d65fd6.php?c=field&m=index&rname=module&rid=2&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-code\"></i> 模块内容字段</a> </li><li><a href=\"javascript:dr_iframe_show(\\'栏目模型字段\\', \\'admin3faf81d65fd6.php?c=field&m=index&rname=catmodule-webnav&rid=0&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-code\"></i> 栏目模型字段</a> </li><li><a href=\"javascript:dr_iframe_show(\\'划分栏目模型字段\\', \\'admin3faf81d65fd6.php?s=module&c=module_category&m=field_index&dir=webnav&rid=0&is_menu=1\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-edit\"></i> 划分栏目模型字段</a> </li></ul> <i class=\"fa fa-circle\"></i> </li><li><a href=\"admin3faf81d65fd6.php?s=webnav&c=category&m=index\"> <i class=\"fa fa-reorder\"></i> 栏目管理</a> <i class=\"fa fa-circle\"></i> </li><li><a href=\"javascript:dr_iframe_show(\\'批量更新内容URL\\', \\'admin3faf81d65fd6.php?c=api&m=update_url&mid=webnav\\', \\'500px\\', \\'300px\\')\"\"> <i class=\"fa fa-refresh\"></i> 更新URL</a> <i class=\"fa fa-circle\"></i> </li><li><a href=\"javascript:dr_iframe_show(\\'模块配置\\', \\'admin3faf81d65fd6.php?s=module&c=module&m=edit&id=2\\', \\'80%\\', \\'80%\\')\"\"> <i class=\"fa fa-cog\"></i> 模块配置</a> <i class=\"fa fa-circle\"></i> </li><li> <a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&pagesize=10&order=+&_=1750911046299&total=124&page=13\" class=\"\"> <i class=\"fa fa-reply\"></i> 返回</a> <i class=\"fa fa-circle\"></i> </li><li> <a href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid=3\" class=\"on\"> <i class=\"fa fa-plus\"></i> 发布</a> <i class=\"fa fa-circle\"></i> </li>'"}, {"name": "select", "value": "'<select class=\"bs-select form-control\" id=\\'dr_catid\\' name=\\'catid\\' onChange=\"show_category_field(this.value)\">\n<option _selected_3_ value=\\'3\\'>AI视频工具</option>\n<option _selected_2_ value=\\'2\\'>AI生图工具</option>\n<option _selected_6_ value=\\'6\\'>AI音频工具</option>\n<option _selected_5_ value=\\'5\\'>AI视频文案</option>\n</select>\n<script type=\"text/javascript\"> var bs_selectAllText = \\'全选\\';var bs_deselectAllText = \\'全删\\';var bs_noneSelectedText = \\'没有选择\\'; var bs_noneResultsText = \\'没有找到 {0}\\';</script>\n<link href=\"/static/assets/global/plugins/bootstrap-select/css/bootstrap-select.css\" rel=\"stylesheet\" type=\"text/css\" />\n<script src=\"/static/assets/global/plugins/bootstrap-select/js/bootstrap-select.js\" type=\"text/javascript\"></script>\n<script type=\"text/javascript\"> jQuery(document).ready(function() { $(\\'.bs-select\\').selectpicker();  }); </script>'"}, {"name": "is_flag", "value": "array (\n  1 => \n  array (\n    'name' => '热门推荐',\n    'icon' => 'fa fa-fire',\n    'role' => \n    array (\n    ),\n  ),\n)"}, {"name": "draft_url", "value": "'admin3faf81d65fd6.php?s=webnav&c=home&m=add'"}, {"name": "draft_list", "value": "array (\n)"}, {"name": "category_field_url", "value": "''"}, {"name": "my_web_url", "value": "'/admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid='"}, {"name": "get", "value": "array (\n  's' => 'webnav',\n  'c' => 'home',\n  'm' => 'add',\n  'catid' => '',\n)"}], "tips": [{"name": "share_post.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/share_post.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/App/Module/Views/share_post.html]！"}, {"name": "header.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/header.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/header.html]"}, {"name": "head.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/head.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/head.html]"}, {"name": "footer.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/footer.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/footer.html]"}], "times": [{"tpl": 0.01}], "files": {"/home/<USER>/web/dayrui/App/Module/Views/share_post.html": {"name": "share_post.html", "path": "/home/<USER>/web/dayrui/App/Module/Views/share_post.html"}, "/home/<USER>/web/dayrui/Fcms/View/header.html": {"name": "header.html", "path": "/home/<USER>/web/dayrui/Fcms/View/header.html"}, "/home/<USER>/web/dayrui/Fcms/View/head.html": {"name": "head.html", "path": "/home/<USER>/web/dayrui/Fcms/View/head.html"}, "/home/<USER>/web/dayrui/Fcms/View/footer.html": {"name": "footer.html", "path": "/home/<USER>/web/dayrui/Fcms/View/footer.html"}}}, "badgeValue": 4, "isEmpty": false, "hasTabContent": true, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 202 )", "display": {"coreFiles": [], "userFiles": [{"path": "/home/<USER>/web/cache/config/domain_app.php", "name": "domain_app.php"}, {"path": "/home/<USER>/web/cache/config/domain_client.php", "name": "domain_client.php"}, {"path": "/home/<USER>/web/cache/config/site.php", "name": "site.php"}, {"path": "/home/<USER>/web/cache/config/system.php", "name": "system.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_App_DS_Module_DS_Views_DS_share_post.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_App_DS_Module_DS_Views_DS_share_post.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_head.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_head.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_header.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_header.html.cache.php"}, {"path": "/home/<USER>/web/dayrui/App/Form/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Module_init.php", "name": "Module_init.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Run.php", "name": "Run.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Module.php", "name": "Module.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Libraries/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Models/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php", "name": "Login.php"}, {"path": "/home/<USER>/web/dayrui/App/Tag/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Home.php", "name": "Home.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Autoload.php", "name": "Autoload.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Constants.php", "name": "Constants.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Feature.php", "name": "Feature.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Hook.php", "name": "Hook.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Common.php", "name": "Common.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseService.php", "name": "BaseService.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factories.php", "name": "Factories.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factory.php", "name": "Factory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Query.php", "name": "Query.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Timer.php", "name": "Timer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Events/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Header.php", "name": "Header.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Message.php", "name": "Message.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Response.php", "name": "Response.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/URI.php", "name": "URI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/Time.php", "name": "Time.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Log/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Modules/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouter.php", "name": "AutoRouter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Security/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Security/SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Superglobals.php", "name": "Superglobals.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Escaper/Escaper.php", "name": "Escaper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LogLevel.php", "name": "LogLevel.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Helper.php", "name": "Helper.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php", "name": "Phpcmf.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Service.php", "name": "Service.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Table.php", "name": "Table.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Catids.php", "name": "Catids.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Date.php", "name": "Date.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Editor.php", "name": "Editor.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/File.php", "name": "File.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Select.php", "name": "Select.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Text.php", "name": "Text.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Textarea.php", "name": "Textarea.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Textbtn.php", "name": "Textbtn.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Touchspin.php", "name": "Touchspin.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Uid.php", "name": "Uid.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Field.php", "name": "Field.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Form.php", "name": "Form.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Input.php", "name": "Input.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Js_packer.php", "name": "Js_packer.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Lang.php", "name": "Lang.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Tree.php", "name": "Tree.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php", "name": "Auth.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Member.php", "name": "Member.php"}, {"path": "/home/<USER>/web/dayrui/My/Config/License.php", "name": "License.php"}, {"path": "/home/<USER>/web/dayrui/My/Config/Version.php", "name": "Version.php"}, {"path": "/home/<USER>/web/public/admin3faf81d65fd6.php", "name": "admin3faf81d65fd6.php"}, {"path": "/home/<USER>/web/public/api/language/zh-cn/lang.php", "name": "lang.php"}, {"path": "/home/<USER>/web/public/config/custom.php", "name": "custom.php"}, {"path": "/home/<USER>/web/public/config/database.php", "name": "database.php"}, {"path": "/home/<USER>/web/public/config/field.php", "name": "field.php"}, {"path": "/home/<USER>/web/public/config/hooks.php", "name": "hooks.php"}, {"path": "/home/<USER>/web/public/index.php", "name": "index.php"}]}, "badgeValue": 202, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"uri": "webnav/home/<USER>", "url": "/admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid=", "app": "webnav", "controller": "home", "method": "add", "file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Home.php"}], "get": {"s": "webnav", "c": "home", "m": "add", "catid": ""}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "5.33", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.22", "count": 7}}}, "badgeValue": 8, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.823194, "duration": 0.005326032638549805}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.853569, "duration": 4.887580871582031e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.855049, "duration": 2.7179718017578125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.865697, "duration": 3.218650817871094e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.866244, "duration": 2.288818359375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.867539, "duration": 3.218650817871094e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.868175, "duration": 2.7894973754882812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.893919, "duration": 3.2901763916015625e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1750910995</pre>", "uid": "1", "_ci_previous_url": "https://www.kuhao123.com/admin3faf81d65fd6.php?s=webnav&amp;c=home&amp;m=add&amp;catid=5", "auth_csrf_token": "c4b7ab6cf996162298be3d0173150d38"}, "get": {"s": "webnav", "c": "home", "m": "add", "catid": ""}, "headers": {"Cookie": "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJtbFh0amEzdWZFVnZpZ2JMdEZsZEE9PSIsInZhbHVlIjoiN0tlRlFRY1YyUFJQVnJDSStPVldLc1NPMldxblJYbW1PbnlZLzBMSlhXOUNnUkxCN0tzZnhYbTExZnBabmJZRTcva0lyazFzUUozT1NZMG96N2Z5RnUzZE5CM1lXQml2a2V5UTdoSUNuVU9nbXgyeGlSVDd4anBxd3R3R2hFODBEQmx4bUN4YXYxWHdLWXJGUGljMGVIb0dRam85Zk16Mk1xUytBMW0vcUpxeFQ2SHM0VVZvQjgybUlJTFpyTU5KRFdraWdjRG1pZzZ2eDZwSGs5ZWRQbVEwbGRYTWVab3pSY3Qyb043bkdkUT0iLCJtYWMiOiJhNjM0ZDIzYmYxZmJmOTM4YTA5ZDU5YzZlMWI0ZGFmNjQ2OTM1NjA3YmZmYmNiNTRhZDRmZjk2YTlmNWEyMmI5IiwidGFnIjoiIn0%3D; PHPSESSID=kjb18ivsdqceau93qdhn34vd0d; d41d8cd98f00b204e9800998ecf8427e_member_uid=1; d41d8cd98f00b204e9800998ecf8427e_member_cookie=5424b2695385ffdccec8d972124ec65f; csrf_cookie_name=cd8376769004a149d672615f02225890; ci_session=bfsq0jfq4aau88p4hrnbre22m219kqkh; d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-638=638; d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-447=447", "Priority": "u=0, i", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8", "Accept-Encoding": "gzip, deflate, br, zstd", "Sec-Fetch-Dest": "document", "Sec-Fetch-User": "?1", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Upgrade-Insecure-Requests": "1", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Host": "www.kuhao123.com"}, "cookies": {"remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6ImJtbFh0amEzdWZFVnZpZ2JMdEZsZEE9PSIsInZhbHVlIjoiN0tlRlFRY1YyUFJQVnJDSStPVldLc1NPMldxblJYbW1PbnlZLzBMSlhXOUNnUkxCN0tzZnhYbTExZnBabmJZRTcva0lyazFzUUozT1NZMG96N2Z5RnUzZE5CM1lXQml2a2V5UTdoSUNuVU9nbXgyeGlSVDd4anBxd3R3R2hFODBEQmx4bUN4YXYxWHdLWXJGUGljMGVIb0dRam85Zk16Mk1xUytBMW0vcUpxeFQ2SHM0VVZvQjgybUlJTFpyTU5KRFdraWdjRG1pZzZ2eDZwSGs5ZWRQbVEwbGRYTWVab3pSY3Qyb043bkdkUT0iLCJtYWMiOiJhNjM0ZDIzYmYxZmJmOTM4YTA5ZDU5YzZlMWI0ZGFmNjQ2OTM1NjA3YmZmYmNiNTRhZDRmZjk2YTlmNWEyMmI5IiwidGFnIjoiIn0=", "PHPSESSID": "kjb18ivsdqceau93qdhn34vd0d", "d41d8cd98f00b204e9800998ecf8427e_member_uid": "1", "d41d8cd98f00b204e9800998ecf8427e_member_cookie": "5424b2695385ffdccec8d972124ec65f", "csrf_cookie_name": "cd8376769004a149d672615f02225890", "ci_session": "bfsq0jfq4aau88p4hrnbre22m219kqkh", "d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-638": "638", "d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-447": "447"}, "request": "HTTPS/2.0", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.7", "phpVersion": "8.2.28", "phpSAPI": "fpm-fcgi", "environment": "development", "baseURL": "https://www.kuhao123.com/", "timezone": "PRC", "locale": "zh-cn", "cspEnabled": false}}