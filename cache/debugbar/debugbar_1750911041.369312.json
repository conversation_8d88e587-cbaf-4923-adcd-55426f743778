{"url": "https://www.kuhao123.com/admin3faf81d65fd6.php", "method": "GET", "isAJAX": false, "startTime": **********.263165, "totalTime": 101.6, "totalMemory": "10.028", "segmentDuration": 15, "segmentCount": 7, "CI_VERSION": "4.4.7", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.277676, "duration": 0.011491775512695312}, {"name": "Routing", "component": "Timer", "start": **********.289175, "duration": 5.507469177246094e-05}, {"name": "Before Filters", "component": "Timer", "start": **********.289967, "duration": 6.699562072753906e-05}, {"name": "Controller", "component": "Timer", "start": **********.290039, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.290039, "duration": 0.053787946701049805}, {"name": "After Filters", "component": "Timer", "start": **********.364757, "duration": 0.0004868507385253906}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(17 total Queries, 17 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.49 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Member.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:17", "function": "        Phpcmf\\Table->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Category->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "5ed8322e543819859306c452aa11dd51"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Member.php:221", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:305", "function": "        Phpcmf\\Model\\Member->get_member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:17", "function": "        Phpcmf\\Table->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Category->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "3614b24ee445acf4dda11df909bbdf75"}, {"hover": "", "class": "", "duration": "0.66 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:328", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:593", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:17", "function": "        Phpcmf\\Table->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Category->__construct()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "d3db0cd6b962513e9987a3ee117b7cfa"}, {"hover": "", "class": "", "duration": "0.53 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:103", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:340", "function": "        Phpcmf\\Model\\Auth->_role()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php:593", "function": "        Phpcmf\\Model\\Auth->member()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:357", "function": "        Phpcmf\\Model\\Auth->is_admin_login()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:17", "function": "        Phpcmf\\Table->__construct()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Category->__construct()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 13    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 14    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "3126db7ff9a02c26d241486b539bd694"}, {"hover": "", "class": "", "duration": "0.63 ms", "sql": "SHOW TABLES <strong>FROM</strong> `kh123`", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1410", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1429", "function": "        CodeIgniter\\Database\\BaseConnection->listTables()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php:12", "function": "        CodeIgniter\\Database\\BaseConnection->tableExists()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php:313", "function": "        call_user_func()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:17", "function": "        Phpcmf\\Table->__construct()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Category->__construct()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 15    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 16    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 17    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php:1410", "qid": "2bf8c143c3a03a22d38f4a8fcbd77007"}, {"hover": "", "class": "", "duration": "0.59 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:615", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php:29", "function": "        Phpcmf\\Model->getRow()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php:142", "function": "        Phpcmf\\Model\\Safe\\Login->get_log()", "index": "  4    "}, {"function": "        Phpcmf\\Service::{closure}", "file": "[internal function]", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php:313", "function": "        call_user_func()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:453", "function": "        Phpcmf\\Hooks::trigger()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php:427", "function": "        Phpcmf\\Common->_init_run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Table.php:58", "function": "        Phpcmf\\Common->__construct()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:17", "function": "        Phpcmf\\Table->__construct()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Category->__construct()", "index": " 11    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": " 12    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 15    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 16    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 17    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "e647e11f25b954958aeb221f5800a615"}, {"hover": "", "class": "", "duration": "0.68 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_field`\n<strong>WHERE</strong> `disabled` = 0\n<strong>AND</strong> `relatedname` = &#039;category-webnav&#039;\n<strong>ORDER</strong> <strong>BY</strong> `displayorder` <strong>ASC</strong>, `id` <strong>ASC</strong>", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Models/Repair.php:28", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:57", "function": "        Phpcmf\\Model\\Module\\Repair->get_category_field()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:23", "function": "        Phpcmf\\Admin\\Category->_Extend_Init()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Category->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": "  9    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 11    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "48e44e802a115b27f6f42c91301d9ccb"}, {"hover": "", "class": "", "duration": "0.64 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:237", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:124", "function": "        Phpcmf\\Model->counts()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:23", "function": "        Phpcmf\\Admin\\Category->_Extend_Init()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:915", "function": "        Phpcmf\\Admin\\Category->__construct()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:494", "function": "        CodeIgniter\\CodeIgniter->createController()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": "  9    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 11    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "qid": "8001648650fea9732f0e62f7a553803d"}, {"hover": "", "class": "", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 0\n<strong>ORDER</strong> <strong>BY</strong> `displayorder` <strong>ASC</strong>, `id` <strong>ASC</strong>", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:559", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Models/Category.php:234", "function": "        Phpcmf\\Model->getAll()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Model\\Module\\Category->cat_data()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "d9891d32a3107f878b1978f8f8fb9461"}, {"hover": "", "class": "", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `id` = 3", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:498", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php:575", "function": "        Phpcmf\\Model->get()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:255", "function": "        dr_module_category_url()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Admin\\Category->_get_tree_list()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "9f97fbbd672db423146079e1a86b1f53"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 3", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:237", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:362", "function": "        Phpcmf\\Model->counts()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Admin\\Category->_get_tree_list()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "qid": "6a651d5957d3234edbb56354e98954c0"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `id` = 2", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:498", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php:575", "function": "        Phpcmf\\Model->get()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:255", "function": "        dr_module_category_url()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Admin\\Category->_get_tree_list()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "432b03061d788f460119e28dfe13fb91"}, {"hover": "", "class": "", "duration": "1.14 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 2", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:237", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:362", "function": "        Phpcmf\\Model->counts()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Admin\\Category->_get_tree_list()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "qid": "2a03525ed06b2f64c6a3609d1f2de76c"}, {"hover": "", "class": "", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `id` = 6", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:498", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php:575", "function": "        Phpcmf\\Model->get()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:255", "function": "        dr_module_category_url()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Admin\\Category->_get_tree_list()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "213897f409985e8b7ab71ce3dbfabe07"}, {"hover": "", "class": "", "duration": "0.7 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 6", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:237", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:362", "function": "        Phpcmf\\Model->counts()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Admin\\Category->_get_tree_list()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "qid": "2c95e15fa69cf317092966626f801d54"}, {"hover": "", "class": "", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `id` = 5", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:498", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php:575", "function": "        Phpcmf\\Model->get()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:255", "function": "        dr_module_category_url()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Admin\\Category->_get_tree_list()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 12    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 13    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1616", "qid": "aedb1434b8f97fb0c0781892d6bdbcf1"}, {"hover": "", "class": "", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 5", "trace": [{"file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Core/Model.php:237", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:362", "function": "        Phpcmf\\Model->counts()", "index": "  3    "}, {"file": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php:744", "function": "        Phpcmf\\Admin\\Category->_get_tree_list()", "index": "  4    "}, {"file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php:11", "function": "        Phpcmf\\Admin\\Category->_Admin_List()", "index": "  5    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:943", "function": "        Phpcmf\\Controllers\\Admin\\Category->index()", "index": "  6    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:503", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "/home/<USER>/web/dayrui/CodeIgniter/Init.php:106", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "/home/<USER>/web/dayrui/Fcms/Init.php:514", "args": ["/home/<USER>/web/dayrui/CodeIgniter/Init.php"], "function": "        require()", "index": " 10    "}, {"file": "/home/<USER>/web/public/index.php:59", "args": ["/home/<USER>/web/dayrui/Fcms/Init.php"], "function": "        require()", "index": " 11    "}, {"file": "/home/<USER>/web/public/admin3faf81d65fd6.php:9", "args": ["/home/<USER>/web/public/index.php"], "function": "        require()", "index": " 12    "}], "trace-file": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php:1700", "qid": "0565a3829bea43c7c4d3249ba88a3c5f"}]}, "badgeValue": 17, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.309394, "duration": "0.006904"}, {"name": "Query", "component": "Database", "start": **********.316901, "duration": "0.000491", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.318489, "duration": "0.000481", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_member_data`\n<strong>WHERE</strong> `id` = 1"}, {"name": "Query", "component": "Database", "start": **********.329868, "duration": "0.000660", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.330659, "duration": "0.000534", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_admin_role_index`\n<strong>WHERE</strong> `uid` = 1"}, {"name": "Query", "component": "Database", "start": **********.332314, "duration": "0.000627", "query": "SHOW TABLES <strong>FROM</strong> `kh123`"}, {"name": "Query", "component": "Database", "start": **********.333339, "duration": "0.000587", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_app_login`\n<strong>WHERE</strong> `uid` = &#039;1&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.340743, "duration": "0.000681", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_field`\n<strong>WHERE</strong> `disabled` = 0\n<strong>AND</strong> `relatedname` = &#039;category-webnav&#039;\n<strong>ORDER</strong> <strong>BY</strong> `displayorder` <strong>ASC</strong>, `id` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.343081, "duration": "0.000641", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`"}, {"name": "Query", "component": "Database", "start": **********.343933, "duration": "0.000598", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 0\n<strong>ORDER</strong> <strong>BY</strong> `displayorder` <strong>ASC</strong>, `id` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.345527, "duration": "0.000497", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `id` = 3"}, {"name": "Query", "component": "Database", "start": **********.346491, "duration": "0.000477", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 3"}, {"name": "Query", "component": "Database", "start": **********.347275, "duration": "0.000322", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `id` = 2"}, {"name": "Query", "component": "Database", "start": **********.348118, "duration": "0.001140", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 2"}, {"name": "Query", "component": "Database", "start": **********.349511, "duration": "0.000501", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `id` = 6"}, {"name": "Query", "component": "Database", "start": **********.350447, "duration": "0.000699", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 6"}, {"name": "Query", "component": "Database", "start": **********.351492, "duration": "0.000595", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `id` = 5"}, {"name": "Query", "component": "Database", "start": **********.352565, "duration": "0.000550", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `dr_1_webnav_category`\n<strong>WHERE</strong> `pid` = 5"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": {"vars": [{"name": "is_ajax", "value": "''"}, {"name": "is_mobile", "value": "0"}, {"name": "dir", "value": "'webnav'"}, {"name": "mid", "value": "'webnav'"}, {"name": "menu", "value": "'<li class=\"\"> <a  href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index\" class=\" {ONE} tooltips\"  data-container=\"body\" data-placement=\"bottom\" data-original-title=\"返回\" title=\"返回\"><i class=\"fa fa-reply\"></i> 返回</a> <i class=\"fa fa-circle\"></i> </li><li class=\"\"> <a  href=\"admin3faf81d65fd6.php?s=webnav&c=category&m=index\" class=\" on tooltips\"  data-container=\"body\" data-placement=\"bottom\" data-original-title=\"栏目管理\" title=\"栏目管理\"><i class=\"fa fa-reorder\"></i> 栏目管理</a> <i class=\"fa fa-circle\"></i> </li><li class=\"\"> <a  href=\"admin3faf81d65fd6.php?s=webnav&c=category&m=add\" class=\" tooltips\"  data-container=\"body\" data-placement=\"bottom\" data-original-title=\"添加\" title=\"添加\"><i class=\"fa fa-plus\"></i> 添加</a> <i class=\"fa fa-circle\"></i> </li><li class=\"\"> <a  href=\"admin3faf81d65fd6.php?s=webnav&c=category&m=all_add\" class=\" tooltips\"  data-container=\"body\" data-placement=\"bottom\" data-original-title=\"批量添加\" title=\"批量添加\"><i class=\"fa fa-plus\"></i> 批量添加</a> <i class=\"fa fa-circle\"></i> </li><li class=\"hidden\"> <a  href=\"/admin3faf81d65fd6.php?s=webnav&c=category&m=index\" class=\" tooltips\"  data-container=\"body\" data-placement=\"bottom\" data-original-title=\"属性设置\" title=\"属性设置\"><i class=\"fa fa-cog\"></i> 属性设置</a> <i class=\"fa fa-circle\"></i> </li><li class=\"hidden\"> <a  href=\"/admin3faf81d65fd6.php?s=webnav&c=category&m=index\" class=\" tooltips\"  data-container=\"body\" data-placement=\"bottom\" data-original-title=\"修改\" title=\"修改\"><i class=\"fa fa-edit\"></i> 修改</a> <i class=\"fa fa-circle\"></i> </li>'"}, {"name": "module", "value": "array (\n  'id' => '2',\n  'name' => '网址',\n  'icon' => 'fa fa-navicon',\n  'site' => \n  array (\n    1 => \n    array (\n      'html' => '0',\n      'theme' => 'default',\n      'domain' => '',\n      'template' => 'default',\n      'urlrule' => '4',\n      'is_cat' => '0',\n      'show_title' => '[第{page}页{join}]{title}{join}{catpname}{join}{SITE_NAME}',\n      'show_keywords' => '',\n      'show_description' => '',\n      'list_title' => '[第{page}页{join}]{catpname}{join}{SITE_NAME}',\n      'list_keywords' => '',\n      'list_description' => '',\n      'search_title' => '[第{page}页{join}][{keyword}{join}][{param}{join}]{SITE_NAME}',\n      'search_keywords' => '',\n      'search_description' => '',\n      'module_title' => '',\n      'module_keywords' => '',\n      'module_description' => '',\n    ),\n  ),\n  'config' => \n  array (\n    'type' => 'module',\n    'name' => '网址',\n    'icon' => 'fa fa-navicon',\n    'system' => '1',\n  ),\n  'share' => '0',\n  'setting' => \n  array (\n    'module_index_html' => 1,\n    'sync_category' => '0',\n    'pcatpost' => '0',\n    'updatetime_select' => '0',\n    'merge' => '0',\n    'right_field' => '0',\n    'desc_auto' => '0',\n    'desc_limit' => '100',\n    'kws_limit' => '10',\n    'desc_clear' => '0',\n    'hits_min' => '',\n    'hits_max' => '',\n    'verify_num' => '10',\n    'verify_msg' => '',\n    'delete_msg' => '',\n    'is_hide_search_bar' => '0',\n    'order' => 'updatetime DESC',\n    'search_time' => 'updatetime',\n    'search_first_field' => 'title',\n    'is_op_more' => '0',\n    'list_field' => \n    array (\n      'id' => \n      array (\n        'use' => '1',\n        'name' => 'Id',\n        'width' => '80',\n        'func' => '',\n      ),\n      'title' => \n      array (\n        'use' => '1',\n        'name' => '主题',\n        'width' => '',\n        'func' => 'title',\n      ),\n      'catid' => \n      array (\n        'use' => '1',\n        'name' => '栏目',\n        'width' => '130',\n        'func' => 'catid',\n      ),\n      'catids' => \n      array (\n        'use' => '1',\n        'name' => '副栏目',\n        'width' => '',\n        'func' => 'catids',\n      ),\n      'keywords' => \n      array (\n        'use' => '1',\n        'name' => '标签',\n        'width' => '',\n        'func' => '',\n      ),\n      'author' => \n      array (\n        'use' => '1',\n        'name' => '笔名',\n        'width' => '120',\n        'func' => 'author',\n      ),\n      'updatetime' => \n      array (\n        'use' => '1',\n        'name' => '更新时间',\n        'width' => '160',\n        'func' => 'datetime',\n      ),\n    ),\n    'flag' => \n    array (\n      1 => \n      array (\n        'name' => '热门推荐',\n        'icon' => 'fa fa-fire',\n        'role' => \n        array (\n        ),\n      ),\n    ),\n    'param' => NULL,\n    'search' => \n    array (\n      'use' => '1',\n      'catsync' => '0',\n      'indexsync' => '0',\n      'show_seo' => '0',\n      'search_404' => '0',\n      'search_param' => '0',\n      'complete' => '0',\n      'is_like' => '0',\n      'is_double_like' => '0',\n      'max' => '0',\n      'length' => '1',\n      'maxlength' => '0',\n      'param_join' => '-',\n      'param_rule' => '0',\n      'param_field' => '',\n      'param_join_field' => \n      array (\n        0 => '',\n        1 => '',\n        2 => '',\n        3 => '',\n        4 => '',\n        5 => '',\n        6 => '',\n        7 => '',\n        8 => '',\n        9 => '',\n        10 => '',\n      ),\n      'param_join_default_value' => '0',\n      'tpl_field' => '',\n      'field' => 'title,keywords',\n    ),\n  ),\n  'dirname' => 'webnav',\n  'domain' => '',\n  'mobile_domain' => '',\n  'cname' => '网址',\n  'html' => '0',\n  'title' => '网址',\n  'urlrule' => '4',\n  'url' => '/webnav.html',\n  'murl' => 'https://www.kuhao123.com/webnav.html',\n  'field' => \n  array (\n    'catids' => \n    array (\n      'id' => '17',\n      'name' => '副栏目',\n      'fieldname' => 'catids',\n      'fieldtype' => 'Catids',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'option' => \n        array (\n          'width' => '',\n          'css' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '-10',\n    ),\n    'title' => \n    array (\n      'id' => '9',\n      'name' => '主题',\n      'fieldname' => 'title',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'width' => 400,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n          'required' => 1,\n          'formattr' => 'onblur=\"check_title();get_keywords(\\'keywords\\');\"',\n        ),\n      ),\n      'displayorder' => '-5',\n    ),\n    'tubiao' => \n    array (\n      'id' => '16',\n      'name' => '图标',\n      'fieldname' => 'tubiao',\n      'fieldtype' => 'File',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '300',\n          'input' => '1',\n          'ext' => 'jpg,gif,png,jpeg',\n          'size' => '1',\n          'chunk' => '1',\n          'attachment' => '0',\n          'image_reduce' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '-2',\n    ),\n    'thumb' => \n    array (\n      'id' => '10',\n      'name' => '缩略图',\n      'fieldname' => 'thumb',\n      'fieldtype' => 'File',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'ext' => 'jpg,gif,png,jpeg',\n          'size' => '1',\n          'attachment' => '0',\n          'image_reduce' => '',\n          'is_ext_tips' => '0',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'keywords' => \n    array (\n      'id' => '11',\n      'name' => '标签',\n      'fieldname' => 'keywords',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'value' => '',\n          'width' => '400',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => ' data-role=\"tagsinput\"',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'description' => \n    array (\n      'id' => '12',\n      'name' => '描述',\n      'fieldname' => 'description',\n      'fieldtype' => 'Textarea',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'width' => 500,\n          'height' => 60,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n          'filter' => 'dr_filter_description',\n        ),\n      ),\n      'displayorder' => '0',\n    ),\n    'author' => \n    array (\n      'id' => '13',\n      'name' => '笔名',\n      'fieldname' => 'author',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '1',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'is_right' => 1,\n        'option' => \n        array (\n          'width' => 200,\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '255',\n          'value' => '{name}',\n        ),\n        'validate' => \n        array (\n          'xss' => 1,\n        ),\n      ),\n      'displayorder' => '0',\n    ),\n    'content' => \n    array (\n      'id' => '14',\n      'name' => '内容',\n      'fieldname' => 'content',\n      'fieldtype' => 'Editor',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '0',\n      'issystem' => '1',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'show_bottom_boot' => '1',\n          'tool_select_3' => '1',\n          'imgtitle' => '0',\n          'imgalt' => '0',\n          'watermark' => '0',\n          'image_ext' => 'jpg,gif,png,webp,jpeg',\n          'image_size' => '2',\n          'attach_size' => '200',\n          'attach_ext' => 'zip,rar,txt,doc',\n          'video_ext' => 'mp4',\n          'video_size' => '500',\n          'attachment' => '0',\n          'value' => '',\n          'width' => '100%',\n          'height' => '400',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '1',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n    'website' => \n    array (\n      'id' => '26',\n      'name' => '官网',\n      'fieldname' => 'website',\n      'fieldtype' => 'Text',\n      'relatedid' => '2',\n      'relatedname' => 'module',\n      'isedit' => '1',\n      'ismain' => '1',\n      'issystem' => '0',\n      'ismember' => '1',\n      'issearch' => '0',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'option' => \n        array (\n          'fieldtype' => 'VARCHAR',\n          'fieldlength' => '300',\n          'value' => '',\n          'width' => '100%',\n          'css' => '',\n        ),\n        'validate' => \n        array (\n          'xss' => '1',\n          'required' => '0',\n          'pattern' => '',\n          'errortips' => '',\n          'check' => '',\n          'filter' => '',\n          'tips' => '',\n          'formattr' => '',\n        ),\n        'is_right' => '0',\n      ),\n      'displayorder' => '0',\n    ),\n  ),\n  'system' => '1',\n  'category_data_field' => \n  array (\n  ),\n  'category' => \n  array (\n    0 => 'webnav',\n  ),\n  'form' => \n  array (\n    'fankui' => \n    array (\n      'id' => '2',\n      'name' => '反馈',\n      'table' => 'fankui',\n      'module' => 'webnav',\n      'disabled' => '0',\n      'setting' => \n      array (\n        'seo' => \n        array (\n          'title' => '{title}{join}{formname}{join}{SITE_NAME}',\n          'keywords' => '',\n          'description' => '',\n        ),\n        'icon' => '',\n        'is_read' => '0',\n        'is_close_post' => '0',\n        'is_post_code' => '1',\n        'is_verify' => '0',\n        'rt_text' => '',\n        'rt_text2' => '',\n        'rt_url' => '',\n        'order' => 'inputtime DESC',\n        'search_time' => 'inputtime',\n        'list_field' => \n        array (\n          'title' => \n          array (\n            'use' => '1',\n            'name' => '主题',\n            'width' => '0',\n            'func' => 'title',\n          ),\n          'uid' => \n          array (\n            'use' => '1',\n            'name' => '账号',\n            'width' => '100',\n            'func' => 'uid',\n          ),\n          'inputtime' => \n          array (\n            'use' => '1',\n            'name' => '录入时间',\n            'width' => '160',\n            'func' => 'datetime',\n          ),\n        ),\n      ),\n      'field' => \n      array (\n        'title' => \n        array (\n          'id' => '39',\n          'name' => '主题',\n          'fieldname' => 'title',\n          'fieldtype' => 'Text',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '1',\n          'ismember' => '1',\n          'issearch' => '1',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'option' => \n            array (\n              'width' => 300,\n              'fieldtype' => 'VARCHAR',\n              'fieldlength' => '255',\n            ),\n            'validate' => \n            array (\n              'xss' => 1,\n              'required' => 1,\n            ),\n          ),\n          'displayorder' => '0',\n        ),\n        'author' => \n        array (\n          'id' => '40',\n          'name' => '作者',\n          'fieldname' => 'author',\n          'fieldtype' => 'Text',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '1',\n          'ismember' => '1',\n          'issearch' => '1',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'is_right' => 1,\n            'option' => \n            array (\n              'width' => 200,\n              'fieldtype' => 'VARCHAR',\n              'fieldlength' => '255',\n            ),\n            'validate' => \n            array (\n              'xss' => 1,\n            ),\n          ),\n          'displayorder' => '0',\n        ),\n        'fknr' => \n        array (\n          'id' => '41',\n          'name' => '反馈内容',\n          'fieldname' => 'fknr',\n          'fieldtype' => 'Textarea',\n          'relatedid' => '2',\n          'relatedname' => 'mform-webnav',\n          'isedit' => '1',\n          'ismain' => '1',\n          'issystem' => '0',\n          'ismember' => '1',\n          'issearch' => '0',\n          'disabled' => '0',\n          'setting' => \n          array (\n            'option' => \n            array (\n              'value' => '',\n              'fieldtype' => 'TEXT',\n              'fieldlength' => '',\n              'width' => '',\n              'height' => '',\n              'css' => '',\n            ),\n            'validate' => \n            array (\n              'xss' => '1',\n              'required' => '0',\n              'pattern' => '',\n              'errortips' => '',\n              'check' => '',\n              'filter' => '',\n              'tips' => '',\n              'formattr' => '',\n            ),\n            'is_right' => '0',\n          ),\n          'displayorder' => '0',\n        ),\n      ),\n    ),\n  ),\n  'mid' => 'webnav',\n  'comment' => 0,\n)"}, {"name": "is_seo", "value": "false"}, {"name": "reply_url", "value": "'admin3faf81d65fd6.php?s=webnav&c=category&m=index'"}, {"name": "is_total", "value": "false"}, {"name": "is_scategory", "value": "false"}, {"name": "is_link_update", "value": "0"}, {"name": "list", "value": "'你在My/View/share_category_list.html，目录中定义过栏目文件，需要删除此文件'"}, {"name": "pcats", "value": "array (\n)"}, {"name": "tcats", "value": "array (\n  0 => '3-webnav',\n  1 => '2-webnav',\n  2 => '6-webnav',\n  3 => '5-webnav',\n)"}, {"name": "cat_head", "value": "'<tr class=\"heading\"><th class=\"myselect\">\n                        <label class=\"mt-table mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline\">\n                            <input type=\"checkbox\" class=\"group-checkable\" data-set=\".checkboxes\" />\n                            <span></span>\n                        </label>\n                    </th><th width=\"70\" style=\"text-align:center\"> 排序 </th><th width=\"50\" style=\"text-align:center\"> 可用 </th><th width=\"50\" style=\"text-align:center\"> 显示 </th><th width=\"70\" style=\"text-align:center\"> 主栏目 </th><th width=\"70\" style=\"text-align:center\"> Id </th><th> 栏目信息 </th><th>操作</th></tr>\n'"}, {"name": "cat_list", "value": "'<tr class=\\'dr_catid_3 dr_pid_0 dr_pid_0\\'><td class=\\'myselect\\'>\n                    <label class=\\'mt-table mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline\\'>\n                        <input type=\\'checkbox\\' class=\\'checkboxes\\' name=\\'ids[]\\' value=\\'3\\' />\n                        <span></span>\n                    </label>\n                </td><td style=\\'text-align:center\\'> <input type=\\'text\\' onblur=\\'dr_cat_ajax_save(this.value, 3)\\' value=\\'1\\' class=\\'displayorder form-control input-sm input-inline input-mini\\'> </td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"禁用状态下此栏目不能正常访问\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&at=used&id=3\\', 1);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"前端循环调用不会显示，但可以正常访问\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&id=3\\', 0);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"主栏目具备权限控制和相关参数配置权限；当栏目过多时建议将第一级设置为主栏目，其余子栏目不设置\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&at=main&id=3\\', 0);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'>3</td><td><a data-container=\\'body\\' data-placement=\\'right\\' data-original-title=\\'ai-video-tools\\' class=\\'tooltips\\' target=\\'_blank\\' href=\\'https://www.kuhao123.com/webnav/ai-video-tools/3.html\\'>AI视频工具</a>  <span class=\"cat-total-3 dr_total\"></span></td><td><a class=\"btn btn-xs blue\" href=admin3faf81d65fd6.php?s=webnav&c=category&m=add&pid=3> <i class=\"fa fa-plus\"></i> 子类</a><a class=\"btn btn-xs green\" href=admin3faf81d65fd6.php?s=webnav&c=category&m=edit&id=3> <i class=\"fa fa-edit\"></i> 修改</a><a class=\"btn btn-xs dark\" href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid=3\"> <i class=\"fa fa-plus\"></i> 发布</a><a class=\"btn btn-xs blue\" href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&catid=3\"> <i class=\"fa fa-th-large\"></i> 管理</a><a class=\"btn btn-xs red\" href=\"javascript:dr_cat_field(3, \\'AI视频工具\\');\"> <i class=\"fa fa-code\"></i> 字段权限</a><a class=\"btn btn-xs yellow\" href=\"javascript:dr_iframe_show(\\'show\\', \\'admin3faf81d65fd6.php?s=mbdy&c=category&m=cms&mid=webnav&id=3\\');\"> <i class=\"fa fa-code\"></i> 前端调用</a></td></tr>\n<tr class=\\'dr_catid_2 dr_pid_0 dr_pid_0\\'><td class=\\'myselect\\'>\n                    <label class=\\'mt-table mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline\\'>\n                        <input type=\\'checkbox\\' class=\\'checkboxes\\' name=\\'ids[]\\' value=\\'2\\' />\n                        <span></span>\n                    </label>\n                </td><td style=\\'text-align:center\\'> <input type=\\'text\\' onblur=\\'dr_cat_ajax_save(this.value, 2)\\' value=\\'2\\' class=\\'displayorder form-control input-sm input-inline input-mini\\'> </td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"禁用状态下此栏目不能正常访问\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&at=used&id=2\\', 1);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"前端循环调用不会显示，但可以正常访问\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&id=2\\', 0);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"主栏目具备权限控制和相关参数配置权限；当栏目过多时建议将第一级设置为主栏目，其余子栏目不设置\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&at=main&id=2\\', 0);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'>2</td><td><a data-container=\\'body\\' data-placement=\\'right\\' data-original-title=\\'ai-image-tools\\' class=\\'tooltips\\' target=\\'_blank\\' href=\\'https://www.kuhao123.com/webnav/ai-image-tools/2.html\\'>AI生图工具</a>  <span class=\"cat-total-2 dr_total\"></span></td><td><a class=\"btn btn-xs blue\" href=admin3faf81d65fd6.php?s=webnav&c=category&m=add&pid=2> <i class=\"fa fa-plus\"></i> 子类</a><a class=\"btn btn-xs green\" href=admin3faf81d65fd6.php?s=webnav&c=category&m=edit&id=2> <i class=\"fa fa-edit\"></i> 修改</a><a class=\"btn btn-xs dark\" href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid=2\"> <i class=\"fa fa-plus\"></i> 发布</a><a class=\"btn btn-xs blue\" href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&catid=2\"> <i class=\"fa fa-th-large\"></i> 管理</a><a class=\"btn btn-xs red\" href=\"javascript:dr_cat_field(2, \\'AI生图工具\\');\"> <i class=\"fa fa-code\"></i> 字段权限</a><a class=\"btn btn-xs yellow\" href=\"javascript:dr_iframe_show(\\'show\\', \\'admin3faf81d65fd6.php?s=mbdy&c=category&m=cms&mid=webnav&id=2\\');\"> <i class=\"fa fa-code\"></i> 前端调用</a></td></tr>\n<tr class=\\'dr_catid_6 dr_pid_0 dr_pid_0\\'><td class=\\'myselect\\'>\n                    <label class=\\'mt-table mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline\\'>\n                        <input type=\\'checkbox\\' class=\\'checkboxes\\' name=\\'ids[]\\' value=\\'6\\' />\n                        <span></span>\n                    </label>\n                </td><td style=\\'text-align:center\\'> <input type=\\'text\\' onblur=\\'dr_cat_ajax_save(this.value, 6)\\' value=\\'3\\' class=\\'displayorder form-control input-sm input-inline input-mini\\'> </td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"禁用状态下此栏目不能正常访问\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&at=used&id=6\\', 1);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"前端循环调用不会显示，但可以正常访问\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&id=6\\', 0);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"主栏目具备权限控制和相关参数配置权限；当栏目过多时建议将第一级设置为主栏目，其余子栏目不设置\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&at=main&id=6\\', 0);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'>6</td><td><a data-container=\\'body\\' data-placement=\\'right\\' data-original-title=\\'ai-audio-tools\\' class=\\'tooltips\\' target=\\'_blank\\' href=\\'https://www.kuhao123.com/webnav/ai-audio-tools/6.html\\'>AI音频工具</a>  <span class=\"cat-total-6 dr_total\"></span></td><td><a class=\"btn btn-xs blue\" href=admin3faf81d65fd6.php?s=webnav&c=category&m=add&pid=6> <i class=\"fa fa-plus\"></i> 子类</a><a class=\"btn btn-xs green\" href=admin3faf81d65fd6.php?s=webnav&c=category&m=edit&id=6> <i class=\"fa fa-edit\"></i> 修改</a><a class=\"btn btn-xs dark\" href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid=6\"> <i class=\"fa fa-plus\"></i> 发布</a><a class=\"btn btn-xs blue\" href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&catid=6\"> <i class=\"fa fa-th-large\"></i> 管理</a><a class=\"btn btn-xs red\" href=\"javascript:dr_cat_field(6, \\'AI音频工具\\');\"> <i class=\"fa fa-code\"></i> 字段权限</a><a class=\"btn btn-xs yellow\" href=\"javascript:dr_iframe_show(\\'show\\', \\'admin3faf81d65fd6.php?s=mbdy&c=category&m=cms&mid=webnav&id=6\\');\"> <i class=\"fa fa-code\"></i> 前端调用</a></td></tr>\n<tr class=\\'dr_catid_5 dr_pid_0 dr_pid_0\\'><td class=\\'myselect\\'>\n                    <label class=\\'mt-table mt-table mt-checkbox mt-checkbox-single mt-checkbox-outline\\'>\n                        <input type=\\'checkbox\\' class=\\'checkboxes\\' name=\\'ids[]\\' value=\\'5\\' />\n                        <span></span>\n                    </label>\n                </td><td style=\\'text-align:center\\'> <input type=\\'text\\' onblur=\\'dr_cat_ajax_save(this.value, 5)\\' value=\\'4\\' class=\\'displayorder form-control input-sm input-inline input-mini\\'> </td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"禁用状态下此栏目不能正常访问\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&at=used&id=5\\', 1);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"前端循环调用不会显示，但可以正常访问\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&id=5\\', 0);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'><a data-container=\"body\" data-placement=\"right\" data-original-title=\"主栏目具备权限控制和相关参数配置权限；当栏目过多时建议将第一级设置为主栏目，其余子栏目不设置\" href=\"javascript:;\" onclick=\"dr_cat_ajax_show_open_close(this, \\'admin3faf81d65fd6.php?s=webnav&c=category&m=show_edit&at=main&id=5\\', 0);\" class=\"tooltips badge badge-yes\"><i class=\"fa fa-check\"></i></a></td><td style=\\'text-align:center\\'>5</td><td><a data-container=\\'body\\' data-placement=\\'right\\' data-original-title=\\'ai-chatbots\\' class=\\'tooltips\\' target=\\'_blank\\' href=\\'https://www.kuhao123.com/webnav/ai-chatbots/5.html\\'>AI视频文案</a>  <span class=\"cat-total-5 dr_total\"></span></td><td><a class=\"btn btn-xs blue\" href=admin3faf81d65fd6.php?s=webnav&c=category&m=add&pid=5> <i class=\"fa fa-plus\"></i> 子类</a><a class=\"btn btn-xs green\" href=admin3faf81d65fd6.php?s=webnav&c=category&m=edit&id=5> <i class=\"fa fa-edit\"></i> 修改</a><a class=\"btn btn-xs dark\" href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=add&catid=5\"> <i class=\"fa fa-plus\"></i> 发布</a><a class=\"btn btn-xs blue\" href=\"admin3faf81d65fd6.php?s=webnav&c=home&m=index&catid=5\"> <i class=\"fa fa-th-large\"></i> 管理</a><a class=\"btn btn-xs red\" href=\"javascript:dr_cat_field(5, \\'AI视频文案\\');\"> <i class=\"fa fa-code\"></i> 字段权限</a><a class=\"btn btn-xs yellow\" href=\"javascript:dr_iframe_show(\\'show\\', \\'admin3faf81d65fd6.php?s=mbdy&c=category&m=cms&mid=webnav&id=5\\');\"> <i class=\"fa fa-code\"></i> 前端调用</a></td></tr>\n'"}, {"name": "list_url", "value": "'admin3faf81d65fd6.php?s=webnav&c=category&m=index'"}, {"name": "list_name", "value": "' <i class=\"fa fa-reorder\"></i>  栏目管理'"}, {"name": "move_select", "value": "'<select class=\"bs-select form-control\" name=\"catid\">\n<option value=\\'0\\'>顶级栏目</option>\n<option _selected_3_ value=\\'3\\'>AI视频工具</option>\n<option _selected_2_ value=\\'2\\'>AI生图工具</option>\n<option _selected_6_ value=\\'6\\'>AI音频工具</option>\n<option _selected_5_ value=\\'5\\'>AI视频文案</option>\n</select>\n<script type=\"text/javascript\"> var bs_selectAllText = \\'全选\\';var bs_deselectAllText = \\'全删\\';var bs_noneSelectedText = \\'没有选择\\'; var bs_noneResultsText = \\'没有找到 {0}\\';</script>\n<link href=\"/static/assets/global/plugins/bootstrap-select/css/bootstrap-select.css\" rel=\"stylesheet\" type=\"text/css\" />\n<script src=\"/static/assets/global/plugins/bootstrap-select/js/bootstrap-select.js\" type=\"text/javascript\"></script>\n<script type=\"text/javascript\"> jQuery(document).ready(function() { $(\\'.bs-select\\').selectpicker();  }); </script>'"}, {"name": "uriprefix", "value": "'webnav/category'"}, {"name": "my_web_url", "value": "'/admin3faf81d65fd6.php?s=webnav&c=category&m=index'"}, {"name": "get", "value": "array (\n  's' => 'webnav',\n  'c' => 'category',\n  'm' => 'index',\n)"}], "tips": [{"name": "share_category_list.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/share_category_list.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/App/Module/Views/share_category_list.html]！"}, {"name": "header.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/header.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/header.html]"}, {"name": "head.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/head.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/head.html]"}, {"name": "category_btn.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/category_btn.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/App/Module/Views/category_btn.html]！"}, {"name": "footer.html", "tips": "由于模板文件[/home/<USER>/web/dayrui/App/Webnav/Views/footer.html]不存在，因此本页面引用主目录的模板[/home/<USER>/web/dayrui/Fcms/View/footer.html]"}], "times": [{"tpl": 0.01}], "files": {"/home/<USER>/web/dayrui/App/Module/Views/share_category_list.html": {"name": "share_category_list.html", "path": "/home/<USER>/web/dayrui/App/Module/Views/share_category_list.html"}, "/home/<USER>/web/dayrui/Fcms/View/header.html": {"name": "header.html", "path": "/home/<USER>/web/dayrui/Fcms/View/header.html"}, "/home/<USER>/web/dayrui/Fcms/View/head.html": {"name": "head.html", "path": "/home/<USER>/web/dayrui/Fcms/View/head.html"}, "/home/<USER>/web/dayrui/App/Module/Views/category_btn.html": {"name": "category_btn.html", "path": "/home/<USER>/web/dayrui/App/Module/Views/category_btn.html"}, "/home/<USER>/web/dayrui/Fcms/View/footer.html": {"name": "footer.html", "path": "/home/<USER>/web/dayrui/Fcms/View/footer.html"}}}, "badgeValue": 5, "isEmpty": false, "hasTabContent": true, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 194 )", "display": {"coreFiles": [], "userFiles": [{"path": "/home/<USER>/web/cache/config/domain_app.php", "name": "domain_app.php"}, {"path": "/home/<USER>/web/cache/config/domain_client.php", "name": "domain_client.php"}, {"path": "/home/<USER>/web/cache/config/site.php", "name": "site.php"}, {"path": "/home/<USER>/web/cache/config/system.php", "name": "system.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_App_DS_Module_DS_Views_DS_category_btn.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_App_DS_Module_DS_Views_DS_category_btn.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_App_DS_Module_DS_Views_DS_share_category_list.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_App_DS_Module_DS_Views_DS_share_category_list.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_footer.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_head.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_head.html.cache.php"}, {"path": "/home/<USER>/web/cache/template/_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_header.html.cache.php", "name": "_DS_home_DS_www.kuhao123.com_DS_web_DS_dayrui_DS_Fcms_DS_View_DS_header.html.cache.php"}, {"path": "/home/<USER>/web/dayrui/App/Form/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mbdy/Models/Code.php", "name": "Code.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Mform/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Auto.php", "name": "Auto.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Module_init.php", "name": "Module_init.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Config/Run.php", "name": "Run.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Extends/Admin/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Libraries/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Models/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Models/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/App/Module/Models/Repair.php", "name": "Repair.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Safe/Models/Login.php", "name": "Login.php"}, {"path": "/home/<USER>/web/dayrui/App/Tag/Config/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Autoload.php", "name": "Autoload.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Constants.php", "name": "Constants.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Feature.php", "name": "Feature.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Hook.php", "name": "Hook.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Extend/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Common.php", "name": "Common.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/BaseService.php", "name": "BaseService.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factories.php", "name": "Factories.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Factory.php", "name": "Factory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Routing.php", "name": "Routing.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Config/Services.php", "name": "Services.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Controller.php", "name": "Controller.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Config.php", "name": "Config.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/Query.php", "name": "Query.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Timer.php", "name": "Timer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Events/Events.php", "name": "Events.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Filters/Filters.php", "name": "Filters.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Header.php", "name": "Header.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Message.php", "name": "Message.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Request.php", "name": "Request.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/Response.php", "name": "Response.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/URI.php", "name": "URI.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/Time.php", "name": "Time.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Log/Logger.php", "name": "Logger.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Modules/Modules.php", "name": "Modules.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouter.php", "name": "AutoRouter.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Security/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Security/SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/Session.php", "name": "Session.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Superglobals.php", "name": "Superglobals.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Escaper/Escaper.php", "name": "Escaper.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LogLevel.php", "name": "LogLevel.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/ThirdParty/PSR/Log/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "/home/<USER>/web/dayrui/CodeIgniter/System/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Config/Routes.php", "name": "Routes.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Helper.php", "name": "Helper.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Hooks.php", "name": "Hooks.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Model.php", "name": "Model.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Phpcmf.php", "name": "Phpcmf.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Service.php", "name": "Service.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/Table.php", "name": "Table.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Core/View.php", "name": "View.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Field/Select.php", "name": "Select.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Init.php", "name": "Init.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Cache.php", "name": "Cache.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Field.php", "name": "Field.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Input.php", "name": "Input.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Lang.php", "name": "Lang.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Router.php", "name": "Router.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Security.php", "name": "Security.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Library/Tree.php", "name": "Tree.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/App.php", "name": "App.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Auth.php", "name": "Auth.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Category.php", "name": "Category.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Content.php", "name": "Content.php"}, {"path": "/home/<USER>/web/dayrui/Fcms/Model/Member.php", "name": "Member.php"}, {"path": "/home/<USER>/web/dayrui/My/Config/License.php", "name": "License.php"}, {"path": "/home/<USER>/web/dayrui/My/Config/Version.php", "name": "Version.php"}, {"path": "/home/<USER>/web/public/admin3faf81d65fd6.php", "name": "admin3faf81d65fd6.php"}, {"path": "/home/<USER>/web/public/api/language/zh-cn/lang.php", "name": "lang.php"}, {"path": "/home/<USER>/web/public/config/custom.php", "name": "custom.php"}, {"path": "/home/<USER>/web/public/config/database.php", "name": "database.php"}, {"path": "/home/<USER>/web/public/config/hooks.php", "name": "hooks.php"}, {"path": "/home/<USER>/web/public/index.php", "name": "index.php"}]}, "badgeValue": 194, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"uri": "webnav/category/index", "url": "/admin3faf81d65fd6.php?s=webnav&c=category&m=index", "app": "webnav", "controller": "category", "method": "index", "file": "/home/<USER>/web/dayrui/App/Webnav/Controllers/Admin/Category.php"}], "get": {"s": "webnav", "c": "category", "m": "index"}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "5.36", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.61", "count": 17}}}, "badgeValue": 18, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.27781, "duration": 0.005363941192626953}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.3174, "duration": 3.695487976074219e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.318975, "duration": 7.796287536621094e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.330534, "duration": 3.2901763916015625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.331198, "duration": 2.4080276489257812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.332947, "duration": 3.409385681152344e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.333935, "duration": 4.601478576660156e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.34143, "duration": 3.1948089599609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.343728, "duration": 3.0994415283203125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.344536, "duration": 3.0040740966796875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.346028, "duration": 2.8848648071289062e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.346971, "duration": 5.2928924560546875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.3476, "duration": 2.4080276489257812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.349264, "duration": 3.0040740966796875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.350017, "duration": 2.5987625122070312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.351152, "duration": 3.409385681152344e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.352092, "duration": 4.100799560546875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.35312, "duration": 2.6941299438476562e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1750910995</pre>", "uid": "1", "_ci_previous_url": "https://www.kuhao123.com/admin3faf81d65fd6.php?s=webnav&amp;c=home&amp;m=index", "auth_csrf_token": "c4b7ab6cf996162298be3d0173150d38"}, "get": {"s": "webnav", "c": "category", "m": "index"}, "headers": {"Cookie": "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJtbFh0amEzdWZFVnZpZ2JMdEZsZEE9PSIsInZhbHVlIjoiN0tlRlFRY1YyUFJQVnJDSStPVldLc1NPMldxblJYbW1PbnlZLzBMSlhXOUNnUkxCN0tzZnhYbTExZnBabmJZRTcva0lyazFzUUozT1NZMG96N2Z5RnUzZE5CM1lXQml2a2V5UTdoSUNuVU9nbXgyeGlSVDd4anBxd3R3R2hFODBEQmx4bUN4YXYxWHdLWXJGUGljMGVIb0dRam85Zk16Mk1xUytBMW0vcUpxeFQ2SHM0VVZvQjgybUlJTFpyTU5KRFdraWdjRG1pZzZ2eDZwSGs5ZWRQbVEwbGRYTWVab3pSY3Qyb043bkdkUT0iLCJtYWMiOiJhNjM0ZDIzYmYxZmJmOTM4YTA5ZDU5YzZlMWI0ZGFmNjQ2OTM1NjA3YmZmYmNiNTRhZDRmZjk2YTlmNWEyMmI5IiwidGFnIjoiIn0%3D; PHPSESSID=kjb18ivsdqceau93qdhn34vd0d; d41d8cd98f00b204e9800998ecf8427e_member_uid=1; d41d8cd98f00b204e9800998ecf8427e_member_cookie=5424b2695385ffdccec8d972124ec65f; csrf_cookie_name=cd8376769004a149d672615f02225890; ci_session=bfsq0jfq4aau88p4hrnbre22m219kqkh; d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-638=638", "Priority": "u=0, i", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8", "Accept-Encoding": "gzip, deflate, br, zstd", "Referer": "https://www.kuhao123.com/admin3faf81d65fd6.php?s=webnav&amp;c=home&amp;m=index", "Sec-Fetch-Dest": "iframe", "Sec-Fetch-User": "?1", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "same-origin", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Upgrade-Insecure-Requests": "1", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Host": "www.kuhao123.com"}, "cookies": {"remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6ImJtbFh0amEzdWZFVnZpZ2JMdEZsZEE9PSIsInZhbHVlIjoiN0tlRlFRY1YyUFJQVnJDSStPVldLc1NPMldxblJYbW1PbnlZLzBMSlhXOUNnUkxCN0tzZnhYbTExZnBabmJZRTcva0lyazFzUUozT1NZMG96N2Z5RnUzZE5CM1lXQml2a2V5UTdoSUNuVU9nbXgyeGlSVDd4anBxd3R3R2hFODBEQmx4bUN4YXYxWHdLWXJGUGljMGVIb0dRam85Zk16Mk1xUytBMW0vcUpxeFQ2SHM0VVZvQjgybUlJTFpyTU5KRFdraWdjRG1pZzZ2eDZwSGs5ZWRQbVEwbGRYTWVab3pSY3Qyb043bkdkUT0iLCJtYWMiOiJhNjM0ZDIzYmYxZmJmOTM4YTA5ZDU5YzZlMWI0ZGFmNjQ2OTM1NjA3YmZmYmNiNTRhZDRmZjk2YTlmNWEyMmI5IiwidGFnIjoiIn0=", "PHPSESSID": "kjb18ivsdqceau93qdhn34vd0d", "d41d8cd98f00b204e9800998ecf8427e_member_uid": "1", "d41d8cd98f00b204e9800998ecf8427e_member_cookie": "5424b2695385ffdccec8d972124ec65f", "csrf_cookie_name": "cd8376769004a149d672615f02225890", "ci_session": "bfsq0jfq4aau88p4hrnbre22m219kqkh", "d41d8cd98f00b204e9800998ecf8427e_module-0fb0a5da7b11e060ac2a5791916b8c52-638": "638"}, "request": "HTTPS/2.0", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.7", "phpVersion": "8.2.28", "phpSAPI": "fpm-fcgi", "environment": "development", "baseURL": "https://www.kuhao123.com/", "timezone": "PRC", "locale": "zh-cn", "cspEnabled": false}}