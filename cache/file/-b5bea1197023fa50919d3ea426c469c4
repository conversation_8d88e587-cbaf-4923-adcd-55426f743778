a:3:{s:4:"time";i:1750911857;s:3:"ttl";i:60;s:4:"data";a:7:{s:4:"data";a:9:{i:0;a:26:{s:2:"id";s:3:"402";s:5:"catid";s:1:"5";s:5:"title";s:12:"文心一言";s:5:"thumb";s:3:"930";s:8:"keywords";s:14:"AI对话工具";s:11:"description";s:59:"百度推出的基于文心大模型的AI对话互动工具";s:4:"hits";s:2:"68";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/402.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:3:"931";s:6:"catids";a:0:{}s:7:"website";s:45:"https://yiyan.baidu.com/?utm_source=ai-bot.cn";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702550189";s:11:"_updatetime";s:10:"1702550189";s:7:"_catids";N;}i:1;a:26:{s:2:"id";s:3:"443";s:5:"catid";s:1:"5";s:5:"title";s:7:"Copilot";s:5:"thumb";s:4:"1032";s:8:"keywords";s:14:"AI对话工具";s:11:"description";s:37:"微软推出的网页版Copilot助手";s:4:"hits";s:2:"88";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/443.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:4:"1033";s:6:"catids";a:0:{}s:7:"website";s:30:"https://copilot.microsoft.com/";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702548915";s:11:"_updatetime";s:10:"1702548915";s:7:"_catids";N;}i:2;a:26:{s:2:"id";s:3:"442";s:5:"catid";s:1:"5";s:5:"title";s:7:"ChatGPT";s:5:"thumb";s:4:"1030";s:8:"keywords";s:29:"AI对话工具,常用AI工具";s:11:"description";s:26:"OpenAI旗下AI对话工具";s:4:"hits";s:2:"55";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/442.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:4:"1031";s:6:"catids";a:0:{}s:7:"website";s:47:"https://cn.bing.com/search?q=ChatGPT&ensearch=1";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702548898";s:11:"_updatetime";s:10:"1702548898";s:7:"_catids";N;}i:3;a:26:{s:2:"id";s:3:"439";s:5:"catid";s:1:"5";s:5:"title";s:11:"Google Bard";s:5:"thumb";s:4:"1023";s:8:"keywords";s:14:"AI对话工具";s:11:"description";s:42:"Google推出的AI聊天对话机器人Bard";s:4:"hits";s:2:"96";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/439.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:4:"1024";s:6:"catids";a:0:{}s:7:"website";s:51:"https://cn.bing.com/search?q=Google Bard&ensearch=1";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702548887";s:11:"_updatetime";s:10:"1702548887";s:7:"_catids";N;}i:4;a:26:{s:2:"id";s:3:"437";s:5:"catid";s:1:"5";s:5:"title";s:12:"紫东太初";s:5:"thumb";s:4:"1018";s:8:"keywords";s:14:"AI对话工具";s:11:"description";s:69:"中科院与武智院推出的千亿参数全模态大模型和助手";s:4:"hits";s:2:"82";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/437.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:4:"1019";s:6:"catids";a:0:{}s:7:"website";s:37:"https://taichu-web.ia.ac.cn/#/welcome";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702548878";s:11:"_updatetime";s:10:"1702548878";s:7:"_catids";N;}i:5;a:26:{s:2:"id";s:3:"441";s:5:"catid";s:1:"5";s:5:"title";s:13:"Bing新必应";s:5:"thumb";s:4:"1028";s:8:"keywords";s:14:"AI对话工具";s:11:"description";s:52:"微软推出的新版结合了ChatGPT功能的必应";s:4:"hits";s:2:"25";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/441.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:3:"884";s:6:"catids";a:0:{}s:7:"website";s:53:"https://cn.bing.com/search?q=Bing新必应&ensearch=1";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702548854";s:11:"_updatetime";s:10:"1702548854";s:7:"_catids";N;}i:6;a:26:{s:2:"id";s:3:"432";s:5:"catid";s:1:"5";s:5:"title";s:12:"智谱清言";s:5:"thumb";s:4:"1004";s:8:"keywords";s:14:"AI对话工具";s:11:"description";s:52:"智谱AI推出的生成式AI助手，基于ChatGLM 2";s:4:"hits";s:2:"62";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/432.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:4:"1005";s:6:"catids";a:0:{}s:7:"website";s:41:"https://chatglm.cn/pureDisplay?fr=mkazb01";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702548838";s:11:"_updatetime";s:10:"1702548838";s:7:"_catids";N;}i:7;a:26:{s:2:"id";s:3:"438";s:5:"catid";s:1:"5";s:5:"title";s:6:"Claude";s:5:"thumb";s:4:"1021";s:8:"keywords";s:14:"AI对话工具";s:11:"description";s:43:"ChatGPT的最为有力的竞争对手之一";s:4:"hits";s:2:"17";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/438.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:4:"1022";s:6:"catids";a:0:{}s:7:"website";s:46:"https://cn.bing.com/search?q=Claude&ensearch=1";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702548823";s:11:"_updatetime";s:10:"1702548823";s:7:"_catids";N;}i:8;a:26:{s:2:"id";s:3:"433";s:5:"catid";s:1:"5";s:5:"title";s:6:"豆包";s:5:"thumb";s:4:"1007";s:8:"keywords";s:14:"AI对话工具";s:11:"description";s:47:"字节跳动最新推出的免费AI对话助手";s:4:"hits";s:2:"27";s:3:"uid";s:1:"1";s:6:"author";s:9:"创始人";s:6:"status";s:1:"9";s:3:"url";s:57:"https://www.kuhao123.com/webnav/ai-chatbots/show/433.html";s:7:"link_id";s:1:"0";s:7:"tableid";s:1:"0";s:7:"inputip";s:9:"127.0.0.1";s:9:"inputtime";s:10:"2023-12-14";s:10:"updatetime";s:10:"2023-12-14";s:12:"displayorder";s:1:"0";s:6:"tubiao";s:4:"1008";s:6:"catids";a:0:{}s:7:"website";s:44:"https://www.doubao.com/?utm_source=ai-bot.cn";s:7:"support";s:1:"0";s:6:"oppose";s:1:"0";s:12:"fankui_total";s:1:"0";s:10:"_inputtime";s:10:"1702548803";s:11:"_updatetime";s:10:"1702548803";s:7:"_catids";N;}}s:3:"sql";s:273:"SELECT * FROM `dr_1_webnav` WHERE `dr_1_webnav`.`id` <> 440 AND ((`title` LIKE "%AI对话工具%" OR `keywords` LIKE "%AI对话工具%") OR (`title` LIKE "%热门AI办公工具%" OR `keywords` LIKE "%热门AI办公工具%")) ORDER BY `dr_1_webnav`.`updatetime` DESC LIMIT 9";s:5:"total";N;s:5:"pages";s:0:"";s:8:"pagesize";N;s:9:"page_used";i:0;s:12:"page_urlrule";s:0:"";}}