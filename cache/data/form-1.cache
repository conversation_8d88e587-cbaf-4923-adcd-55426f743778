{"yqlj": {"id": "1", "name": "友情链接", "table": "yqlj", "setting": {"web": "0", "post_add": ["1", "1"], "post_verify": ["1", "1"], "post_code": ["1", "1"], "seo": {"title": "{title}{join}{formname}{join}{SITE_NAME}", "keywords": "", "description": ""}, "icon": "bi bi-link", "dev": "1", "rt_text": "提交成功", "rt_text2": "提交成功，等待管理员审核", "rt_url": "", "is_hide_search_bar": "0", "list_field": {"id": {"use": "1", "name": "Id", "width": "80", "func": ""}, "title": {"use": "1", "name": "主题", "width": "150", "func": "title"}, "href": {"use": "1", "name": "链接地址", "width": "", "func": ""}, "jianjie": {"use": "1", "name": "简介", "width": "", "func": ""}, "logo": {"use": "1", "name": "logo地址", "width": "", "func": ""}, "inputip": {"use": "1", "name": "客户端IP", "width": "", "func": ""}, "inputtime": {"use": "1", "name": "录入时间", "width": "160", "func": "datetime"}, "displayorder": {"use": "1", "name": "排列值", "width": "", "func": ""}, "author": {"use": "1", "name": "作者", "width": "", "func": ""}}, "notice": {"is_send": "0", "username": ""}}, "field": {"title": {"id": "50", "name": "链接名称", "fieldname": "title", "fieldtype": "Text", "relatedid": "1", "relatedname": "form-1", "isedit": "1", "ismain": "1", "issystem": "1", "ismember": "1", "issearch": "0", "disabled": "0", "setting": {"option": {"fieldtype": "VARCHAR", "fieldlength": "255", "value": "", "width": "300", "css": ""}, "validate": {"xss": "1", "required": "1", "pattern": "", "errortips": "", "check": "", "filter": "", "tips": "", "formattr": ""}, "is_right": "0"}, "displayorder": "0"}, "author": {"id": "51", "name": "作者", "fieldname": "author", "fieldtype": "Text", "relatedid": "1", "relatedname": "form-1", "isedit": "1", "ismain": "1", "issystem": "1", "ismember": "1", "issearch": "1", "disabled": "0", "setting": {"is_right": 1, "option": {"width": 200, "fieldtype": "VARCHAR", "fieldlength": "255"}, "validate": {"xss": 1}}, "displayorder": "0"}, "href": {"id": "52", "name": "链接地址", "fieldname": "href", "fieldtype": "Text", "relatedid": "1", "relatedname": "form-1", "isedit": "1", "ismain": "1", "issystem": "0", "ismember": "1", "issearch": "0", "disabled": "0", "setting": {"option": {"fieldtype": "VARCHAR", "fieldlength": "300", "value": "", "width": "100%", "css": ""}, "validate": {"xss": "1", "required": "1", "pattern": "/^https?:\\/\\/[^\\s\\/$.?#].[^\\s]*$/", "errortips": "合法的链接地址规则是以http开头", "check": "", "filter": "", "tips": "", "formattr": ""}, "is_right": "0"}, "displayorder": "0"}, "jianjie": {"id": "53", "name": "简介", "fieldname": "jianjie", "fieldtype": "Text", "relatedid": "1", "relatedname": "form-1", "isedit": "1", "ismain": "1", "issystem": "0", "ismember": "1", "issearch": "0", "disabled": "0", "setting": {"option": {"fieldtype": "VARCHAR", "fieldlength": "600", "value": "", "width": "100%", "css": ""}, "validate": {"xss": "1", "required": "0", "pattern": "", "errortips": "", "check": "", "filter": "", "tips": "", "formattr": ""}, "is_right": "0"}, "displayorder": "0"}, "logo": {"id": "54", "name": "logo地址", "fieldname": "logo", "fieldtype": "Text", "relatedid": "1", "relatedname": "form-1", "isedit": "1", "ismain": "1", "issystem": "0", "ismember": "1", "issearch": "0", "disabled": "0", "setting": {"option": {"fieldtype": "VARCHAR", "fieldlength": "300", "value": "", "width": "100%", "css": ""}, "validate": {"required": "1", "pattern": "/^https?:\\/\\/[^\\s\\/$.?#].[^\\s]*$/", "errortips": "不是合法的LOGO地址", "check": "", "filter": "", "tips": "", "formattr": ""}, "is_right": "0"}, "displayorder": "0"}}}}