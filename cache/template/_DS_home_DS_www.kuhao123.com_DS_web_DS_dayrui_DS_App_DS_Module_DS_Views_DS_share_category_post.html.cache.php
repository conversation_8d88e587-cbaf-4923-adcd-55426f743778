<?php if ($fn_include = $this->_include("header.html")) include($fn_include); ?>
<script>
$(function () {
    if (top.$("body").hasClass('page-admin-min') == true) {
        $("#dr_tpl_tab").hide();
    }
    <?php if ($is_scategory) { ?>
    dr_tid(<?php echo intval($data['tid']); ?>);
    <?php } else { ?>
    dr_tid(1);
    <?php } ?>
});
    function dr_cat_field(id, name) {
        dr_iframe('<i class="fa fa-edit"></i> '+name,'<?php echo dr_url(APP_DIR.'/category/field_edit'); ?>&is_yc=1&id='+id, '50%', '70%', 'nogo');
    }
function dr_select_tpl(file, name) {
    $('.'+name).val(file);
}
function dr_tid(i) {
    $('.dr_tid_0').hide();
    $('.dr_tid_1').hide();
    $('.dr_tid_2').hide();
    $('.dr_tid_'+i).show();
}

// 处理post提交
function dr_post_submit(url, form, time, go) {

    var p = url.split('/');
    if ((p[0] == 'http:' || p[0] == 'https:') && document.location.protocol != p[0]) {
        alert('当前提交的URL是'+p[0]+'模式，请使用'+document.location.protocol+'模式访问再提交');
        return;
    }

    url = url.replace(/&page=\d+&page/g, '&page');

    $("#"+form+' .form-group').removeClass('has-error');
    var cms_post_dofunc = "";
    for(var i = 0; i < cms_post_addfunc.length; i++) {
        var cms_post_dofunc = cms_post_addfunc[i];
        var rst = cms_post_dofunc();
        if (rst) {
            dr_cmf_tips(0, rst);
            return;
        }
    }
    var loading = layer.load(2, {
        shade: [0.3,'#fff'], //0.1透明度的白色背景
        time: 100000000
    });
    $.ajax({
        type: "POST",
        dataType: "json",
        url: url,
        data: $("#"+form).serialize(),
        success: function(json) {
            layer.close(loading);
            // token 更新
            if (json.token) {
                var token = json.token;
                $("#"+form+" input[name='"+token.name+"']").val(token.value);
            }
            if (json.code) {
                if (json.data.htmlfile) {
                    // 执行生成htmljs
                    $.ajax({
                        type: "GET",
                        url: json.data.htmlfile,
                        dataType: "jsonp",
                        success: function(json){ },
                        error: function(){ }
                    });
                }
                //
                <?php if ($is_link_update) { ?>
                layer.confirm(
                    '<?php echo dr_lang("需要更新栏目后才会生效，现在更新吗？"); ?>',
                    {
                        icon: 3,
                        shade: 0.3,
                        closeBtn: 0,
                        title: dr_lang('提示'),
                        btn: [dr_lang('立即更新'), dr_lang('稍后更新')]
                    }, function(index){
                        layer.close(index);
                        var width = '500px';
                        var height = '300px';

                        if (is_mobile_cms == 1) {
                            width = '95%';
                            height = '90%';
                        }
                        layer.open({
                            type: 2,
                            title: '<?php echo dr_lang('更新栏目'); ?>',
                            fix:true,
                            scrollbar: false,
                            shadeClose: true,
                            shade: 0.3,
                            area: [width, height],
                            success: function(layero, index){
                                // 主要用于后台权限验证

                                dr_iframe_error(layer, index, 1);
                            },end: function(){
                                setTimeout("window.location.href = '"+go+"'", 2000);
                                dr_cmf_tips(1, json.msg, json.data.time);
                            },
                            content: '<?php echo dr_url('module/api/update_category_repair'); ?>&is_close=1&all=1&mid=<?php echo $dir; ?>&is_iframe=1'
                        });
                    }, function(){
                        setTimeout("window.location.href = '"+go+"'", 2000);
                        dr_cmf_tips(1, json.msg, json.data.time);
                    }
                );
                <?php } else { ?>
                dr_link_ajax_url('<?php echo dr_url('module/api/update_category_repair'); ?>&is_close=1&all=1&mid=<?php echo $dir; ?>', json, go);
                <?php } ?>
                //
            } else {
                dr_cmf_tips(0, json.msg, json.data.time);
                if (json.data.field) {
                    $('#dr_row_'+json.data.field).addClass('has-error');
                    $('#dr_'+json.data.field).focus();
                }
            }
        },
        error: function(HttpRequest, ajaxOptions, thrownError) {
            dr_ajax_alert_error(HttpRequest, this, thrownError);
        }
    });
}

    function dr_link_ajax_url(url, rt, go, index) {
        if (index == undefined) {
            var index = layer.msg("<?php echo dr_lang('正在自动更新栏目缓存，请等待...'); ?>", {time: 999999999});
        }
        $.ajax({
            type: "GET",
            cache: false,
            url: url+"&is_ajax=1",
            dataType: "json",
            success: function (json) {
                if (json.code) {
                    if (json.data) {
                        dr_link_ajax_url(json.data, rt, go, index);
                    } else {
                        if (rt.data.htmlfile) {
                            // 执行生成htmljs
                            $.ajax({
                                type: "GET",
                                url: rt.data.htmlfile,
                                dataType: "jsonp",
                                success: function(json){ },
                                error: function(){ }
                            });
                        }
                        layer.close(index);
                        if (go) {
                            setTimeout("window.location.href = '"+go+"'", 2000);
                        }
                        dr_cmf_tips(1, rt.msg, rt.data.time);
                    }
                } else {
                    dr_cmf_tips(0, json.msg);
                }
            },
            error: function(HttpRequest, ajaxOptions, thrownError) {
                layer.close(index);
            }
        });
    }
</script>

<?php if (isset($_GET['isedit'])) {  } else if (isset($_GET['is_content'])) {  } else { ?>
<div class="note note-danger">
    <p> <?php if ($fn_include = $this->_include("category_btn.html")) include($fn_include); ?></p>
</div>
<?php } ?>

<form action="" class="form-horizontal" method="post" name="myform" id="myform">
    <?php echo $form; ?>
    <input type="hidden" name="system[show]" value="<?php if ($data['show']) { ?>1<?php } else { ?>0<?php } ?>">
    <input type="hidden" name="system[setting][disabled]" value="<?php if ($data['setting']['disabled']) { ?>1<?php } else { ?>0<?php } ?>">
    <div class="myfbody">
    <div class="portlet bordered light ">
        <?php if (!isset($_GET['isedit'])) { ?>
        <div class="portlet-title tabbable-line">
            <ul class="nav nav-tabs" style="float:left;">
                <li class="<?php if ($page==0) { ?>active<?php } ?>">
                    <a href="#tab_0" data-toggle="tab" onclick="$('#dr_page').val('0')"> <i class="fa fa-cog"></i> <?php echo dr_lang('基本设置'); ?> </a>
                </li>
                <?php if ($sysfield || $myfield || $diyfield) { ?>
                <li class="<?php if ($page==1) { ?>active<?php } ?>">
                    <a href="#tab_1" data-toggle="tab" onclick="$('#dr_page').val('1')"> <i class="fa fa-table"></i> <?php echo dr_lang('属性设置'); ?> </a>
                </li>
                <?php }  if ($is_seo) { ?>
                <li class="<?php if ($page==3) { ?>active<?php } ?>">
                    <a href="#tab_3" data-toggle="tab" onclick="$('#dr_page').val('3')"> <i class="fa fa-internet-explorer"></i> <?php echo dr_lang('SEO优化'); ?> </a>
                </li>
                <?php } ?>
                <li id="dr_tpl_tab" class="<?php if ($page==4) { ?>active<?php } ?>">
                    <a href="#tab_4" data-toggle="tab" onclick="$('#dr_page').val('4')"> <i class="fa fa-html5"></i> <?php echo dr_lang('模板设置'); ?> </a>
                </li>
            </ul>
        </div>
        <div class="portlet-body">
        <?php } else { ?>
        <div class="portlet-body" style="margin-top: -30px;<?php if (IS_PC) { ?>margin-left: -30px<?php } ?>">
        <?php } ?>
            <div class="tab-content">

                <div class="tab-pane <?php if ($page==0) { ?>active<?php } ?>" id="tab_0">
                    <div class="form-body">

                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('所属栏目'); ?></label>
                            <div class="col-md-9">
                                <label><?php echo str_replace('data[', 'system[', $select); ?></label>
                            </div>
                        </div>

                        <?php if ($is_scategory) { ?>
                        <div class="form-group r1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('栏目类型'); ?></label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[tid]" value="0" onclick="dr_tid(0)" <?php if (!$data['tid']) { ?> checked<?php } ?> /> <?php echo dr_lang('单网页'); ?> <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[tid]" value="1" onclick="dr_tid(1)" <?php if ($data['tid']==1) { ?> checked<?php } ?> /> <?php echo dr_lang('内容模块'); ?> <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[tid]" value="2" onclick="dr_tid(2)" <?php if ($data['tid']==2) { ?> checked<?php } ?> /> <?php echo dr_lang('外部地址'); ?> <span></span></label>
                                </div>
                            </div>
                        </div>
                        <?php }  if ($module['share']) { ?>
                        <div class="form-group dr_tid_1 " style="display: none">
                            <label class="col-md-2 control-label"><?php echo dr_lang('共享模块'); ?></label>
                            <div class="col-md-9">
                                <label>
                                    <select class="form-control" name="system[mid]">
                                    <option value=""> -- </option>
                                    <?php if (isset($module_share) && is_array($module_share) && $module_share) { $key_t=-1;$count_t=dr_count($module_share);foreach ($module_share as $t) { $key_t++; $is_first=$key_t==0 ? 1 : 0;$is_last=$count_t==$key_t+1 ? 1 : 0; if ($t['share']) { ?>
                                    <option value="<?php echo $t['dirname']; ?>" <?php if ($t['dirname']==$data['mid']) { ?>selected<?php } ?>> <?php echo dr_lang($t['name']); ?> </option>
                                    <?php }  } } ?>
                                    </select>
                                </label>
                                <?php if ($ci->_is_admin_auth('module/module/index')) { ?>
                                <span class="help-block"><a href="<?php echo dr_url('module/module/index'); ?>"><?php echo dr_lang('在这里可以创建更多的共享模块'); ?></a></span>
                                <?php } ?>
                            </div>
                        </div>
                        <?php } ?>
                        <div class="form-group " id="dr_row_name">
                            <label class="col-md-2 control-label"><?php echo dr_lang('栏目名称'); ?></label>
                            <div class="col-md-9">
                                <label><input class="form-control input-large" type="text" name="system[name]" value="<?php echo htmlspecialchars((string)$data['name']); ?>" id="dr_name" onblur="d_topinyin('dirname','name', 1);"></label>
                                <span class="help-block" id="dr_name_tips"><?php echo dr_lang('栏目的一个显示名称'); ?></span>
                            </div>
                        </div>
                        <div class="form-group " id="dr_row_dirname">
                            <label class="col-md-2 control-label"><?php echo dr_lang('目录名称'); ?></label>
                            <div class="col-md-9">
                                <?php if ($pdir) { ?>
                                <div class="input-group">
                                    <span class="input-group-addon"><?php echo $pdir; ?></span>
                                    <input class="form-control input-medium" type="text" name="system[dirname]" id="dr_dirname" value="<?php echo htmlspecialchars((string)$data['dirname']); ?>">
                                </div>
                                <?php } else { ?>
                                <input class="form-control input-large" type="text" name="system[dirname]" id="dr_dirname" value="<?php echo htmlspecialchars((string)$data['dirname']); ?>">
                                <?php } ?>
                                <span class="help-block" id="dr_dirname_tips"><?php echo dr_lang('栏目目录确保唯一，用于url填充或者生成目录'); ?></span>
                            </div>
                        </div>
                        <div class="form-group dr_tid_2 " style="display: none">
                            <label class="col-md-2 control-label"><?php echo dr_lang('外链地址'); ?></label>
                            <div class="col-md-9">
                                <input class="form-control" type="text" name="system[setting][linkurl]" value="<?php echo htmlspecialchars((string)$data['setting']['linkurl']); ?>">
                                <span class="help-block"><?php echo dr_lang('可跳转到指定地址（不允许发布内容）'); ?></span>
                            </div>
                        </div>
                        <div class="form-group dr_tid_0 dr_tid_1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('继承下级'); ?></label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[setting][getchild]" value="1" <?php if ($data['setting']['getchild']) { ?>checked<?php } ?> /> <?php echo dr_lang('开启'); ?> <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[setting][getchild]" value="0" <?php if (empty($data['setting']['getchild'])) { ?>checked<?php } ?> /> <?php echo dr_lang('关闭'); ?> <span></span></label>
                                </div>
                                <span class="help-block"><?php echo dr_lang('将下级第一个栏目数据作为当前的栏目，不对外链类型的栏目有效'); ?></span>
                            </div>
                        </div>
                        <div class="form-group dr_tid_1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('禁止修改'); ?></label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[setting][notedit]" value="1" <?php if ($data['setting']['notedit']) { ?>checked<?php } ?> /> <?php echo dr_lang('开启'); ?> <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[setting][notedit]" value="0" <?php if (empty($data['setting']['notedit'])) { ?>checked<?php } ?> /> <?php echo dr_lang('关闭'); ?> <span></span></label>
                                </div>
                                <span class="help-block"><?php echo dr_lang('内容发布之后将禁止修改栏目'); ?></span>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="tab-pane <?php if ($page==3) { ?>active<?php } ?>" id="tab_3">
                    <div class="form-body">

                        <?php if ($module['share']) {  if (dr_is_app('chtml')) { ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('栏目生成静态'); ?></label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[setting][html]" value="1" <?php if ($data['setting']['html']) { ?>checked<?php } ?> /> <?php echo dr_lang('开启'); ?> <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[setting][html]" value="0" <?php if (empty($data['setting']['html'])) { ?>checked<?php } ?> /> <?php echo dr_lang('关闭'); ?> <span></span></label>
                                </div>
                                <span class="help-block"><?php echo dr_lang('开启生成静态时必须给它配置URL规则'); ?></span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('内容生成静态'); ?></label>
                            <div class="col-md-9">
                                <div class="mt-radio-inline">
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[setting][chtml]" value="1" <?php if ($data['setting']['chtml']) { ?>checked<?php } ?> /> <?php echo dr_lang('开启'); ?> <span></span></label>
                                    <label class="mt-radio mt-radio-outline"><input type="radio" name="system[setting][chtml]" value="0" <?php if (empty($data['setting']['chtml'])) { ?>checked<?php } ?> /> <?php echo dr_lang('关闭'); ?> <span></span></label>
                                </div>
                                <span class="help-block"><?php echo dr_lang('开启生成静态时必须给它配置URL规则'); ?></span>
                            </div>
                        </div>
                        <?php } ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('URL规则'); ?></label>
                            <div class="col-md-9">

                                <label>
                                    <select class="form-control" name="system[setting][urlrule]">
                                        <option value="0"> <?php echo dr_lang('动态地址'); ?> </option>
                                        <?php $return_u = [];$list_return_u = $this->list_tag("action=cache name=urlrule  return=u"); if ($list_return_u) { extract($list_return_u, EXTR_OVERWRITE); $count_u=dr_count($return_u);} if (is_array($return_u) && $return_u) { $key_u=-1;foreach ($return_u as $u) { $key_u++; $is_first=$key_u==0 ? 1 : 0;$is_last=$count_u==$key_u+1 ? 1 : 0;   if ($u['type']==3) { ?><option value="<?php echo $u['id']; ?>" <?php if ($u['id'] == $data['setting']['urlrule']) { ?>selected<?php } ?>> <?php echo $u['name']; ?> </option><?php }  } } ?>
                                    </select>
                                </label>
                                <label style="margin-left:20px;"><a href="<?php echo dr_url('module/urlrule/index', ['hide_menu' => 1]); ?>" style="color:blue !important"><?php echo dr_lang('[URL规则管理]'); ?></a> </label>

                            </div>
                        </div>
                        <?php } ?>

                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('SEO标题'); ?></label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="system[setting][seo][list_title]"><?php echo $data['setting']['seo']['list_title']; ?></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('SEO关键字'); ?></label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="system[setting][seo][list_keywords]"><?php echo $data['setting']['seo']['list_keywords']; ?></textarea>

                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('SEO描述信息'); ?></label>
                            <div class="col-md-9">
                                <textarea class="form-control " style="height:90px" name="system[setting][seo][list_description]"><?php echo $data['setting']['seo']['list_description']; ?></textarea>

                            </div>
                        </div>
                        <?php if ($id) { ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('同步到其他栏目'); ?></label>
                            <div class="col-md-9">
                                <label><button onclick="dr_iframe('<?php echo dr_lang('复制'); ?>', '<?php echo dr_url(APP_DIR.'/category/copy_edit'); ?>&at=seo&catid=<?php echo $id; ?>')" type="button" class="btn green btn-sm"> <i class="fa fa-copy"></i> <?php echo dr_lang('同步SEO配置'); ?></button></label>
                                <label><?php echo dr_lang('把本页的设置数据同步到其他栏目；需要保存之后再同步'); ?></label>
                            </div>
                        </div>
                        <?php } ?>

                    </div>
                </div>
                <div class="tab-pane <?php if ($page==4) { ?>active<?php } ?>" id="tab_4">
                    <div class="form-body">
<?php $catsync = \Phpcmf\Service::L('cache')->get('module-'.SITE_ID.'-'.$mid, 'setting', 'search', 'catsync');  if ($catsync) { ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('列表信息数'); ?></label>
                            <div class="col-md-9">
                                <label class="form-control-static">
                                    <span class="badge badge-warning badge-roundless"> <?php echo dr_lang('已开启搜索集成于栏目，此设置无效'); ?> </span>
                                </label>
                            </div>
                        </div>
                        <?php } else { ?>
                        <div class="form-group dr_tid_1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('电脑列表信息数'); ?></label>
                            <div class="col-md-9">
                                <label><input class="form-control" type="text" value="<?php echo intval($data['setting']['template']['pagesize']); ?>" name="system[setting][template][pagesize]"></label>
                                <span class="help-block"><?php echo dr_lang('列表页面每页显示的信息数量，静态生成时调用此参数'); ?></span>
                            </div>
                        </div>
                        <?php if (SITE_IS_MOBILE || $ci->site_info[SITE_ID]['SITE_AUTO']) { ?>
                        <div class="form-group dr_tid_1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('手机列表信息数'); ?></label>
                            <div class="col-md-9">
                                <label><input class="form-control " type="text" value="<?php echo intval($data['setting']['template']['mpagesize']); ?>" name="system[setting][template][mpagesize]"></label>
                                <span class="help-block"><?php echo dr_lang('列表页面每页显示的信息数量，静态生成时调用此参数'); ?></span>
                            </div>
                        </div>
                        <?php } else { ?>
                        <input type="hidden" value="<?php echo intval($data['setting']['template']['mpagesize']); ?>" name="system[setting][template][mpagesize]">
                        <?php }  }  if ($is_scategory) { ?>
                        <div class="form-group dr_tid_0">
                            <label class="col-md-2 control-label"><?php echo dr_lang('单网页模板'); ?></label>
                            <div class="col-md-9">

                                <div class="input-group  input-large">
                                    <input class="form-control dr_page_tpl" type="text" value="<?php echo htmlspecialchars((string)$data['setting']['template']['page']); ?>" name="system[setting][template][page]">
                                    <div class="input-group-btn">
                                        <button type="button" class="btn green dropdown-toggle" data-toggle="dropdown"><?php echo dr_lang('选择'); ?>
                                            <i class="fa fa-angle-down"></i>
                                        </button>
                                        <?php echo dr_rp($select_tpl, '{name}', 'dr_page_tpl'); ?>
                                    </div>
                                </div>
                                <span class="help-block"><?php echo dr_lang('单网页模板默认page.html'); ?></span>
                            </div>
                        </div>
                        <?php }  if ($catsync) { ?>

                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('内容列表页模板'); ?></label>
                            <div class="col-md-9">
                                <label class="form-control-static">
                                    <span class="badge badge-warning badge-roundless"> <?php echo dr_lang('已开启搜索集成于栏目，此模板无效'); ?> </span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('内容栏目封面模板'); ?></label>
                            <div class="col-md-9">
                                <label class="form-control-static">
                                    <span class="badge badge-warning badge-roundless"> <?php echo dr_lang('已开启搜索集成于栏目，此模板无效'); ?> </span>
                                </label>
                            </div>
                        </div>
                        <?php } else { ?>

                        <div class="form-group dr_tid_1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('内容列表页模板'); ?></label>
                            <div class="col-md-9">
                                <div class="input-group  input-large">
                                    <input class="form-control dr_list_tpl" type="text" value="<?php echo htmlspecialchars((string)$data['setting']['template']['list']); ?>" name="system[setting][template][list]">
                                    <div class="input-group-btn">
                                        <button type="button" class="btn green dropdown-toggle" data-toggle="dropdown"><?php echo dr_lang('选择'); ?>
                                            <i class="fa fa-angle-down"></i>
                                        </button>
                                        <?php echo dr_rp($select_tpl, '{name}', 'dr_list_tpl'); ?>
                                    </div>
                                </div>
                                <span class="help-block"><?php echo dr_lang('模块栏目列表默认list.html'); ?></span>
                            </div>
                        </div>
                        <div class="form-group dr_tid_1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('内容栏目封面模板'); ?></label>
                            <div class="col-md-9">

                                <div class="input-group  input-large">
                                    <input class="form-control dr_category_tpl" type="text" value="<?php echo htmlspecialchars((string)$data['setting']['template']['category']); ?>" name="system[setting][template][category]">
                                    <div class="input-group-btn">
                                        <button type="button" class="btn green dropdown-toggle" data-toggle="dropdown"><?php echo dr_lang('选择'); ?>
                                            <i class="fa fa-angle-down"></i>
                                        </button>
                                        <?php echo dr_rp($select_tpl, '{name}', 'dr_category_tpl'); ?>
                                    </div>
                                </div>
                                <span class="help-block"><?php echo dr_lang('默认category.html'); ?></span>
                            </div>
                        </div>
                        <?php } ?>
                        <div class="form-group dr_tid_1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('内容搜索页模板'); ?></label>
                            <div class="col-md-9">
                                <div class="input-group  input-large">
                                    <input class="form-control dr_search_tpl" type="text" value="<?php echo $data['setting']['template']['search'] ? htmlspecialchars($data['setting']['template']['search']) : 'search.html' ?>" name="system[setting][template][search]">
                                    <div class="input-group-btn">
                                        <button type="button" class="btn green dropdown-toggle" data-toggle="dropdown"><?php echo dr_lang('选择'); ?>
                                            <i class="fa fa-angle-down"></i>
                                        </button>
                                        <?php echo dr_rp($select_tpl, '{name}', 'dr_search_tpl'); ?>
                                    </div>
                                </div>
                                    <span class="help-block"><?php echo dr_lang('默认search.html'); ?></span>
                            </div>
                        </div>
                        <div class="form-group dr_tid_1">
                            <label class="col-md-2 control-label"><?php echo dr_lang('内容详细页模板'); ?></label>
                            <div class="col-md-9">

                                <div class="input-group  input-large">
                                    <input class="form-control dr_show_tpl" type="text" value="<?php echo htmlspecialchars((string)$data['setting']['template']['show']); ?>" name="system[setting][template][show]">
                                    <div class="input-group-btn">
                                        <button type="button" class="btn green dropdown-toggle" data-toggle="dropdown"><?php echo dr_lang('选择'); ?>
                                            <i class="fa fa-angle-down"></i>
                                        </button>
                                        <?php echo dr_rp($select_tpl, '{name}', 'dr_show_tpl'); ?>
                                    </div>
                                </div>
                                    <span class="help-block"><?php echo dr_lang('默认show.html'); ?></span>
                            </div>
                        </div>
                        <?php if ($id) { ?>
                        <div class="form-group">
                            <label class="col-md-2 control-label"><?php echo dr_lang('同步到其他栏目'); ?></label>
                            <div class="col-md-9">
                                <label><button onclick="dr_iframe('<?php echo dr_lang('复制'); ?>', '<?php echo dr_url(APP_DIR.'/category/copy_edit'); ?>&at=tpl&catid=<?php echo $id; ?>')" type="button" class="btn green btn-sm"> <i class="fa fa-copy"></i> <?php echo dr_lang('同步模板配置'); ?></button></label>
                                <label><?php echo dr_lang('把本页的设置数据同步到其他栏目；需要保存之后再同步'); ?></label>
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                </div>

                <div class="tab-pane <?php if ($page==1) { ?>active<?php } ?>" id="tab_1">
                    <div class="form-body">
                        <?php echo $sysfield;  echo $myfield;  echo $diyfield; ?>
                    </div>
                </div>

            </div>
        </div>
    </div>
    </div>
    <?php if (!isset($_GET['isedit'])) { ?>
    <div class="portlet-body form myfooter">
        <div class="form-actions text-center">
            <label><button type="button" onclick="dr_ajax_submit('<?php echo dr_now_url(); ?>&is_self=1&page='+$('#dr_page').val(), 'myform', '2000', '<?php echo dr_now_url(); ?>&is_self=1&page='+$('#dr_page').val())" class="btn blue"> <i class="fa fa-save"></i> <?php echo dr_lang('保存内容'); ?></button></label>

            <label><button type="button" onclick="dr_ajax_submit('<?php echo dr_now_url(); ?>', 'myform', '2000', '<?php echo dr_url(APP_DIR.'/category/add'); ?>')" class="btn green"> <i class="fa fa-plus"></i> <?php echo dr_lang('保存再添加'); ?></button></label>
            <label><button type="button" onclick="dr_ajax_submit('<?php echo dr_now_url(); ?>', 'myform', '2000', '<?php echo dr_url(APP_DIR.'/category/index'); ?>')" class="btn yellow"> <i class="fa fa-mail-reply-all"></i> <?php echo dr_lang('保存并返回'); ?></button></label>

            <?php if ($id && (isset($admin['role'][1]) && dr_count($ci->init['field']))) { ?>
            <label><button type="button" onclick="dr_cat_field('<?php echo $id; ?>', '<?php echo $name; ?>');" class="btn red"> <i class="fa fa-code"></i> <?php echo dr_lang('字段权限'); ?></button></label>
            <?php }  if ($web_url) { ?>
            <label><a href="<?php echo $web_url; ?>" target="_blank" class="btn dark"> <i class="fa fa-link"></i> <?php echo dr_lang('浏览'); ?></a></label>
            <?php } ?>
        </div>
    </div>
   <?php } ?>
</form>

<?php if ($fn_include = $this->_include("footer.html")) include($fn_include); ?>